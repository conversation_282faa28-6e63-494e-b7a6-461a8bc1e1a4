import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../domain/usecases/dashboard/get_dashboard_stats_usecase.dart';
import 'dashboard_event.dart';
import 'dashboard_state.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final GetDashboardStatsUseCase _getDashboardStatsUseCase;
  Timer? _autoRefreshTimer;

  DashboardBloc({
    required GetDashboardStatsUseCase getDashboardStatsUseCase,
  })  : _getDashboardStatsUseCase = getDashboardStatsUseCase,
        super(const DashboardInitial()) {
    on<DashboardStatsLoadRequested>(_onDashboardStatsLoadRequested);
    on<DashboardStatsRefreshRequested>(_onDashboardStatsRefreshRequested);
    on<DashboardStatsLoadForDateRangeRequested>(_onDashboardStatsLoadForDateRangeRequested);
    on<DashboardStatsLoadForRoleRequested>(_onDashboardStatsLoadForRoleRequested);
    on<DashboardClearError>(_onDashboardClearError);
    on<DashboardFilterByDepartmentRequested>(_onDashboardFilterByDepartmentRequested);
    on<DashboardFilterByStatusRequested>(_onDashboardFilterByStatusRequested);
    on<DashboardRecentActivitiesLoadRequested>(_onDashboardRecentActivitiesLoadRequested);
    on<DashboardPendingApprovalsLoadRequested>(_onDashboardPendingApprovalsLoadRequested);
    on<DashboardOverdueJobsLoadRequested>(_onDashboardOverdueJobsLoadRequested);
    on<DashboardQualityControlAlertsLoadRequested>(_onDashboardQualityControlAlertsLoadRequested);
    on<DashboardAutoRefreshToggled>(_onDashboardAutoRefreshToggled);
  }

  @override
  Future<void> close() {
    _autoRefreshTimer?.cancel();
    return super.close();
  }

  Future<void> _onDashboardStatsLoadRequested(
    DashboardStatsLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardLoading());

    final result = await _getDashboardStatsUseCase(const NoParams());

    result.fold(
      (failure) => emit(DashboardError(failure: failure)),
      (stats) => emit(DashboardStatsLoaded(
        stats: stats,
        lastUpdated: DateTime.now(),
      )),
    );
  }

  Future<void> _onDashboardStatsRefreshRequested(
    DashboardStatsRefreshRequested event,
    Emitter<DashboardState> emit,
  ) async {
    if (state is DashboardStatsLoaded) {
      final currentState = state as DashboardStatsLoaded;
      emit(DashboardRefreshing(currentStats: currentState.stats));
    } else {
      emit(const DashboardLoading());
    }

    final result = await _getDashboardStatsUseCase(const NoParams());

    result.fold(
      (failure) => emit(DashboardError(failure: failure)),
      (stats) {
        if (state is DashboardStatsLoaded) {
          final currentState = state as DashboardStatsLoaded;
          emit(currentState.copyWith(
            stats: stats,
            lastUpdated: DateTime.now(),
          ));
        } else {
          emit(DashboardStatsLoaded(
            stats: stats,
            lastUpdated: DateTime.now(),
          ));
        }
      },
    );
  }

  Future<void> _onDashboardStatsLoadForDateRangeRequested(
    DashboardStatsLoadForDateRangeRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardLoading());

    // In a real implementation, you'd pass date range parameters to the use case
    final result = await _getDashboardStatsUseCase(const NoParams());

    result.fold(
      (failure) => emit(DashboardError(failure: failure)),
      (stats) => emit(DashboardStatsLoaded(
        stats: stats,
        startDate: event.startDate,
        endDate: event.endDate,
        lastUpdated: DateTime.now(),
      )),
    );
  }

  Future<void> _onDashboardStatsLoadForRoleRequested(
    DashboardStatsLoadForRoleRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardLoading());

    // In a real implementation, you'd pass role parameters to the use case
    final result = await _getDashboardStatsUseCase(const NoParams());

    result.fold(
      (failure) => emit(DashboardError(failure: failure)),
      (stats) => emit(DashboardStatsLoaded(
        stats: stats,
        userRole: event.userRole,
        lastUpdated: DateTime.now(),
      )),
    );
  }

  void _onDashboardClearError(
    DashboardClearError event,
    Emitter<DashboardState> emit,
  ) {
    if (state is DashboardError) {
      emit(const DashboardInitial());
    }
  }

  Future<void> _onDashboardFilterByDepartmentRequested(
    DashboardFilterByDepartmentRequested event,
    Emitter<DashboardState> emit,
  ) async {
    if (state is DashboardStatsLoaded) {
      final currentState = state as DashboardStatsLoaded;
      
      emit(DashboardOperationLoading(
        operation: 'filtering_by_department',
        currentStats: currentState.stats,
      ));

      // In a real implementation, you'd reload data with department filter
      final result = await _getDashboardStatsUseCase(const NoParams());

      result.fold(
        (failure) => emit(DashboardError(failure: failure)),
        (stats) => emit(currentState.copyWith(
          stats: stats,
          departmentFilter: event.department,
          lastUpdated: DateTime.now(),
        )),
      );
    }
  }

  Future<void> _onDashboardFilterByStatusRequested(
    DashboardFilterByStatusRequested event,
    Emitter<DashboardState> emit,
  ) async {
    if (state is DashboardStatsLoaded) {
      final currentState = state as DashboardStatsLoaded;
      
      emit(DashboardOperationLoading(
        operation: 'filtering_by_status',
        currentStats: currentState.stats,
      ));

      // In a real implementation, you'd reload data with status filter
      final result = await _getDashboardStatsUseCase(const NoParams());

      result.fold(
        (failure) => emit(DashboardError(failure: failure)),
        (stats) => emit(currentState.copyWith(
          stats: stats,
          statusFilter: event.status,
          lastUpdated: DateTime.now(),
        )),
      );
    }
  }

  Future<void> _onDashboardRecentActivitiesLoadRequested(
    DashboardRecentActivitiesLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentStats = state is DashboardStatsLoaded 
        ? (state as DashboardStatsLoaded).stats 
        : null;

    emit(DashboardOperationLoading(
      operation: 'loading_recent_activities',
      currentStats: currentStats,
    ));

    // Simulate loading recent activities
    // In a real implementation, you'd have a dedicated use case
    await Future.delayed(const Duration(milliseconds: 500));

    emit(DashboardRecentActivitiesLoaded(
      activities: [], // Would be actual activity entities
      currentStats: currentStats,
    ));
  }

  Future<void> _onDashboardPendingApprovalsLoadRequested(
    DashboardPendingApprovalsLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentStats = state is DashboardStatsLoaded 
        ? (state as DashboardStatsLoaded).stats 
        : null;

    emit(DashboardOperationLoading(
      operation: 'loading_pending_approvals',
      currentStats: currentStats,
    ));

    // Simulate loading pending approvals count
    await Future.delayed(const Duration(milliseconds: 500));

    emit(DashboardPendingApprovalsLoaded(
      pendingCount: 5, // Would be actual count from API
      currentStats: currentStats,
    ));
  }

  Future<void> _onDashboardOverdueJobsLoadRequested(
    DashboardOverdueJobsLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentStats = state is DashboardStatsLoaded 
        ? (state as DashboardStatsLoaded).stats 
        : null;

    emit(DashboardOperationLoading(
      operation: 'loading_overdue_jobs',
      currentStats: currentStats,
    ));

    // Simulate loading overdue jobs count
    await Future.delayed(const Duration(milliseconds: 500));

    emit(DashboardOverdueJobsLoaded(
      overdueCount: 3, // Would be actual count from API
      currentStats: currentStats,
    ));
  }

  Future<void> _onDashboardQualityControlAlertsLoadRequested(
    DashboardQualityControlAlertsLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentStats = state is DashboardStatsLoaded 
        ? (state as DashboardStatsLoaded).stats 
        : null;

    emit(DashboardOperationLoading(
      operation: 'loading_quality_control_alerts',
      currentStats: currentStats,
    ));

    // Simulate loading quality control alerts
    await Future.delayed(const Duration(milliseconds: 500));

    emit(DashboardQualityControlAlertsLoaded(
      alerts: [], // Would be actual alert entities
      currentStats: currentStats,
    ));
  }

  void _onDashboardAutoRefreshToggled(
    DashboardAutoRefreshToggled event,
    Emitter<DashboardState> emit,
  ) {
    if (state is DashboardStatsLoaded) {
      final currentState = state as DashboardStatsLoaded;
      
      if (event.enabled) {
        _startAutoRefresh();
      } else {
        _stopAutoRefresh();
      }

      emit(currentState.copyWith(autoRefreshEnabled: event.enabled));
    }
  }

  void _startAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = Timer.periodic(
      const Duration(minutes: 5), // Refresh every 5 minutes
      (_) => add(const DashboardStatsRefreshRequested()),
    );
  }

  void _stopAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = null;
  }
}
