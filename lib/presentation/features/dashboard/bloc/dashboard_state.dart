import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/dashboard/dashboard_stats_entity.dart';

abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

class DashboardInitial extends DashboardState {
  const DashboardInitial();
}

class DashboardLoading extends DashboardState {
  const DashboardLoading();
}

class DashboardStatsLoaded extends DashboardState {
  final DashboardStatsEntity stats;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? userRole;
  final String? departmentFilter;
  final String? statusFilter;
  final bool autoRefreshEnabled;
  final DateTime lastUpdated;

  const DashboardStatsLoaded({
    required this.stats,
    this.startDate,
    this.endDate,
    this.userRole,
    this.departmentFilter,
    this.statusFilter,
    this.autoRefreshEnabled = false,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
        stats,
        startDate,
        endDate,
        userRole,
        departmentFilter,
        statusFilter,
        autoRefreshEnabled,
        lastUpdated,
      ];

  DashboardStatsLoaded copyWith({
    DashboardStatsEntity? stats,
    DateTime? startDate,
    DateTime? endDate,
    String? userRole,
    String? departmentFilter,
    String? statusFilter,
    bool? autoRefreshEnabled,
    DateTime? lastUpdated,
  }) {
    return DashboardStatsLoaded(
      stats: stats ?? this.stats,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      userRole: userRole ?? this.userRole,
      departmentFilter: departmentFilter ?? this.departmentFilter,
      statusFilter: statusFilter ?? this.statusFilter,
      autoRefreshEnabled: autoRefreshEnabled ?? this.autoRefreshEnabled,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class DashboardRefreshing extends DashboardState {
  final DashboardStatsEntity currentStats;

  const DashboardRefreshing({required this.currentStats});

  @override
  List<Object> get props => [currentStats];
}

class DashboardError extends DashboardState {
  final Failure failure;

  const DashboardError({required this.failure});

  @override
  List<Object> get props => [failure];
}

class DashboardOperationLoading extends DashboardState {
  final String operation;
  final DashboardStatsEntity? currentStats;

  const DashboardOperationLoading({
    required this.operation,
    this.currentStats,
  });

  @override
  List<Object?> get props => [operation, currentStats];
}

// Specific states for different dashboard components
class DashboardRecentActivitiesLoaded extends DashboardState {
  final List<dynamic> activities; // Would be proper activity entities
  final DashboardStatsEntity? currentStats;

  const DashboardRecentActivitiesLoaded({
    required this.activities,
    this.currentStats,
  });

  @override
  List<Object?> get props => [activities, currentStats];
}

class DashboardPendingApprovalsLoaded extends DashboardState {
  final int pendingCount;
  final DashboardStatsEntity? currentStats;

  const DashboardPendingApprovalsLoaded({
    required this.pendingCount,
    this.currentStats,
  });

  @override
  List<Object?> get props => [pendingCount, currentStats];
}

class DashboardOverdueJobsLoaded extends DashboardState {
  final int overdueCount;
  final DashboardStatsEntity? currentStats;

  const DashboardOverdueJobsLoaded({
    required this.overdueCount,
    this.currentStats,
  });

  @override
  List<Object?> get props => [overdueCount, currentStats];
}

class DashboardQualityControlAlertsLoaded extends DashboardState {
  final List<dynamic> alerts; // Would be proper alert entities
  final DashboardStatsEntity? currentStats;

  const DashboardQualityControlAlertsLoaded({
    required this.alerts,
    this.currentStats,
  });

  @override
  List<Object?> get props => [alerts, currentStats];
}
