import 'package:equatable/equatable.dart';

abstract class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object?> get props => [];
}

// Load dashboard statistics
class DashboardStatsLoadRequested extends DashboardEvent {
  const DashboardStatsLoadRequested();
}

// Refresh dashboard statistics
class DashboardStatsRefreshRequested extends DashboardEvent {
  const DashboardStatsRefreshRequested();
}

// Load dashboard data for specific date range
class DashboardStatsLoadForDateRangeRequested extends DashboardEvent {
  final DateTime startDate;
  final DateTime endDate;

  const DashboardStatsLoadForDateRangeRequested({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object> get props => [startDate, endDate];
}

// Load dashboard data for specific user role
class DashboardStatsLoadForRoleRequested extends DashboardEvent {
  final String userRole;

  const DashboardStatsLoadForRoleRequested({required this.userRole});

  @override
  List<Object> get props => [userRole];
}

// Clear dashboard errors
class DashboardClearError extends DashboardEvent {
  const DashboardClearError();
}

// Filter dashboard by department
class DashboardFilterByDepartmentRequested extends DashboardEvent {
  final String? department;

  const DashboardFilterByDepartmentRequested({this.department});

  @override
  List<Object?> get props => [department];
}

// Filter dashboard by status
class DashboardFilterByStatusRequested extends DashboardEvent {
  final String? status;

  const DashboardFilterByStatusRequested({this.status});

  @override
  List<Object?> get props => [status];
}

// Load recent activities
class DashboardRecentActivitiesLoadRequested extends DashboardEvent {
  final int limit;

  const DashboardRecentActivitiesLoadRequested({this.limit = 10});

  @override
  List<Object> get props => [limit];
}

// Load pending approvals count
class DashboardPendingApprovalsLoadRequested extends DashboardEvent {
  const DashboardPendingApprovalsLoadRequested();
}

// Load overdue jobs count
class DashboardOverdueJobsLoadRequested extends DashboardEvent {
  const DashboardOverdueJobsLoadRequested();
}

// Load quality control alerts
class DashboardQualityControlAlertsLoadRequested extends DashboardEvent {
  const DashboardQualityControlAlertsLoadRequested();
}

// Auto-refresh dashboard (for real-time updates)
class DashboardAutoRefreshToggled extends DashboardEvent {
  final bool enabled;

  const DashboardAutoRefreshToggled({required this.enabled});

  @override
  List<Object> get props => [enabled];
}
