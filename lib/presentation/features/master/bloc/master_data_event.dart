import 'package:equatable/equatable.dart';
import '../../../../domain/entities/master/customer_entity.dart';

abstract class MasterDataEvent extends Equatable {
  const MasterDataEvent();

  @override
  List<Object?> get props => [];
}

// Parameters events
class ParametersLoadRequested extends MasterDataEvent {
  const ParametersLoadRequested();
}

class ParametersRefreshRequested extends MasterDataEvent {
  const ParametersRefreshRequested();
}

// Customers events
class CustomersLoadRequested extends MasterDataEvent {
  final String? search;

  const CustomersLoadRequested({this.search});

  @override
  List<Object?> get props => [search];
}

class CustomersRefreshRequested extends MasterDataEvent {
  final String? search;

  const CustomersRefreshRequested({this.search});

  @override
  List<Object?> get props => [search];
}

class CustomerAddRequested extends MasterDataEvent {
  final CustomerEntity customer;

  const CustomerAddRequested({required this.customer});

  @override
  List<Object> get props => [customer];
}

class CustomerSearchRequested extends MasterDataEvent {
  final String query;

  const CustomerSearchRequested({required this.query});

  @override
  List<Object> get props => [query];
}

class CustomerSearchCleared extends MasterDataEvent {
  const CustomerSearchCleared();
}

// Clear errors
class MasterDataClearError extends MasterDataEvent {
  const MasterDataClearError();
}

// Load all master data
class AllMasterDataLoadRequested extends MasterDataEvent {
  const AllMasterDataLoadRequested();
}

// Refresh all master data
class AllMasterDataRefreshRequested extends MasterDataEvent {
  const AllMasterDataRefreshRequested();
}

// Filter parameters by type
class ParametersFilterByTypeRequested extends MasterDataEvent {
  final String? type;

  const ParametersFilterByTypeRequested({this.type});

  @override
  List<Object?> get props => [type];
}

// Filter parameters by status
class ParametersFilterByStatusRequested extends MasterDataEvent {
  final int? status;

  const ParametersFilterByStatusRequested({this.status});

  @override
  List<Object?> get props => [status];
}

// Search parameters
class ParametersSearchRequested extends MasterDataEvent {
  final String query;

  const ParametersSearchRequested({required this.query});

  @override
  List<Object> get props => [query];
}

class ParametersSearchCleared extends MasterDataEvent {
  const ParametersSearchCleared();
}
