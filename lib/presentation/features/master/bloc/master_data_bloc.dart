import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lims_app_flutter/domain/entities/master/customer_entity.dart';
import 'package:lims_app_flutter/domain/entities/master/parameter_entity.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../domain/usecases/master/get_parameters_usecase.dart';
import '../../../../domain/usecases/master/get_customers_usecase.dart';
import '../../../../domain/usecases/master/add_customer_usecase.dart';
import 'master_data_event.dart';
import 'master_data_state.dart';

class MasterDataBloc extends Bloc<MasterDataEvent, MasterDataState> {
  final GetParametersUseCase _getParametersUseCase;
  final GetCustomersUseCase _getCustomersUseCase;
  final AddCustomerUseCase _addCustomerUseCase;

  MasterDataBloc({
    required GetParametersUseCase getParametersUseCase,
    required GetCustomersUseCase getCustomersUseCase,
    required AddCustomerUseCase addCustomerUseCase,
  })  : _getParametersUseCase = getParametersUseCase,
        _getCustomersUseCase = getCustomersUseCase,
        _addCustomerUseCase = addCustomerUseCase,
        super(const MasterDataInitial()) {
    on<ParametersLoadRequested>(_onParametersLoadRequested);
    on<ParametersRefreshRequested>(_onParametersRefreshRequested);
    on<CustomersLoadRequested>(_onCustomersLoadRequested);
    on<CustomersRefreshRequested>(_onCustomersRefreshRequested);
    on<CustomerAddRequested>(_onCustomerAddRequested);
    on<CustomerSearchRequested>(_onCustomerSearchRequested);
    on<CustomerSearchCleared>(_onCustomerSearchCleared);
    on<MasterDataClearError>(_onMasterDataClearError);
    on<AllMasterDataLoadRequested>(_onAllMasterDataLoadRequested);
    on<AllMasterDataRefreshRequested>(_onAllMasterDataRefreshRequested);
    on<ParametersFilterByTypeRequested>(_onParametersFilterByTypeRequested);
    on<ParametersFilterByStatusRequested>(_onParametersFilterByStatusRequested);
    on<ParametersSearchRequested>(_onParametersSearchRequested);
    on<ParametersSearchCleared>(_onParametersSearchCleared);
  }

  Future<void> _onParametersLoadRequested(
    ParametersLoadRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    emit(const MasterDataLoading());

    final result = await _getParametersUseCase(const NoParams());

    result.fold(
      (failure) => emit(MasterDataError(failure: failure)),
      (parameters) => emit(ParametersLoaded(parameters: parameters)),
    );
  }

  Future<void> _onParametersRefreshRequested(
    ParametersRefreshRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    if (state is ParametersLoaded) {
      emit(const MasterDataRefreshing(operation: 'refreshing_parameters'));
    } else {
      emit(const MasterDataLoading());
    }

    final result = await _getParametersUseCase(const NoParams());

    result.fold(
      (failure) => emit(MasterDataError(failure: failure)),
      (parameters) {
        if (state is ParametersLoaded) {
          final currentState = state as ParametersLoaded;
          emit(currentState.copyWith(parameters: parameters));
        } else {
          emit(ParametersLoaded(parameters: parameters));
        }
      },
    );
  }

  Future<void> _onCustomersLoadRequested(
    CustomersLoadRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    emit(const MasterDataLoading());

    final result = await _getCustomersUseCase(
      GetCustomersParams(search: event.search),
    );

    result.fold(
      (failure) => emit(MasterDataError(failure: failure)),
      (customers) => emit(CustomersLoaded(
        customers: customers,
        searchQuery: event.search,
      )),
    );
  }

  Future<void> _onCustomersRefreshRequested(
    CustomersRefreshRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    if (state is CustomersLoaded) {
      emit(const MasterDataRefreshing(operation: 'refreshing_customers'));
    } else {
      emit(const MasterDataLoading());
    }

    final result = await _getCustomersUseCase(
      GetCustomersParams(search: event.search),
    );

    result.fold(
      (failure) => emit(MasterDataError(failure: failure)),
      (customers) {
        if (state is CustomersLoaded) {
          final currentState = state as CustomersLoaded;
          emit(currentState.copyWith(
            customers: customers,
            searchQuery: event.search,
          ));
        } else {
          emit(CustomersLoaded(
            customers: customers,
            searchQuery: event.search,
          ));
        }
      },
    );
  }

  Future<void> _onCustomerAddRequested(
    CustomerAddRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    emit(const MasterDataOperationLoading(operation: 'adding_customer'));

    final result = await _addCustomerUseCase(
      AddCustomerParams(customer: event.customer),
    );

    result.fold(
      (failure) => emit(MasterDataError(failure: failure)),
      (customer) => emit(CustomerAdded(customer: customer)),
    );
  }

  Future<void> _onCustomerSearchRequested(
    CustomerSearchRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    emit(const MasterDataLoading());

    final result = await _getCustomersUseCase(
      GetCustomersParams(search: event.query),
    );

    result.fold(
      (failure) => emit(MasterDataError(failure: failure)),
      (customers) => emit(CustomersLoaded(
        customers: customers,
        searchQuery: event.query,
      )),
    );
  }

  Future<void> _onCustomerSearchCleared(
    CustomerSearchCleared event,
    Emitter<MasterDataState> emit,
  ) async {
    emit(const MasterDataLoading());

    final result = await _getCustomersUseCase(
      const GetCustomersParams(),
    );

    result.fold(
      (failure) => emit(MasterDataError(failure: failure)),
      (customers) => emit(CustomersLoaded(
        customers: customers,
        searchQuery: null,
      )),
    );
  }

  void _onMasterDataClearError(
    MasterDataClearError event,
    Emitter<MasterDataState> emit,
  ) {
    if (state is MasterDataError) {
      emit(const MasterDataInitial());
    }
  }

  Future<void> _onAllMasterDataLoadRequested(
    AllMasterDataLoadRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    emit(const MasterDataLoading());

    // Load both parameters and customers concurrently
    final results = await Future.wait([
      _getParametersUseCase(const NoParams()),
      _getCustomersUseCase(const GetCustomersParams()),
    ]);

    final parametersResult = results[0];
    final customersResult = results[1];

    // Check if both succeeded
    if (parametersResult.isRight() && customersResult.isRight()) {
      final parameters = parametersResult.getOrElse(() => []);
      final customers = customersResult.getOrElse(() => []);

      emit(AllMasterDataLoaded(
        parameters: parameters as List<ParameterEntity>,
        customers: customers as List<CustomerEntity>,
        lastUpdated: DateTime.now(),
      ));
    } else {
      // If either failed, emit the first failure
      final failure = parametersResult.isLeft() 
          ? parametersResult.fold((l) => l, (r) => null)
          : customersResult.fold((l) => l, (r) => null);
      
      emit(MasterDataError(failure: failure!));
    }
  }

  Future<void> _onAllMasterDataRefreshRequested(
    AllMasterDataRefreshRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    if (state is AllMasterDataLoaded) {
      emit(const MasterDataRefreshing(operation: 'refreshing_all_master_data'));
    } else {
      emit(const MasterDataLoading());
    }

    // Load both parameters and customers concurrently
    final results = await Future.wait([
      _getParametersUseCase(const NoParams()),
      _getCustomersUseCase(const GetCustomersParams()),
    ]);

    final parametersResult = results[0];
    final customersResult = results[1];

    // Check if both succeeded
    if (parametersResult.isRight() && customersResult.isRight()) {
      final parameters = parametersResult.getOrElse(() => []);
      final customers = customersResult.getOrElse(() => []);

      emit(AllMasterDataLoaded(
        parameters: parameters as List<ParameterEntity>,
        customers: customers as List<CustomerEntity>,
        lastUpdated: DateTime.now(),
      ));
    } else {
      // If either failed, emit the first failure
      final failure = parametersResult.isLeft() 
          ? parametersResult.fold((l) => l, (r) => null)
          : customersResult.fold((l) => l, (r) => null);
      
      emit(MasterDataError(failure: failure!));
    }
  }

  Future<void> _onParametersFilterByTypeRequested(
    ParametersFilterByTypeRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    if (state is ParametersLoaded) {
      final currentState = state as ParametersLoaded;
      
      // In a real implementation, you'd reload with filter parameters
      // For now, we'll just update the filter state
      emit(currentState.copyWith(typeFilter: event.type));
      
      // Reload parameters with filter
      final result = await _getParametersUseCase(const NoParams());
      
      result.fold(
        (failure) => emit(MasterDataError(failure: failure)),
        (parameters) => emit(currentState.copyWith(
          parameters: parameters,
          typeFilter: event.type,
        )),
      );
    }
  }

  Future<void> _onParametersFilterByStatusRequested(
    ParametersFilterByStatusRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    if (state is ParametersLoaded) {
      final currentState = state as ParametersLoaded;
      
      // In a real implementation, you'd reload with filter parameters
      final result = await _getParametersUseCase(const NoParams());
      
      result.fold(
        (failure) => emit(MasterDataError(failure: failure)),
        (parameters) => emit(currentState.copyWith(
          parameters: parameters,
          statusFilter: event.status,
        )),
      );
    }
  }

  Future<void> _onParametersSearchRequested(
    ParametersSearchRequested event,
    Emitter<MasterDataState> emit,
  ) async {
    emit(const MasterDataLoading());

    // In a real implementation, you'd pass search query to the use case
    final result = await _getParametersUseCase(const NoParams());

    result.fold(
      (failure) => emit(MasterDataError(failure: failure)),
      (parameters) => emit(ParametersLoaded(
        parameters: parameters,
        searchQuery: event.query,
      )),
    );
  }

  Future<void> _onParametersSearchCleared(
    ParametersSearchCleared event,
    Emitter<MasterDataState> emit,
  ) async {
    emit(const MasterDataLoading());

    final result = await _getParametersUseCase(const NoParams());

    result.fold(
      (failure) => emit(MasterDataError(failure: failure)),
      (parameters) => emit(ParametersLoaded(
        parameters: parameters,
        searchQuery: null,
      )),
    );
  }
}
