import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/master/parameter_entity.dart';
import '../../../../domain/entities/master/customer_entity.dart';

abstract class MasterDataState extends Equatable {
  const MasterDataState();

  @override
  List<Object?> get props => [];
}

class MasterDataInitial extends MasterDataState {
  const MasterDataInitial();
}

class MasterDataLoading extends MasterDataState {
  const MasterDataLoading();
}

// Parameters states
class ParametersLoaded extends MasterDataState {
  final List<ParameterEntity> parameters;
  final String? typeFilter;
  final int? statusFilter;
  final String? searchQuery;

  const ParametersLoaded({
    required this.parameters,
    this.typeFilter,
    this.statusFilter,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [parameters, typeFilter, statusFilter, searchQuery];

  ParametersLoaded copyWith({
    List<ParameterEntity>? parameters,
    String? typeFilter,
    int? statusFilter,
    String? searchQuery,
  }) {
    return ParametersLoaded(
      parameters: parameters ?? this.parameters,
      typeFilter: typeFilter ?? this.typeFilter,
      statusFilter: statusFilter ?? this.statusFilter,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

// Customers states
class CustomersLoaded extends MasterDataState {
  final List<CustomerEntity> customers;
  final String? searchQuery;

  const CustomersLoaded({
    required this.customers,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [customers, searchQuery];

  CustomersLoaded copyWith({
    List<CustomerEntity>? customers,
    String? searchQuery,
  }) {
    return CustomersLoaded(
      customers: customers ?? this.customers,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class CustomerAdded extends MasterDataState {
  final CustomerEntity customer;

  const CustomerAdded({required this.customer});

  @override
  List<Object> get props => [customer];
}

// Combined state for all master data
class AllMasterDataLoaded extends MasterDataState {
  final List<ParameterEntity> parameters;
  final List<CustomerEntity> customers;
  final DateTime lastUpdated;

  const AllMasterDataLoaded({
    required this.parameters,
    required this.customers,
    required this.lastUpdated,
  });

  @override
  List<Object> get props => [parameters, customers, lastUpdated];

  AllMasterDataLoaded copyWith({
    List<ParameterEntity>? parameters,
    List<CustomerEntity>? customers,
    DateTime? lastUpdated,
  }) {
    return AllMasterDataLoaded(
      parameters: parameters ?? this.parameters,
      customers: customers ?? this.customers,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class MasterDataRefreshing extends MasterDataState {
  final String operation;

  const MasterDataRefreshing({required this.operation});

  @override
  List<Object> get props => [operation];
}

class MasterDataError extends MasterDataState {
  final Failure failure;

  const MasterDataError({required this.failure});

  @override
  List<Object> get props => [failure];
}

class MasterDataOperationLoading extends MasterDataState {
  final String operation;

  const MasterDataOperationLoading({required this.operation});

  @override
  List<Object> get props => [operation];
}
