import 'package:equatable/equatable.dart';

abstract class Auth<PERSON>vent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class AuthLoginRequested extends AuthEvent {
  final String username;
  final String password;

  const AuthLoginRequested({
    required this.username,
    required this.password,
  });

  @override
  List<Object> get props => [username, password];
}

class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested();
}

class AuthGetProfileRequested extends AuthEvent {
  const AuthGetProfileRequested();
}

class AuthUpdateProfileRequested extends AuthEvent {
  final String name;

  const AuthUpdateProfileRequested({
    required this.name,
  });

  @override
  List<Object> get props => [name];
}

class AuthCheckStatusRequested extends AuthEvent {
  const AuthCheckStatusRequested();
}

class AuthClearError extends AuthEvent {
  const AuthClearError();
}
