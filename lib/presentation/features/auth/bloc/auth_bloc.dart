import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/api_constants.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../domain/usecases/auth/login_usecase.dart';
import '../../../../domain/usecases/auth/logout_usecase.dart';
import '../../../../domain/usecases/auth/get_profile_usecase.dart';
import '../../../../domain/usecases/auth/update_profile_usecase.dart';
import '../../../../domain/usecases/auth/login_and_get_profile_usecase.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase _loginUseCase;
  final LogoutUseCase _logoutUseCase;
  final GetProfileUseCase _getProfileUseCase;
  final UpdateProfileUseCase _updateProfileUseCase;
  final LoginAndGetProfileUseCase _loginAndGetProfileUseCase;
  final SharedPreferences _sharedPreferences;

  AuthBloc({
    required LoginUseCase loginUseCase,
    required LogoutUseCase logoutUseCase,
    required GetProfileUseCase getProfileUseCase,
    required UpdateProfileUseCase updateProfileUseCase,
    required LoginAndGetProfileUseCase loginAndGetProfileUseCase,
    required SharedPreferences sharedPreferences,
  })  : _loginUseCase = loginUseCase,
        _logoutUseCase = logoutUseCase,
        _getProfileUseCase = getProfileUseCase,
        _updateProfileUseCase = updateProfileUseCase,
        _loginAndGetProfileUseCase = loginAndGetProfileUseCase,
        _sharedPreferences = sharedPreferences,
        super(const AuthInitial()) {
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthGetProfileRequested>(_onGetProfileRequested);
    on<AuthUpdateProfileRequested>(_onUpdateProfileRequested);
    on<AuthCheckStatusRequested>(_onCheckStatusRequested);
    on<AuthClearError>(_onClearError);
  }

  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _loginAndGetProfileUseCase(
      LoginAndGetProfileParams(
        username: event.username,
        password: event.password,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(failure: failure)),
      (loginResult) => emit(AuthAuthenticated(
        user: loginResult.user,
        token: loginResult.token,
      )),
    );
  }

  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _logoutUseCase(const NoParams());

    result.fold(
      (failure) => emit(AuthError(failure: failure)),
      (_) {
        // Clear token from local storage
        _sharedPreferences.remove(ApiConstants.authTokenKey);
        emit(const AuthUnauthenticated());
      },
    );
  }

  Future<void> _onGetProfileRequested(
    AuthGetProfileRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (state is AuthAuthenticated) {
      final currentState = state as AuthAuthenticated;
      emit(AuthProfileLoading(
        user: currentState.user,
        token: currentState.token,
      ));

      final result = await _getProfileUseCase(const NoParams());

      result.fold(
        (failure) => emit(AuthError(failure: failure)),
        (user) => emit(AuthAuthenticated(
          user: user,
          token: currentState.token,
        )),
      );
    }
  }

  Future<void> _onUpdateProfileRequested(
    AuthUpdateProfileRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (state is AuthAuthenticated) {
      final currentState = state as AuthAuthenticated;
      emit(AuthProfileLoading(
        user: currentState.user,
        token: currentState.token,
      ));

      final result = await _updateProfileUseCase(
        UpdateProfileParams(name: event.name),
      );

      result.fold(
        (failure) => emit(AuthError(failure: failure)),
        (user) => emit(AuthProfileUpdated(
          user: user,
          token: currentState.token,
        )),
      );
    }
  }

  Future<void> _onCheckStatusRequested(
    AuthCheckStatusRequested event,
    Emitter<AuthState> emit,
  ) async {
    final token = _sharedPreferences.getString(ApiConstants.authTokenKey);
    
    if (token != null && token.isNotEmpty) {
      emit(const AuthLoading());
      
      final result = await _getProfileUseCase(const NoParams());
      
      result.fold(
        (failure) {
          // Token might be expired, clear it
          _sharedPreferences.remove(ApiConstants.authTokenKey);
          emit(const AuthUnauthenticated());
        },
        (user) => emit(AuthAuthenticated(
          user: user,
          token: token,
        )),
      );
    } else {
      emit(const AuthUnauthenticated());
    }
  }

  void _onClearError(
    AuthClearError event,
    Emitter<AuthState> emit,
  ) {
    if (state is AuthError) {
      emit(const AuthUnauthenticated());
    }
  }
}
