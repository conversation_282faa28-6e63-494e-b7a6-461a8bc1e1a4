import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/auth/user_entity.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthAuthenticated extends AuthState {
  final UserEntity user;
  final String token;

  const AuthAuthenticated({
    required this.user,
    required this.token,
  });

  @override
  List<Object> get props => [user, token];
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthError extends AuthState {
  final Failure failure;

  const AuthError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

class AuthProfileLoading extends AuthState {
  final UserEntity user;
  final String token;

  const AuthProfileLoading({
    required this.user,
    required this.token,
  });

  @override
  List<Object> get props => [user, token];
}

class AuthProfileUpdated extends AuthState {
  final UserEntity user;
  final String token;

  const AuthProfileUpdated({
    required this.user,
    required this.token,
  });

  @override
  List<Object> get props => [user, token];
}
