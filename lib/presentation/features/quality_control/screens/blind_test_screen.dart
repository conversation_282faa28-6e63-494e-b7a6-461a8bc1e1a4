import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/widgets/error_text_field.dart';
import '../../../../core/error/failures.dart';

class BlindTestScreen extends StatefulWidget {
  final int originalTestId;

  const BlindTestScreen({
    super.key,
    required this.originalTestId,
  });

  @override
  State<BlindTestScreen> createState() => _BlindTestScreenState();
}

class _BlindTestScreenState extends State<BlindTestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();
  final _instructionsController = TextEditingController();
  final _blindCodeController = TextEditingController();
  
  String? _selectedAnalyst;
  String _selectedPriority = 'medium';
  DateTime? _dueDate;
  bool _isLoading = false;
  bool _dataLoading = true;
  bool _hideOriginalResult = true;
  Failure? _blindTestFailure;
  
  late Map<String, dynamic> _originalTest;
  final List<Map<String, dynamic>> _availableAnalysts = [
    {'id': 1, 'name': '<PERSON>lyst', 'activeJobs': 2},
    {'id': 2, 'name': 'Jane Analyst', 'activeJobs': 1},
    {'id': 3, 'name': 'Mike Technician', 'activeJobs': 3},
  ];

  @override
  void initState() {
    super.initState();
    _loadOriginalTest();
    _generateBlindCode();
  }

  void _loadOriginalTest() {
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _originalTest = {
          'id': widget.originalTestId,
          'requestNumber': 'TR-2024-001',
          'customerName': 'ABC Industries',
          'originalAnalyst': 'John Analyst',
          'completedDate': '2024-01-20',
          'parameter': 'pH Level',
          'originalResult': '7.2 pH',
          'permissibleLimit': '6.5-8.5 pH',
          'method': 'Electrometric',
          'sampleType': 'Water',
          'sampleCode': 'WTR-001',
        };
        _dataLoading = false;
      });
    });
  }

  void _generateBlindCode() {
    final now = DateTime.now();
    final blindCode = 'BLIND-${now.year}-${now.millisecondsSinceEpoch.toString().substring(8)}';
    _blindCodeController.text = blindCode;
  }

  @override
  Widget build(BuildContext context) {
    if (_dataLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Create Blind Test'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Blind Test'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _createBlindTest,
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Create'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Error Summary
              ValidationErrorSummary(failure: _blindTestFailure),

              // Blind Test Info
              _buildBlindTestInfoCard(),
              const SizedBox(height: 16),

              // Original Test Information (with option to hide result)
              _buildOriginalTestCard(),
              const SizedBox(height: 16),

              // Blind Test Details
              _buildBlindTestDetailsCard(),
              const SizedBox(height: 16),

              // Assignment Details
              _buildAssignmentCard(),
              const SizedBox(height: 24),

              // Create Button
              ElevatedButton(
                onPressed: _isLoading ? null : _createBlindTest,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text(
                        'Create Blind Test',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBlindTestInfoCard() {
    return Card(
      color: Colors.orange.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.visibility_off, color: Colors.orange.shade700),
                const SizedBox(width: 8),
                Text(
                  'Blind Test Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                border: Border.all(color: Colors.orange.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ℹ️ Blind Test Purpose',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'In a blind test, the analyst performs the analysis without knowing the original result. This helps ensure unbiased testing and validates the accuracy of the original analysis.',
                    style: TextStyle(fontSize: 12),
                  ),
                  const SizedBox(height: 12),
                  ErrorTextField(
                    fieldName: 'blind_code',
                    labelText: 'Blind Test Code',
                    controller: _blindCodeController,
                    failure: _blindTestFailure,
                    readOnly: true,
                    prefixIcon: const Icon(Icons.code),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOriginalTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.history, color: Theme.of(context).primaryColor),
                    const SizedBox(width: 8),
                    const Text(
                      'Original Test Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    const Text('Hide Result: '),
                    Switch(
                      value: _hideOriginalResult,
                      onChanged: (value) {
                        setState(() {
                          _hideOriginalResult = value;
                        });
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Request Number', _originalTest['requestNumber']),
            _buildInfoRow('Customer', _originalTest['customerName']),
            _buildInfoRow('Parameter', _originalTest['parameter']),
            _buildInfoRow('Sample Type', _originalTest['sampleType']),
            _buildInfoRow('Sample Code', _originalTest['sampleCode']),
            _buildInfoRow('Original Analyst', _originalTest['originalAnalyst']),
            _buildInfoRow('Completed Date', _originalTest['completedDate']),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _hideOriginalResult ? Colors.grey.shade100 : Colors.blue.shade50,
                border: Border.all(
                  color: _hideOriginalResult ? Colors.grey.shade300 : Colors.blue.shade200,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Original Result',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          color: _hideOriginalResult ? Colors.grey : null,
                        ),
                      ),
                      if (_hideOriginalResult) ...[
                        const SizedBox(width: 8),
                        Icon(
                          Icons.visibility_off,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  if (_hideOriginalResult) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Text(
                        '████████ (Hidden for blind test)',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ] else ...[
                    Text(
                      'Result: ${_originalTest['originalResult']}',
                      style: const TextStyle(fontSize: 14),
                    ),
                    Text(
                      'Limit: ${_originalTest['permissibleLimit']}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                  Text(
                    'Method: ${_originalTest['method']}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBlindTestDetailsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.visibility_off, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Blind Test Details',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Reason for Blind Test
            ErrorTextField(
              fieldName: 'reason',
              labelText: 'Reason for Blind Test *',
              controller: _reasonController,
              failure: _blindTestFailure,
              maxLines: 3,
              prefixIcon: const Icon(Icons.description),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please provide a reason for blind test';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Special Instructions
            ErrorTextField(
              fieldName: 'instructions',
              labelText: 'Special Instructions (Optional)',
              controller: _instructionsController,
              failure: _blindTestFailure,
              maxLines: 3,
              prefixIcon: const Icon(Icons.note),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignmentCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.assignment_ind, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Assignment Details',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Analyst Selection (exclude original analyst)
            DropdownButtonFormField<String>(
              value: _selectedAnalyst,
              decoration: const InputDecoration(
                labelText: 'Assign to Different Analyst *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
                helperText: 'Original analyst will be excluded from selection',
              ),
              items: _availableAnalysts
                  .where((analyst) => analyst['name'] != _originalTest['originalAnalyst'])
                  .map((analyst) {
                return DropdownMenuItem<String>(
                  value: analyst['name'],
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 12,
                        child: Text(
                          analyst['name'].split(' ').map((n) => n[0]).join(),
                          style: const TextStyle(fontSize: 10),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(analyst['name']),
                            Text(
                              '${analyst['activeJobs']} active jobs',
                              style: const TextStyle(fontSize: 12, color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedAnalyst = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select an analyst';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Priority Selection
            DropdownButtonFormField<String>(
              value: _selectedPriority,
              decoration: const InputDecoration(
                labelText: 'Priority',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.flag),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'low',
                  child: Row(
                    children: [
                      Icon(Icons.keyboard_arrow_down, color: Colors.green),
                      SizedBox(width: 8),
                      Text('Low Priority'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'medium',
                  child: Row(
                    children: [
                      Icon(Icons.remove, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('Medium Priority'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'high',
                  child: Row(
                    children: [
                      Icon(Icons.keyboard_arrow_up, color: Colors.red),
                      SizedBox(width: 8),
                      Text('High Priority'),
                    ],
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPriority = value!;
                });
              },
            ),
            const SizedBox(height: 16),

            // Due Date
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Due Date *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.calendar_today),
                suffixIcon: Icon(Icons.arrow_drop_down),
              ),
              readOnly: true,
              onTap: _selectDueDate,
              controller: TextEditingController(
                text: _dueDate != null 
                    ? '${_dueDate!.day}/${_dueDate!.month}/${_dueDate!.year}'
                    : '',
              ),
              validator: (value) {
                if (_dueDate == null) {
                  return 'Please select a due date';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  void _selectDueDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (picked != null) {
      setState(() {
        _dueDate = picked;
      });
    }
  }

  void _createBlindTest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _blindTestFailure = null;
    });

    try {
      // TODO: Implement actual blind test creation with BLoC
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Blind test created successfully')),
        );
        context.pop();
      }
    } catch (e) {
      // TODO: Handle actual creation errors
      setState(() {
        _blindTestFailure = const ValidationFailure(
          message: 'Failed to create blind test',
          fieldErrors: {
            'reason': ['Reason must be more detailed'],
          },
        );
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _reasonController.dispose();
    _instructionsController.dispose();
    _blindCodeController.dispose();
    super.dispose();
  }
}
