import 'package:equatable/equatable.dart';
import '../../../../domain/entities/quality_control/qc_test_entity.dart';

abstract class QCEvent extends Equatable {
  const QCEvent();

  @override
  List<Object?> get props => [];
}

// Load QC tests
class QCTestsLoadRequested extends QCEvent {
  final int page;
  final int perPage;
  final bool refresh;

  const QCTestsLoadRequested({
    this.page = 1,
    this.perPage = 15,
    this.refresh = false,
  });

  @override
  List<Object> get props => [page, perPage, refresh];
}

// Load more QC tests (pagination)
class QCTestsLoadMoreRequested extends QCEvent {
  const QCTestsLoadMoreRequested();
}

// Refresh QC tests
class QCTestsRefreshRequested extends QCEvent {
  const QCTestsRefreshRequested();
}

// Create QC test
class QCTestCreateRequested extends QCEvent {
  final QCTestRequestEntity request;

  const QCTestCreateRequested({
    required this.request,
  });

  @override
  List<Object> get props => [request];
}

// Approve test
class QCTestApproveRequested extends QCEvent {
  final int testId;
  final ApproveTestRequestEntity request;

  const QCTestApproveRequested({
    required this.testId,
    required this.request,
  });

  @override
  List<Object> get props => [testId, request];
}

// Clear error state
class QCClearError extends QCEvent {
  const QCClearError();
}

// Clear success state
class QCClearSuccess extends QCEvent {
  const QCClearSuccess();
}
