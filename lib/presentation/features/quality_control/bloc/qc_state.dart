import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/completed_test/completed_test_entity.dart';
import '../../../../domain/repositories/quality_control_repository.dart';

abstract class QCState extends Equatable {
  const QCState();

  @override
  List<Object?> get props => [];
}

class QCInitial extends QCState {
  const QCInitial();
}

class QCLoading extends QCState {
  const QCLoading();
}

class QCTestsLoaded extends QCState {
  final List<CompletedTestEntity> qcTests;
  final int currentPage;
  final bool hasReachedMax;

  const QCTestsLoaded({
    required this.qcTests,
    required this.currentPage,
    this.hasReachedMax = false,
  });

  @override
  List<Object> get props => [qcTests, currentPage, hasReachedMax];

  QCTestsLoaded copyWith({
    List<CompletedTestEntity>? qcTests,
    int? currentPage,
    bool? hasReachedMax,
  }) {
    return QCTestsLoaded(
      qcTests: qcTests ?? this.qcTests,
      currentPage: currentPage ?? this.currentPage,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }
}

class QCTestsLoadingMore extends QCState {
  final List<CompletedTestEntity> qcTests;
  final int currentPage;

  const QCTestsLoadingMore({
    required this.qcTests,
    required this.currentPage,
  });

  @override
  List<Object> get props => [qcTests, currentPage];
}

class QCError extends QCState {
  final Failure failure;

  const QCError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// QC Test Creation States
class QCTestCreating extends QCState {
  const QCTestCreating();
}

class QCTestCreated extends QCState {
  final CompletedTestEntity createdTest;

  const QCTestCreated({
    required this.createdTest,
  });

  @override
  List<Object> get props => [createdTest];
}

class QCTestCreateError extends QCState {
  final Failure failure;

  const QCTestCreateError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// QC Test Approval States
class QCTestApproving extends QCState {
  const QCTestApproving();
}

class QCTestApproved extends QCState {
  final CompletedTestEntity approvedTest;

  const QCTestApproved({
    required this.approvedTest,
  });

  @override
  List<Object> get props => [approvedTest];
}

class QCTestApprovalError extends QCState {
  final Failure failure;

  const QCTestApprovalError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}
