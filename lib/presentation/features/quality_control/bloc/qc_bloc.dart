import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../domain/usecases/quality_control/get_qc_tests_usecase.dart';
import '../../../../domain/usecases/quality_control/create_qc_test_usecase.dart';
import '../../../../domain/usecases/quality_control/approve_test_usecase.dart';
import 'qc_event.dart';
import 'qc_state.dart';

class QCBloc extends Bloc<QCEvent, QCState> {
  final GetQCTestsUseCase _getQCTestsUseCase;
  final CreateQCTestUseCase _createQCTestUseCase;
  final ApproveTestUseCase _approveTestUseCase;

  QCBloc({
    required GetQCTestsUseCase getQCTestsUseCase,
    required CreateQCTestUseCase createQCTestUseCase,
    required ApproveTestUseCase approveTestUseCase,
  })  : _getQCTestsUseCase = getQCTestsUseCase,
        _createQCTestUseCase = createQCTestUseCase,
        _approveTestUseCase = approveTestUseCase,
        super(const QCInitial()) {
    on<QCTestsLoadRequested>(_onLoadRequested);
    on<QCTestsLoadMoreRequested>(_onLoadMoreRequested);
    on<QCTestsRefreshRequested>(_onRefreshRequested);
    on<QCTestCreateRequested>(_onCreateRequested);
    on<QCTestApproveRequested>(_onApproveRequested);
    on<QCClearError>(_onClearError);
    on<QCClearSuccess>(_onClearSuccess);
  }

  Future<void> _onLoadRequested(
    QCTestsLoadRequested event,
    Emitter<QCState> emit,
  ) async {
    if (event.refresh || state is QCInitial) {
      emit(const QCLoading());
    }

    final result = await _getQCTestsUseCase(
      GetQCTestsParams(
        page: event.page,
        perPage: event.perPage,
      ),
    );

    result.fold(
      (failure) => emit(QCError(failure: failure)),
      (qcTestsResult) => emit(QCTestsLoaded(
        qcTests: qcTestsResult.tests,
        currentPage: qcTestsResult.currentPage,
        hasReachedMax: qcTestsResult.tests.length < event.perPage,
      )),
    );
  }

  Future<void> _onLoadMoreRequested(
    QCTestsLoadMoreRequested event,
    Emitter<QCState> emit,
  ) async {
    final currentState = state;
    if (currentState is QCTestsLoaded && !currentState.hasReachedMax) {
      emit(QCTestsLoadingMore(
        qcTests: currentState.qcTests,
        currentPage: currentState.currentPage,
      ));

      final nextPage = currentState.currentPage + 1;
      final result = await _getQCTestsUseCase(
        GetQCTestsParams(page: nextPage),
      );

      result.fold(
        (failure) => emit(QCError(failure: failure)),
        (qcTestsResult) {
          final allQCTests = [
            ...currentState.qcTests,
            ...qcTestsResult.tests,
          ];

          emit(QCTestsLoaded(
            qcTests: allQCTests,
            currentPage: qcTestsResult.currentPage,
            hasReachedMax: qcTestsResult.tests.length < 15,
          ));
        },
      );
    }
  }

  Future<void> _onRefreshRequested(
    QCTestsRefreshRequested event,
    Emitter<QCState> emit,
  ) async {
    add(const QCTestsLoadRequested(refresh: true));
  }

  Future<void> _onCreateRequested(
    QCTestCreateRequested event,
    Emitter<QCState> emit,
  ) async {
    emit(const QCTestCreating());

    final result = await _createQCTestUseCase(event.request);

    result.fold(
      (failure) => emit(QCTestCreateError(failure: failure)),
      (createdTest) => emit(QCTestCreated(createdTest: createdTest)),
    );
  }

  Future<void> _onApproveRequested(
    QCTestApproveRequested event,
    Emitter<QCState> emit,
  ) async {
    emit(const QCTestApproving());

    final result = await _approveTestUseCase(
      ApproveTestParams(
        testId: event.testId,
        request: event.request,
      ),
    );

    result.fold(
      (failure) => emit(QCTestApprovalError(failure: failure)),
      (approvedTest) => emit(QCTestApproved(approvedTest: approvedTest)),
    );
  }

  void _onClearError(
    QCClearError event,
    Emitter<QCState> emit,
  ) {
    if (state is QCError || state is QCTestCreateError || state is QCTestApprovalError) {
      emit(const QCInitial());
    }
  }

  void _onClearSuccess(
    QCClearSuccess event,
    Emitter<QCState> emit,
  ) {
    if (state is QCTestCreated || state is QCTestApproved) {
      emit(const QCInitial());
    }
  }
}
