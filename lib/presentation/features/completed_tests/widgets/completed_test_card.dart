import 'package:flutter/material.dart';
import '../../../../domain/entities/completed_test/completed_test_entity.dart';
import '../../../core/utils/date_formatter.dart';

class CompletedTestCard extends StatelessWidget {
  final CompletedTestEntity completedTest;
  final VoidCallback? onTap;

  const CompletedTestCard({
    super.key,
    required this.completedTest,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Test ID: ${completedTest.id}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildApprovalStatusChip(),
                ],
              ),
              const SizedBox(height: 8),

              // Test Details
              _buildDetailRow(
                icon: Icons.science_outlined,
                label: 'Result',
                value: completedTest.result,
              ),
              const SizedBox(height: 4),

              if (completedTest.analysisCompletionDate != null)
                _buildDetailRow(
                  icon: Icons.schedule_outlined,
                  label: 'Completed',
                  value: DateFormatter.formatDate(completedTest.analysisCompletionDate!),
                ),
              const SizedBox(height: 4),

              if (completedTest.approvalDate != null)
                _buildDetailRow(
                  icon: Icons.check_circle_outline,
                  label: 'Approved',
                  value: DateFormatter.formatDate(completedTest.approvalDate!),
                ),

              // Quality Control Indicators
              if (completedTest.isRetest || completedTest.isBlind || completedTest.isReplicate)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Wrap(
                    spacing: 8,
                    children: [
                      if (completedTest.isRetest) _buildQCChip('Retest', Colors.orange),
                      if (completedTest.isBlind) _buildQCChip('Blind', Colors.blue),
                      if (completedTest.isReplicate) _buildQCChip('Replicate', Colors.purple),
                    ],
                  ),
                ),

              // Location/Water Type Data
              if (completedTest.hasLocationData || completedTest.hasWaterTypeData)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    children: [
                      if (completedTest.hasLocationData)
                        Icon(
                          Icons.location_on_outlined,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                      if (completedTest.hasWaterTypeData)
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Icon(
                            Icons.water_drop_outlined,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      const SizedBox(width: 4),
                      Text(
                        'Additional data available',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),

              // Approval Remarks
              if (completedTest.approvalRemarks != null && 
                  completedTest.approvalRemarks!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.comment_outlined,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            completedTest.approvalRemarks!,
                            style: Theme.of(context).textTheme.bodySmall,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 14),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildApprovalStatusChip() {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (completedTest.approvalStatus) {
      case ApprovalStatus.approved:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[800]!;
        text = 'Approved';
        break;
      case ApprovalStatus.rejected:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[800]!;
        text = 'Rejected';
        break;
      case ApprovalStatus.pending:
      default:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[800]!;
        text = 'Pending';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildQCChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
