import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/completed_tests_bloc.dart';
import '../bloc/completed_tests_event.dart';
import '../bloc/completed_tests_state.dart';
import '../widgets/completed_test_card.dart';
import '../widgets/completed_tests_summary_card.dart';

class CompletedTestsScreen extends StatefulWidget {
  const CompletedTestsScreen({super.key});

  @override
  State<CompletedTestsScreen> createState() => _CompletedTestsScreenState();
}

class _CompletedTestsScreenState extends State<CompletedTestsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    context.read<CompletedTestsBloc>().add(const CompletedTestsLoadRequested());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<CompletedTestsBloc>().add(const CompletedTestsLoadMoreRequested());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Completed Tests'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<CompletedTestsBloc, CompletedTestsState>(
        builder: (context, state) {
          if (state is CompletedTestsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is CompletedTestsError) {
            return _buildErrorWidget(context, state.failure.message);
          }

          if (state is CompletedTestsLoaded) {
            if (state.completedTests.isEmpty) {
              return _buildEmptyState();
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<CompletedTestsBloc>().add(const CompletedTestsRefreshRequested());
              },
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Summary Card
                  if (state.summary != null)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: CompletedTestsSummaryCard(summary: state.summary!),
                      ),
                    ),

                  // Completed Tests List
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          if (index >= state.completedTests.length) {
                            return const SizedBox(
                              height: 80,
                              child: Center(child: CircularProgressIndicator()),
                            );
                          }

                          final completedTest = state.completedTests[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12.0),
                            child: CompletedTestCard(
                              completedTest: completedTest,
                              onTap: () {
                                // Navigate to completed test details
                                // Navigator.pushNamed(
                                //   context,
                                //   AppRoutes.completedTestDetailsWithId(completedTest.id),
                                // );
                              },
                            ),
                          );
                        },
                        childCount: state.hasReachedMax
                            ? state.completedTests.length
                            : state.completedTests.length + 1,
                      ),
                    ),
                  ),

                  // Bottom padding
                  const SliverToBoxAdapter(
                    child: SizedBox(height: 16),
                  ),
                ],
              ),
            );
          }

          if (state is CompletedTestsLoadingMore) {
            return RefreshIndicator(
              onRefresh: () async {
                context.read<CompletedTestsBloc>().add(const CompletedTestsRefreshRequested());
              },
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Summary Card
                  if (state.summary != null)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: CompletedTestsSummaryCard(summary: state.summary!),
                      ),
                    ),

                  // Completed Tests List
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          if (index >= state.completedTests.length) {
                            return const SizedBox(
                              height: 80,
                              child: Center(child: CircularProgressIndicator()),
                            );
                          }

                          final completedTest = state.completedTests[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12.0),
                            child: CompletedTestCard(
                              completedTest: completedTest,
                              onTap: () {
                                // Navigate to completed test details
                              },
                            ),
                          );
                        },
                        childCount: state.completedTests.length + 1,
                      ),
                    ),
                  ),

                  // Bottom padding
                  const SliverToBoxAdapter(
                    child: SizedBox(height: 16),
                  ),
                ],
              ),
            );
          }

          return _buildErrorWidget(context, 'Unable to load completed tests.');
        },
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.read<CompletedTestsBloc>().add(const CompletedTestsRefreshRequested());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_turned_in_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Completed Tests',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'There are no completed tests to display.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
