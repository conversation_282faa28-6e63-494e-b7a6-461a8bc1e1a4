import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/empty_state_widget.dart';
import '../bloc/completed_tests_bloc.dart';
import '../bloc/completed_tests_event.dart';
import '../bloc/completed_tests_state.dart';
import '../widgets/completed_test_card.dart';
import '../widgets/completed_tests_summary_card.dart';

class CompletedTestsScreen extends StatefulWidget {
  const CompletedTestsScreen({super.key});

  @override
  State<CompletedTestsScreen> createState() => _CompletedTestsScreenState();
}

class _CompletedTestsScreenState extends State<CompletedTestsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    context.read<CompletedTestsBloc>().add(const CompletedTestsLoadRequested());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<CompletedTestsBloc>().add(const CompletedTestsLoadMoreRequested());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Completed Tests',
        showBackButton: true,
      ),
      body: BlocBuilder<CompletedTestsBloc, CompletedTestsState>(
        builder: (context, state) {
          if (state is CompletedTestsLoading) {
            return const LoadingWidget();
          }

          if (state is CompletedTestsError) {
            return CustomErrorWidget(
              message: state.failure.message,
              onRetry: () {
                context.read<CompletedTestsBloc>().add(const CompletedTestsRefreshRequested());
              },
            );
          }

          if (state is CompletedTestsLoaded) {
            if (state.completedTests.isEmpty) {
              return const EmptyStateWidget(
                title: 'No Completed Tests',
                message: 'There are no completed tests to display.',
                icon: Icons.assignment_turned_in_outlined,
              );
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<CompletedTestsBloc>().add(const CompletedTestsRefreshRequested());
              },
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Summary Card
                  if (state.summary != null)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: CompletedTestsSummaryCard(summary: state.summary!),
                      ),
                    ),

                  // Completed Tests List
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          if (index >= state.completedTests.length) {
                            return const SizedBox(
                              height: 80,
                              child: Center(child: CircularProgressIndicator()),
                            );
                          }

                          final completedTest = state.completedTests[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12.0),
                            child: CompletedTestCard(
                              completedTest: completedTest,
                              onTap: () {
                                // Navigate to completed test details
                                // Navigator.pushNamed(
                                //   context,
                                //   AppRoutes.completedTestDetailsWithId(completedTest.id),
                                // );
                              },
                            ),
                          );
                        },
                        childCount: state.hasReachedMax
                            ? state.completedTests.length
                            : state.completedTests.length + 1,
                      ),
                    ),
                  ),

                  // Bottom padding
                  const SliverToBoxAdapter(
                    child: SizedBox(height: 16),
                  ),
                ],
              ),
            );
          }

          if (state is CompletedTestsLoadingMore) {
            return RefreshIndicator(
              onRefresh: () async {
                context.read<CompletedTestsBloc>().add(const CompletedTestsRefreshRequested());
              },
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Summary Card
                  if (state.summary != null)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: CompletedTestsSummaryCard(summary: state.summary!),
                      ),
                    ),

                  // Completed Tests List
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          if (index >= state.completedTests.length) {
                            return const SizedBox(
                              height: 80,
                              child: Center(child: CircularProgressIndicator()),
                            );
                          }

                          final completedTest = state.completedTests[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12.0),
                            child: CompletedTestCard(
                              completedTest: completedTest,
                              onTap: () {
                                // Navigate to completed test details
                              },
                            ),
                          );
                        },
                        childCount: state.completedTests.length + 1,
                      ),
                    ),
                  ),

                  // Bottom padding
                  const SliverToBoxAdapter(
                    child: SizedBox(height: 16),
                  ),
                ],
              ),
            );
          }

          return const EmptyStateWidget(
            title: 'Something went wrong',
            message: 'Unable to load completed tests.',
            icon: Icons.error_outline,
          );
        },
      ),
    );
  }
}
