import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../domain/usecases/completed_test/get_completed_tests_usecase.dart';
import '../../../../domain/usecases/completed_test/get_completed_test_by_id_usecase.dart';
import 'completed_tests_event.dart';
import 'completed_tests_state.dart';

class CompletedTestsBloc extends Bloc<CompletedTestsEvent, CompletedTestsState> {
  final GetCompletedTestsUseCase _getCompletedTestsUseCase;
  final GetCompletedTestByIdUseCase _getCompletedTestByIdUseCase;

  CompletedTestsBloc({
    required GetCompletedTestsUseCase getCompletedTestsUseCase,
    required GetCompletedTestByIdUseCase getCompletedTestByIdUseCase,
  })  : _getCompletedTestsUseCase = getCompletedTestsUseCase,
        _getCompletedTestByIdUseCase = getCompletedTestByIdUseCase,
        super(const CompletedTestsInitial()) {
    on<CompletedTestsLoadRequested>(_onLoadRequested);
    on<CompletedTestsLoadMoreRequested>(_onLoadMoreRequested);
    on<CompletedTestsRefreshRequested>(_onRefreshRequested);
    on<CompletedTestDetailsLoadRequested>(_onDetailsLoadRequested);
    on<CompletedTestsClearError>(_onClearError);
  }

  Future<void> _onLoadRequested(
    CompletedTestsLoadRequested event,
    Emitter<CompletedTestsState> emit,
  ) async {
    if (event.refresh || state is CompletedTestsInitial) {
      emit(const CompletedTestsLoading());
    }

    final result = await _getCompletedTestsUseCase(
      GetCompletedTestsParams(
        page: event.page,
        perPage: event.perPage,
      ),
    );

    result.fold(
      (failure) => emit(CompletedTestsError(failure: failure)),
      (completedTestsResult) => emit(CompletedTestsLoaded(
        completedTests: completedTestsResult.completedTests,
        pagination: completedTestsResult.pagination,
        summary: completedTestsResult.summary,
        hasReachedMax: !completedTestsResult.pagination.hasMore,
      )),
    );
  }

  Future<void> _onLoadMoreRequested(
    CompletedTestsLoadMoreRequested event,
    Emitter<CompletedTestsState> emit,
  ) async {
    final currentState = state;
    if (currentState is CompletedTestsLoaded && !currentState.hasReachedMax) {
      emit(CompletedTestsLoadingMore(
        completedTests: currentState.completedTests,
        pagination: currentState.pagination,
        summary: currentState.summary,
      ));

      final nextPage = currentState.pagination.currentPage + 1;
      final result = await _getCompletedTestsUseCase(
        GetCompletedTestsParams(page: nextPage),
      );

      result.fold(
        (failure) => emit(CompletedTestsError(failure: failure)),
        (completedTestsResult) {
          final allCompletedTests = [
            ...currentState.completedTests,
            ...completedTestsResult.completedTests,
          ];

          emit(CompletedTestsLoaded(
            completedTests: allCompletedTests,
            pagination: completedTestsResult.pagination,
            summary: completedTestsResult.summary,
            hasReachedMax: !completedTestsResult.pagination.hasMore,
          ));
        },
      );
    }
  }

  Future<void> _onRefreshRequested(
    CompletedTestsRefreshRequested event,
    Emitter<CompletedTestsState> emit,
  ) async {
    add(const CompletedTestsLoadRequested(refresh: true));
  }

  Future<void> _onDetailsLoadRequested(
    CompletedTestDetailsLoadRequested event,
    Emitter<CompletedTestsState> emit,
  ) async {
    emit(const CompletedTestDetailsLoading());

    final result = await _getCompletedTestByIdUseCase(
      GetCompletedTestByIdParams(id: event.testId),
    );

    result.fold(
      (failure) => emit(CompletedTestDetailsError(failure: failure)),
      (completedTest) => emit(CompletedTestDetailsLoaded(completedTest: completedTest)),
    );
  }

  void _onClearError(
    CompletedTestsClearError event,
    Emitter<CompletedTestsState> emit,
  ) {
    if (state is CompletedTestsError || state is CompletedTestDetailsError) {
      emit(const CompletedTestsInitial());
    }
  }
}
