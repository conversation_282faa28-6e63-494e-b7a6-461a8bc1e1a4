import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/completed_test/completed_test_entity.dart';
import '../../../../domain/entities/common/pagination_entity.dart';
import '../../../../domain/repositories/completed_test_repository.dart';

abstract class CompletedTestsState extends Equatable {
  const CompletedTestsState();

  @override
  List<Object?> get props => [];
}

class CompletedTestsInitial extends CompletedTestsState {
  const CompletedTestsInitial();
}

class CompletedTestsLoading extends CompletedTestsState {
  const CompletedTestsLoading();
}

class CompletedTestsLoaded extends CompletedTestsState {
  final List<CompletedTestEntity> completedTests;
  final PaginationEntity pagination;
  final CompletedTestsSummary? summary;
  final bool hasReachedMax;

  const CompletedTestsLoaded({
    required this.completedTests,
    required this.pagination,
    this.summary,
    this.hasReachedMax = false,
  });

  @override
  List<Object?> get props => [completedTests, pagination, summary, hasReachedMax];

  CompletedTestsLoaded copyWith({
    List<CompletedTestEntity>? completedTests,
    PaginationEntity? pagination,
    CompletedTestsSummary? summary,
    bool? hasReachedMax,
  }) {
    return CompletedTestsLoaded(
      completedTests: completedTests ?? this.completedTests,
      pagination: pagination ?? this.pagination,
      summary: summary ?? this.summary,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }
}

class CompletedTestsLoadingMore extends CompletedTestsState {
  final List<CompletedTestEntity> completedTests;
  final PaginationEntity pagination;
  final CompletedTestsSummary? summary;

  const CompletedTestsLoadingMore({
    required this.completedTests,
    required this.pagination,
    this.summary,
  });

  @override
  List<Object?> get props => [completedTests, pagination, summary];
}

class CompletedTestsError extends CompletedTestsState {
  final Failure failure;

  const CompletedTestsError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Completed Test Details States
class CompletedTestDetailsLoading extends CompletedTestsState {
  const CompletedTestDetailsLoading();
}

class CompletedTestDetailsLoaded extends CompletedTestsState {
  final CompletedTestEntity completedTest;

  const CompletedTestDetailsLoaded({
    required this.completedTest,
  });

  @override
  List<Object> get props => [completedTest];
}

class CompletedTestDetailsError extends CompletedTestsState {
  final Failure failure;

  const CompletedTestDetailsError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}
