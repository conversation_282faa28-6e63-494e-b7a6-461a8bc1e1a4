import 'package:equatable/equatable.dart';

abstract class CompletedTestsEvent extends Equatable {
  const CompletedTestsEvent();

  @override
  List<Object?> get props => [];
}

// Load completed tests with pagination
class CompletedTestsLoadRequested extends CompletedTestsEvent {
  final int page;
  final int perPage;
  final bool refresh;

  const CompletedTestsLoadRequested({
    this.page = 1,
    this.perPage = 15,
    this.refresh = false,
  });

  @override
  List<Object> get props => [page, perPage, refresh];
}

// Load more completed tests (pagination)
class CompletedTestsLoadMoreRequested extends CompletedTestsEvent {
  const CompletedTestsLoadMoreRequested();
}

// Refresh completed tests
class CompletedTestsRefreshRequested extends CompletedTestsEvent {
  const CompletedTestsRefreshRequested();
}

// Load completed test details by ID
class CompletedTestDetailsLoadRequested extends CompletedTestsEvent {
  final int testId;

  const CompletedTestDetailsLoadRequested({
    required this.testId,
  });

  @override
  List<Object> get props => [testId];
}

// Clear error state
class CompletedTestsClearError extends CompletedTestsEvent {
  const CompletedTestsClearError();
}
