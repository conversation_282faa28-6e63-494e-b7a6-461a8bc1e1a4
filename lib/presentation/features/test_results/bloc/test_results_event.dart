import 'package:equatable/equatable.dart';
import '../../../../domain/entities/test_result/test_result_entity.dart';

abstract class TestResultsEvent extends Equatable {
  const TestResultsEvent();

  @override
  List<Object?> get props => [];
}

// Load test results with filters
class TestResultsLoadRequested extends TestResultsEvent {
  final int page;
  final int perPage;
  final int? parameterId;
  final int? sampleId;
  final int? analystId;
  final TestResultStatus? status;
  final bool refresh;

  const TestResultsLoadRequested({
    this.page = 1,
    this.perPage = 15,
    this.parameterId,
    this.sampleId,
    this.analystId,
    this.status,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [page, perPage, parameterId, sampleId, analystId, status, refresh];
}

// Load more test results (pagination)
class TestResultsLoadMoreRequested extends TestResultsEvent {
  const TestResultsLoadMoreRequested();
}

// Refresh test results
class TestResultsRefreshRequested extends TestResultsEvent {
  const TestResultsRefreshRequested();
}

// Load test result details by ID
class TestResultDetailsLoadRequested extends TestResultsEvent {
  final int testResultId;

  const TestResultDetailsLoadRequested({
    required this.testResultId,
  });

  @override
  List<Object> get props => [testResultId];
}

// Load test results by allocation
class TestResultsByAllocationLoadRequested extends TestResultsEvent {
  final int parameterAllocationId;

  const TestResultsByAllocationLoadRequested({
    required this.parameterAllocationId,
  });

  @override
  List<Object> get props => [parameterAllocationId];
}

// Create test result
class TestResultCreateRequested extends TestResultsEvent {
  final Map<String, dynamic> request;

  const TestResultCreateRequested({
    required this.request,
  });

  @override
  List<Object> get props => [request];
}

// Update test result
class TestResultUpdateRequested extends TestResultsEvent {
  final int testResultId;
  final UpdateTestResultRequestEntity request;

  const TestResultUpdateRequested({
    required this.testResultId,
    required this.request,
  });

  @override
  List<Object> get props => [testResultId, request];
}

// Delete test result
class TestResultDeleteRequested extends TestResultsEvent {
  final int testResultId;

  const TestResultDeleteRequested({
    required this.testResultId,
  });

  @override
  List<Object> get props => [testResultId];
}

// Load analyst's test results
class MyTestResultsLoadRequested extends TestResultsEvent {
  final int analystId;

  const MyTestResultsLoadRequested({
    required this.analystId,
  });

  @override
  List<Object> get props => [analystId];
}

// Load test results requiring attention
class TestResultsRequiringAttentionLoadRequested extends TestResultsEvent {
  const TestResultsRequiringAttentionLoadRequested();
}

// Clear error state
class TestResultsClearError extends TestResultsEvent {
  const TestResultsClearError();
}

// Clear success state
class TestResultsClearSuccess extends TestResultsEvent {
  const TestResultsClearSuccess();
}
