import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/test_result/test_result_entity.dart';
import '../../../../domain/entities/common/pagination_entity.dart';

abstract class TestResultsState extends Equatable {
  const TestResultsState();

  @override
  List<Object?> get props => [];
}

class TestResultsInitial extends TestResultsState {
  const TestResultsInitial();
}

class TestResultsLoading extends TestResultsState {
  const TestResultsLoading();
}

class TestResultsLoaded extends TestResultsState {
  final List<TestResultEntity> testResults;
  final PaginationEntity pagination;
  final bool hasReachedMax;

  const TestResultsLoaded({
    required this.testResults,
    required this.pagination,
    this.hasReachedMax = false,
  });

  @override
  List<Object> get props => [testResults, pagination, hasReachedMax];

  TestResultsLoaded copyWith({
    List<TestResultEntity>? testResults,
    PaginationEntity? pagination,
    bool? hasReachedMax,
  }) {
    return TestResultsLoaded(
      testResults: testResults ?? this.testResults,
      pagination: pagination ?? this.pagination,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }
}

class TestResultsLoadingMore extends TestResultsState {
  final List<TestResultEntity> testResults;
  final PaginationEntity pagination;

  const TestResultsLoadingMore({
    required this.testResults,
    required this.pagination,
  });

  @override
  List<Object> get props => [testResults, pagination];
}

class TestResultsError extends TestResultsState {
  final Failure failure;

  const TestResultsError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Test Result Details States
class TestResultDetailsLoading extends TestResultsState {
  const TestResultDetailsLoading();
}

class TestResultDetailsLoaded extends TestResultsState {
  final TestResultEntity testResult;

  const TestResultDetailsLoaded({
    required this.testResult,
  });

  @override
  List<Object> get props => [testResult];
}

class TestResultDetailsError extends TestResultsState {
  final Failure failure;

  const TestResultDetailsError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Test Results by Allocation States
class TestResultsByAllocationLoading extends TestResultsState {
  const TestResultsByAllocationLoading();
}

class TestResultsByAllocationLoaded extends TestResultsState {
  final List<TestResultEntity> testResults;

  const TestResultsByAllocationLoaded({
    required this.testResults,
  });

  @override
  List<Object> get props => [testResults];
}

class TestResultsByAllocationError extends TestResultsState {
  final Failure failure;

  const TestResultsByAllocationError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Test Result Creation States
class TestResultCreating extends TestResultsState {
  const TestResultCreating();
}

class TestResultCreated extends TestResultsState {
  final TestResultEntity createdTestResult;

  const TestResultCreated({
    required this.createdTestResult,
  });

  @override
  List<Object> get props => [createdTestResult];
}

class TestResultCreateError extends TestResultsState {
  final Failure failure;

  const TestResultCreateError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Test Result Update States
class TestResultUpdating extends TestResultsState {
  const TestResultUpdating();
}

class TestResultUpdated extends TestResultsState {
  final TestResultEntity updatedTestResult;

  const TestResultUpdated({
    required this.updatedTestResult,
  });

  @override
  List<Object> get props => [updatedTestResult];
}

class TestResultUpdateError extends TestResultsState {
  final Failure failure;

  const TestResultUpdateError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Test Result Deletion States
class TestResultDeleting extends TestResultsState {
  const TestResultDeleting();
}

class TestResultDeleted extends TestResultsState {
  const TestResultDeleted();
}

class TestResultDeleteError extends TestResultsState {
  final Failure failure;

  const TestResultDeleteError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// My Test Results States
class MyTestResultsLoading extends TestResultsState {
  const MyTestResultsLoading();
}

class MyTestResultsLoaded extends TestResultsState {
  final List<TestResultEntity> myTestResults;

  const MyTestResultsLoaded({
    required this.myTestResults,
  });

  @override
  List<Object> get props => [myTestResults];
}

class MyTestResultsError extends TestResultsState {
  final Failure failure;

  const MyTestResultsError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Test Results Requiring Attention States
class TestResultsRequiringAttentionLoading extends TestResultsState {
  const TestResultsRequiringAttentionLoading();
}

class TestResultsRequiringAttentionLoaded extends TestResultsState {
  final List<TestResultEntity> testResultsRequiringAttention;

  const TestResultsRequiringAttentionLoaded({
    required this.testResultsRequiringAttention,
  });

  @override
  List<Object> get props => [testResultsRequiringAttention];
}

class TestResultsRequiringAttentionError extends TestResultsState {
  final Failure failure;

  const TestResultsRequiringAttentionError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}
