import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../domain/usecases/test_result/get_test_results_usecase.dart';
import '../../../../domain/usecases/test_result/create_test_result_usecase.dart';
import 'test_results_event.dart';
import 'test_results_state.dart';

class TestResultsBloc extends Bloc<TestResultsEvent, TestResultsState> {
  final GetTestResultsUseCase _getTestResultsUseCase;
  final CreateTestResultUseCase _createTestResultUseCase;

  TestResultsBloc({
    required GetTestResultsUseCase getTestResultsUseCase,
    required CreateTestResultUseCase createTestResultUseCase,
  })  : _getTestResultsUseCase = getTestResultsUseCase,
        _createTestResultUseCase = createTestResultUseCase,
        super(const TestResultsInitial()) {
    on<TestResultsLoadRequested>(_onLoadRequested);
    on<TestResultsLoadMoreRequested>(_onLoadMoreRequested);
    on<TestResultsRefreshRequested>(_onRefreshRequested);
    on<TestResultCreateRequested>(_onCreateRequested);
    on<TestResultsClearError>(_onClearError);
    on<TestResultsClearSuccess>(_onClearSuccess);
  }

  Future<void> _onLoadRequested(
    TestResultsLoadRequested event,
    Emitter<TestResultsState> emit,
  ) async {
    if (event.refresh || state is TestResultsInitial) {
      emit(const TestResultsLoading());
    }

    final result = await _getTestResultsUseCase(
      GetTestResultsParams(
        page: event.page,
        perPage: event.perPage,
        parameterId: event.parameterId,
        sampleId: event.sampleId,
        analystId: event.analystId,
        status: event.status,
      ),
    );

    result.fold(
      (failure) => emit(TestResultsError(failure: failure)),
      (testResultsResult) => emit(TestResultsLoaded(
        testResults: testResultsResult.items,
        pagination: testResultsResult.pagination,
        hasReachedMax: !testResultsResult.pagination.hasMore,
      )),
    );
  }

  Future<void> _onLoadMoreRequested(
    TestResultsLoadMoreRequested event,
    Emitter<TestResultsState> emit,
  ) async {
    final currentState = state;
    if (currentState is TestResultsLoaded && !currentState.hasReachedMax) {
      emit(TestResultsLoadingMore(
        testResults: currentState.testResults,
        pagination: currentState.pagination,
      ));

      final nextPage = currentState.pagination.currentPage + 1;
      final result = await _getTestResultsUseCase(
        GetTestResultsParams(page: nextPage),
      );

      result.fold(
        (failure) => emit(TestResultsError(failure: failure)),
        (testResultsResult) {
          final allTestResults = [
            ...currentState.testResults,
            ...testResultsResult.items,
          ];

          emit(TestResultsLoaded(
            testResults: allTestResults,
            pagination: testResultsResult.pagination,
            hasReachedMax: !testResultsResult.pagination.hasMore,
          ));
        },
      );
    }
  }

  Future<void> _onRefreshRequested(
    TestResultsRefreshRequested event,
    Emitter<TestResultsState> emit,
  ) async {
    add(const TestResultsLoadRequested(refresh: true));
  }

  Future<void> _onCreateRequested(
    TestResultCreateRequested event,
    Emitter<TestResultsState> emit,
  ) async {
    emit(const TestResultCreating());

    final result = await _createTestResultUseCase(event.request);

    result.fold(
      (failure) => emit(TestResultCreateError(failure: failure)),
      (createdTestResult) => emit(TestResultCreated(createdTestResult: createdTestResult)),
    );
  }

  void _onClearError(
    TestResultsClearError event,
    Emitter<TestResultsState> emit,
  ) {
    if (state is TestResultsError || 
        state is TestResultDetailsError ||
        state is TestResultsByAllocationError ||
        state is TestResultCreateError ||
        state is TestResultUpdateError ||
        state is TestResultDeleteError ||
        state is MyTestResultsError ||
        state is TestResultsRequiringAttentionError) {
      emit(const TestResultsInitial());
    }
  }

  void _onClearSuccess(
    TestResultsClearSuccess event,
    Emitter<TestResultsState> emit,
  ) {
    if (state is TestResultCreated || 
        state is TestResultUpdated ||
        state is TestResultDeleted) {
      emit(const TestResultsInitial());
    }
  }
}
