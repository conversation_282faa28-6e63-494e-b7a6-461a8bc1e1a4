import 'package:flutter/material.dart';

class ViewReportsScreen extends StatefulWidget {
  const ViewReportsScreen({super.key});

  @override
  State<ViewReportsScreen> createState() => _ViewReportsScreenState();
}

class _ViewReportsScreenState extends State<ViewReportsScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _statusFilter = 'all';
  DateTimeRange? _dateRange;

  // Mock data - will be replaced with actual data from BLoC
  final List<Map<String, dynamic>> _mockReports = [
    {
      'id': 1,
      'requestNumber': 'TR-2024-001',
      'customerName': 'ABC Industries',
      'reportDate': '2024-01-20',
      'status': 'completed',
      'sampleType': 'Water',
      'testCount': 3,
      'reportUrl': 'https://example.com/report1.pdf',
    },
    {
      'id': 2,
      'requestNumber': 'TR-2024-002',
      'customerName': 'XYZ Corp',
      'reportDate': '2024-01-21',
      'status': 'completed',
      'sampleType': 'Soil',
      'testCount': 5,
      'reportUrl': 'https://example.com/report2.pdf',
    },
    {
      'id': 3,
      'requestNumber': 'TR-2024-003',
      'customerName': 'DEF Ltd',
      'reportDate': '2024-01-22',
      'status': 'pending',
      'sampleType': 'Air',
      'testCount': 2,
      'reportUrl': null,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredReports = _getFilteredReports();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Reports'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[50],
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search by request number or customer name...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),
                
                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      FilterChip(
                        label: Text('Status: ${_getStatusLabel(_statusFilter)}'),
                        selected: _statusFilter != 'all',
                        onSelected: (_) => _showStatusFilter(),
                      ),
                      const SizedBox(width: 8),
                      FilterChip(
                        label: Text(_dateRange == null 
                            ? 'Date Range' 
                            : '${_formatDate(_dateRange!.start)} - ${_formatDate(_dateRange!.end)}'),
                        selected: _dateRange != null,
                        onSelected: (_) => _selectDateRange(),
                      ),
                      if (_statusFilter != 'all' || _dateRange != null) ...[
                        const SizedBox(width: 8),
                        ActionChip(
                          label: const Text('Clear Filters'),
                          onPressed: _clearFilters,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Reports List
          Expanded(
            child: filteredReports.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: filteredReports.length,
                    itemBuilder: (context, index) {
                      final report = filteredReports[index];
                      return _buildReportCard(report);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(Map<String, dynamic> report) {
    final isCompleted = report['status'] == 'completed';
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  report['requestNumber'],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                _buildStatusChip(report['status']),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              report['customerName'],
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  report['reportDate'],
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.science, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  report['sampleType'],
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.assignment, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  '${report['testCount']} tests',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (isCompleted) ...[
                  OutlinedButton.icon(
                    onPressed: () => _viewReport(report),
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('View'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => _downloadReport(report),
                    icon: const Icon(Icons.download, size: 16),
                    label: const Text('Download PDF'),
                  ),
                ] else ...[
                  Text(
                    'Report not ready',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;
    
    switch (status) {
      case 'pending':
        color = Colors.orange;
        label = 'Pending';
        break;
      case 'completed':
        color = Colors.green;
        label = 'Completed';
        break;
      default:
        color = Colors.grey;
        label = 'Unknown';
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      ),
      backgroundColor: color,
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No reports found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Reports will appear here once tests are completed',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredReports() {
    return _mockReports.where((report) {
      final matchesSearch = _searchQuery.isEmpty ||
          report['requestNumber'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          report['customerName'].toLowerCase().contains(_searchQuery.toLowerCase());
      
      final matchesStatus = _statusFilter == 'all' || report['status'] == _statusFilter;
      
      final matchesDate = _dateRange == null || 
          _isDateInRange(DateTime.parse(report['reportDate']), _dateRange!);
      
      return matchesSearch && matchesStatus && matchesDate;
    }).toList();
  }

  bool _isDateInRange(DateTime date, DateTimeRange range) {
    return date.isAfter(range.start.subtract(const Duration(days: 1))) &&
           date.isBefore(range.end.add(const Duration(days: 1)));
  }

  String _getStatusLabel(String status) {
    switch (status) {
      case 'all':
        return 'All';
      case 'pending':
        return 'Pending';
      case 'completed':
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showFilterDialog() {
    // TODO: Implement comprehensive filter dialog
  }

  void _showStatusFilter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('All'),
              value: 'all',
              groupValue: _statusFilter,
              onChanged: (value) {
                setState(() {
                  _statusFilter = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('Pending'),
              value: 'pending',
              groupValue: _statusFilter,
              onChanged: (value) {
                setState(() {
                  _statusFilter = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('Completed'),
              value: 'completed',
              groupValue: _statusFilter,
              onChanged: (value) {
                setState(() {
                  _statusFilter = value!;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _dateRange,
    );
    
    if (picked != null) {
      setState(() {
        _dateRange = picked;
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _statusFilter = 'all';
      _dateRange = null;
    });
  }

  void _viewReport(Map<String, dynamic> report) {
    // TODO: Implement report viewing
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing report for ${report['requestNumber']}')),
    );
  }

  void _downloadReport(Map<String, dynamic> report) {
    // TODO: Implement report download
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Downloading report for ${report['requestNumber']}')),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
