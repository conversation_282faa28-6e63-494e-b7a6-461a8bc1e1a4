import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/job_allocation_bloc.dart';
import '../bloc/job_allocation_event.dart';
import '../bloc/job_allocation_state.dart';

class AnalystAssignmentDialog extends StatefulWidget {
  final int jobId;
  final String jobTitle;

  const AnalystAssignmentDialog({
    super.key,
    required this.jobId,
    required this.jobTitle,
  });

  @override
  State<AnalystAssignmentDialog> createState() => _AnalystAssignmentDialogState();
}

class _AnalystAssignmentDialogState extends State<AnalystAssignmentDialog> {
  Map<String, dynamic>? selectedAnalyst;

  @override
  void initState() {
    super.initState();
    // Load analysts when dialog opens
    context.read<JobAllocationBloc>().add(const AnalystsLoadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Assign Analyst to ${widget.jobTitle}'),
      content: SizedBox(
        width: double.maxFinite,
        child: BlocBuilder<JobAllocationBloc, JobAllocationState>(
          builder: (context, state) {
            if (state is AnalystsLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (state is AnalystsError) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load analysts: ${state.failure.message}',
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<JobAllocationBloc>().add(const AnalystsLoadRequested());
                    },
                    child: const Text('Retry'),
                  ),
                ],
              );
            }

            if (state is AnalystsLoaded) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Select an analyst to assign this job:'),
                  const SizedBox(height: 16),
                  Container(
                    constraints: const BoxConstraints(maxHeight: 300),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: state.analysts.length,
                      itemBuilder: (context, index) {
                        final analyst = state.analysts[index];
                        final isSelected = selectedAnalyst?['id'] == analyst['id'];
                        
                        return ListTile(
                          leading: CircleAvatar(
                            child: Text(
                              (analyst['name'] ?? 'A')[0].toUpperCase(),
                            ),
                          ),
                          title: Text(analyst['name'] ?? 'Unknown'),
                          subtitle: Text(analyst['email'] ?? ''),
                          trailing: isSelected 
                            ? const Icon(Icons.check_circle, color: Colors.green)
                            : null,
                          selected: isSelected,
                          onTap: () {
                            setState(() {
                              selectedAnalyst = analyst;
                            });
                          },
                        );
                      },
                    ),
                  ),
                ],
              );
            }

            return const Text('No analysts available');
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: selectedAnalyst != null
            ? () {
                // Trigger job assignment
                context.read<JobAllocationBloc>().add(
                  JobAllocationAssignRequested(
                    jobId: widget.jobId,
                    analystId: selectedAnalyst!['id'],
                  ),
                );
                Navigator.of(context).pop(selectedAnalyst);
              }
            : null,
          child: const Text('Assign'),
        ),
      ],
    );
  }
}

// Extension to show the dialog easily
extension JobAssignmentDialogExtension on BuildContext {
  Future<Map<String, dynamic>?> showAnalystAssignmentDialog({
    required int jobId,
    required String jobTitle,
  }) {
    return showDialog<Map<String, dynamic>>(
      context: this,
      builder: (context) => AnalystAssignmentDialog(
        jobId: jobId,
        jobTitle: jobTitle,
      ),
    );
  }
}
