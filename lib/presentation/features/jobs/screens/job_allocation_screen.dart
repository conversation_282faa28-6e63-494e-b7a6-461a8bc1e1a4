import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lims_app_flutter/domain/entities/job_allocation/job_allocation_entity.dart';
import '../../../core/router/app_routes.dart';
import '../bloc/job_allocation_bloc.dart';
import '../bloc/job_allocation_event.dart';
import '../bloc/job_allocation_state.dart';

class JobAllocationScreen extends StatefulWidget {
  const JobAllocationScreen({super.key});

  @override
  State<JobAllocationScreen> createState() => _JobAllocationScreenState();
}

class _JobAllocationScreenState extends State<JobAllocationScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _statusFilter = 'all';

  final List<Map<String, dynamic>> _analysts = [
    {'name': '<PERSON> Analyst', 'id': 1, 'activeJobs': 2},
    {'name': '<PERSON> Analyst', 'id': 2, 'activeJobs': 1},
    {'name': '<PERSON>', 'id': 3, 'activeJobs': 3},
  ];

  @override
  void initState() {
    super.initState();
    // Load job allocations when screen initializes
    context.read<JobAllocationBloc>().add(
      const JobAllocationsLoadRequested(page: 1, perPage: 20),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Job Allocation'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<JobAllocationBloc>().add(
                JobAllocationsRefreshRequested(search: _searchQuery.isNotEmpty ? _searchQuery : null),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.push(AppRoutes.createJob);
            },
          ),
        ],
      ),
      body: BlocListener<JobAllocationBloc, JobAllocationState>(
        listener: (context, state) {
          if (state is JobAllocationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.failure.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is JobAllocationAssigned) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Job assigned successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        child: Column(
          children: [
            // Search and Filter Section
            Container(
              padding: const EdgeInsets.all(16.0),
              color: Colors.grey[50],
              child: Column(
                children: [
                  // Search Bar
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search by job number, test request, or customer...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = '';
                                });
                                context.read<JobAllocationBloc>().add(
                                  const JobAllocationSearchCleared(),
                                );
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                    onSubmitted: (value) {
                      if (value.isNotEmpty) {
                        context.read<JobAllocationBloc>().add(
                          JobAllocationSearchRequested(query: value),
                        );
                      }
                    },
                  ),
                  const SizedBox(height: 12),

                  // Status Filter
                  Row(
                    children: [
                      const Text('Status: '),
                      const SizedBox(width: 8),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _statusFilter,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          items: const [
                            DropdownMenuItem(value: 'all', child: Text('All')),
                            DropdownMenuItem(value: 'pending', child: Text('Pending Assignment')),
                            DropdownMenuItem(value: 'assigned', child: Text('Assigned')),
                            DropdownMenuItem(value: 'in_progress', child: Text('In Progress')),
                            DropdownMenuItem(value: 'completed', child: Text('Completed')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _statusFilter = value!;
                            });
                            context.read<JobAllocationBloc>().add(
                              JobAllocationFilterByStatusRequested(status: value == 'all' ? null : value),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Jobs List
            Expanded(
              child: BlocBuilder<JobAllocationBloc, JobAllocationState>(
                builder: (context, state) {
                  if (state is JobAllocationLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else if (state is JobAllocationsLoaded) {
                    if (state.jobAllocations.isEmpty) {
                      return _buildEmptyState();
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        context.read<JobAllocationBloc>().add(
                          JobAllocationsRefreshRequested(search: state.currentSearch),
                        );
                      },
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16.0),
                        itemCount: state.jobAllocations.length + (state.paginationMeta.hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == state.jobAllocations.length) {
                            // Load more indicator
                            if (state.isLoadingMore) {
                              return const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(16.0),
                                  child: CircularProgressIndicator(),
                                ),
                              );
                            } else {
                              return Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: ElevatedButton(
                                  onPressed: () {
                                    context.read<JobAllocationBloc>().add(
                                      const JobAllocationsLoadMoreRequested(),
                                    );
                                  },
                                  child: const Text('Load More'),
                                ),
                              );
                            }
                          }

                          final jobAllocation = state.jobAllocations[index];
                          return _buildJobCard(jobAllocation);
                        },
                      ),
                    );
                  } else if (state is JobAllocationError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.red[300],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading job allocations',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.failure.message,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton(
                            onPressed: () {
                              context.read<JobAllocationBloc>().add(
                                const JobAllocationsLoadRequested(),
                              );
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  } else {
                    return _buildEmptyState();
                  }
                },
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push(AppRoutes.createJob);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildJobCard(JobAllocationEntity jobAllocation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          context.push(AppRoutes.jobDetailsWithId(jobAllocation.id));
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'JOB-${jobAllocation.id.toString().padLeft(6, '0')}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      _buildPriorityChip(jobAllocation.priority ?? 'medium'),
                      const SizedBox(width: 8),
                      _buildStatusChip(jobAllocation.status ?? 'pending'),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Test Request: TR-${jobAllocation.testRequestId.toString().padLeft(6, '0')}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Test Request ID: ${jobAllocation.testRequestId}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  // const SizedBox(width: 4),
                  // Text(
                  //   jobAllocation.analystId != null ? 'Analyst ID: ${jobAllocation.analystId}' : 'Unassigned',
                  //   style: TextStyle(
                  //     fontSize: 12,
                  //     color: jobAllocation.analystId != null ? Colors.grey[600] : Colors.red,
                  //     fontWeight: jobAllocation.analystId != null ? FontWeight.normal : FontWeight.w500,
                  //   ),
                  // ),
                  const SizedBox(width: 16),
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Due: ${jobAllocation.dueDate?.toString().split(' ')[0] ?? 'N/A'}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.assignment, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Created: ${jobAllocation.createdAt?.toString().split(' ')[0] ?? 'N/A'}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              if ((jobAllocation.status ?? 'pending') == 'pending') ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _assignJob(jobAllocation),
                      icon: const Icon(Icons.person_add, size: 16),
                      label: const Text('Assign'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;

    switch (status) {
      case 'pending':
        color = Colors.orange;
        label = 'Pending';
        break;
      case 'assigned':
        color = Colors.blue;
        label = 'Assigned';
        break;
      case 'in_progress':
        color = Colors.purple;
        label = 'In Progress';
        break;
      case 'completed':
        color = Colors.green;
        label = 'Completed';
        break;
      default:
        color = Colors.grey;
        label = 'Unknown';
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      ),
      backgroundColor: color,
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    IconData icon;

    switch (priority) {
      case 'high':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        break;
      case 'medium':
        color = Colors.orange;
        icon = Icons.remove;
        break;
      case 'low':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 2),
          Text(
            priority.toUpperCase(),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.work_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No jobs found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first job allocation',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.push(AppRoutes.createJob);
            },
            icon: const Icon(Icons.add),
            label: const Text('Create Job'),
          ),
        ],
      ),
    );
  }

  void _assignJob(dynamic jobAllocation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Assign Job JOB-${jobAllocation.id.toString().padLeft(6, '0')}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select an analyst to assign this job:'),
            const SizedBox(height: 16),
            ..._analysts.map((analyst) => ListTile(
              leading: CircleAvatar(
                child: Text(analyst['name'].split(' ').map((n) => n[0]).join()),
              ),
              title: Text(analyst['name']),
              subtitle: Text('${analyst['activeJobs']} active jobs'),
              onTap: () {
                Navigator.of(context).pop();
                context.read<JobAllocationBloc>().add(
                  JobAllocationAssignRequested(
                    jobId: jobAllocation.id,
                    analystId: analyst['id'],
                  ),
                );
              },
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}