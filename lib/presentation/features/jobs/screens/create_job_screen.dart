import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/widgets/error_text_field.dart';
import '../../../../core/error/failures.dart';

class CreateJobScreen extends StatefulWidget {
  const CreateJobScreen({super.key});

  @override
  State<CreateJobScreen> createState() => _CreateJobScreenState();
}

class _CreateJobScreenState extends State<CreateJobScreen> {
  final _formKey = GlobalKey<FormState>();
  final _jobNumberController = TextEditingController();
  final _dueDateController = TextEditingController();
  final _remarksController = TextEditingController();
  
  String? _selectedTestRequest;
  String? _selectedAnalyst;
  String _selectedPriority = 'medium';
  DateTime? _dueDate;
  bool _isLoading = false;
  Failure? _createJobFailure;

  // Mock data - will be replaced with actual data from BLoC
  final List<Map<String, dynamic>> _availableTestRequests = [
    {
      'id': 1,
      'requestNumber': 'TR-2024-001',
      'customerName': 'ABC Industries',
      'sampleType': 'Water',
      'testCount': 3,
    },
    {
      'id': 2,
      'requestNumber': 'TR-2024-002',
      'customerName': 'XYZ Corp',
      'sampleType': 'Soil',
      'testCount': 5,
    },
  ];

  final List<Map<String, dynamic>> _availableAnalysts = [
    {'id': 1, 'name': 'John Analyst', 'activeJobs': 2},
    {'id': 2, 'name': 'Jane Analyst', 'activeJobs': 1},
    {'id': 3, 'name': 'Mike Technician', 'activeJobs': 3},
  ];

  @override
  void initState() {
    super.initState();
    _generateJobNumber();
  }

  void _generateJobNumber() {
    final now = DateTime.now();
    final jobNumber = 'JOB-${now.year}-${now.millisecondsSinceEpoch.toString().substring(8)}';
    _jobNumberController.text = jobNumber;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Job'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveJob,
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Error Summary
              ValidationErrorSummary(failure: _createJobFailure),

              // Job Number
              ErrorTextField(
                fieldName: 'job_number',
                labelText: 'Job Number',
                controller: _jobNumberController,
                failure: _createJobFailure,
                readOnly: true,
                prefixIcon: const Icon(Icons.numbers),
              ),
              const SizedBox(height: 16),

              // Test Request Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Test Request',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      DropdownButtonFormField<String>(
                        value: _selectedTestRequest,
                        decoration: const InputDecoration(
                          labelText: 'Select Test Request',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.assignment),
                        ),
                        items: _availableTestRequests.map((request) {
                          return DropdownMenuItem<String>(
                            value: request['requestNumber'],
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  request['requestNumber'],
                                  style: const TextStyle(fontWeight: FontWeight.w500),
                                ),
                                Text(
                                  '${request['customerName']} - ${request['sampleType']} (${request['testCount']} tests)',
                                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedTestRequest = value;
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a test request';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Assignment Details
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Assignment Details',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      // Analyst Selection
                      DropdownButtonFormField<String>(
                        value: _selectedAnalyst,
                        decoration: const InputDecoration(
                          labelText: 'Assign to Analyst',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.person),
                        ),
                        items: _availableAnalysts.map((analyst) {
                          return DropdownMenuItem<String>(
                            value: analyst['name'],
                            child: Row(
                              children: [
                                CircleAvatar(
                                  radius: 12,
                                  child: Text(
                                    analyst['name'].split(' ').map((n) => n[0]).join(),
                                    style: const TextStyle(fontSize: 10),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(analyst['name']),
                                      Text(
                                        '${analyst['activeJobs']} active jobs',
                                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedAnalyst = value;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Priority Selection
                      DropdownButtonFormField<String>(
                        value: _selectedPriority,
                        decoration: const InputDecoration(
                          labelText: 'Priority',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.flag),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'low',
                            child: Row(
                              children: [
                                Icon(Icons.keyboard_arrow_down, color: Colors.green),
                                SizedBox(width: 8),
                                Text('Low Priority'),
                              ],
                            ),
                          ),
                          DropdownMenuItem(
                            value: 'medium',
                            child: Row(
                              children: [
                                Icon(Icons.remove, color: Colors.orange),
                                SizedBox(width: 8),
                                Text('Medium Priority'),
                              ],
                            ),
                          ),
                          DropdownMenuItem(
                            value: 'high',
                            child: Row(
                              children: [
                                Icon(Icons.keyboard_arrow_up, color: Colors.red),
                                SizedBox(width: 8),
                                Text('High Priority'),
                              ],
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPriority = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Due Date
                      TextFormField(
                        controller: _dueDateController,
                        decoration: const InputDecoration(
                          labelText: 'Due Date',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                          suffixIcon: Icon(Icons.arrow_drop_down),
                        ),
                        readOnly: true,
                        onTap: _selectDueDate,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a due date';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Additional Information
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Additional Information',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ErrorTextField(
                        fieldName: 'remarks',
                        labelText: 'Remarks (Optional)',
                        controller: _remarksController,
                        failure: _createJobFailure,
                        maxLines: 3,
                        prefixIcon: const Icon(Icons.note),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Create Button
              ElevatedButton(
                onPressed: _isLoading ? null : _saveJob,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text(
                        'Create Job',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectDueDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (picked != null) {
      setState(() {
        _dueDate = picked;
        _dueDateController.text = '${picked.day}/${picked.month}/${picked.year}';
      });
    }
  }

  void _saveJob() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _createJobFailure = null;
    });

    try {
      // TODO: Implement actual job creation with BLoC
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Job created successfully')),
        );
        context.pop();
      }
    } catch (e) {
      // TODO: Handle actual creation errors
      setState(() {
        _createJobFailure = const ValidationFailure(
          message: 'Failed to create job',
          fieldErrors: {
            'job_number': ['Job number already exists'],
          },
        );
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _jobNumberController.dispose();
    _dueDateController.dispose();
    _remarksController.dispose();
    super.dispose();
  }
}
