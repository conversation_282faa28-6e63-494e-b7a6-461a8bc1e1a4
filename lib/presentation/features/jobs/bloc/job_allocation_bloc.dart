import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../domain/usecases/job_allocation/get_job_allocations_usecase.dart';
import '../../../../domain/usecases/job_allocation/get_job_allocation_by_id_usecase.dart';
import '../../../../domain/usecases/job_allocation/create_job_allocation_usecase.dart';
import '../../../../domain/usecases/job_allocation/update_job_allocation_usecase.dart';
import '../../../../domain/usecases/job_allocation/delete_job_allocation_usecase.dart';
import '../../../../domain/usecases/job_allocation/get_analysts_usecase.dart';
import '../../../../core/usecases/usecase.dart';
import 'job_allocation_event.dart';
import 'job_allocation_state.dart';

class JobAllocationBloc extends Bloc<JobAllocationEvent, JobAllocationState> {
  final GetJobAllocationsUseCase _getJobAllocationsUseCase;
  final GetJobAllocationByIdUseCase _getJobAllocationByIdUseCase;
  final CreateJobAllocationUseCase _createJobAllocationUseCase;
  final UpdateJobAllocationUseCase _updateJobAllocationUseCase;
  final DeleteJobAllocationUseCase _deleteJobAllocationUseCase;
  final GetAnalystsUseCase _getAnalystsUseCase;

  JobAllocationBloc({
    required GetJobAllocationsUseCase getJobAllocationsUseCase,
    required GetJobAllocationByIdUseCase getJobAllocationByIdUseCase,
    required CreateJobAllocationUseCase createJobAllocationUseCase,
    required UpdateJobAllocationUseCase updateJobAllocationUseCase,
    required DeleteJobAllocationUseCase deleteJobAllocationUseCase,
    required GetAnalystsUseCase getAnalystsUseCase,
  })  : _getJobAllocationsUseCase = getJobAllocationsUseCase,
        _getJobAllocationByIdUseCase = getJobAllocationByIdUseCase,
        _createJobAllocationUseCase = createJobAllocationUseCase,
        _updateJobAllocationUseCase = updateJobAllocationUseCase,
        _deleteJobAllocationUseCase = deleteJobAllocationUseCase,
        _getAnalystsUseCase = getAnalystsUseCase,
        super(const JobAllocationInitial()) {
    on<JobAllocationsLoadRequested>(_onJobAllocationsLoadRequested);
    on<JobAllocationsRefreshRequested>(_onJobAllocationsRefreshRequested);
    on<JobAllocationsLoadMoreRequested>(_onJobAllocationsLoadMoreRequested);
    on<JobAllocationLoadRequested>(_onJobAllocationLoadRequested);
    on<JobAllocationCreateRequested>(_onJobAllocationCreateRequested);
    on<JobAllocationUpdateRequested>(_onJobAllocationUpdateRequested);
    on<JobAllocationDeleteRequested>(_onJobAllocationDeleteRequested);
    on<JobAllocationClearError>(_onJobAllocationClearError);
    on<JobAllocationSearchRequested>(_onJobAllocationSearchRequested);
    on<JobAllocationSearchCleared>(_onJobAllocationSearchCleared);
    on<JobAllocationFilterByStatusRequested>(_onJobAllocationFilterByStatusRequested);
    on<JobAllocationFilterByAnalystRequested>(_onJobAllocationFilterByAnalystRequested);
    on<JobAllocationAssignRequested>(_onJobAllocationAssignRequested);
    on<JobAllocationUnassignRequested>(_onJobAllocationUnassignRequested);
    on<JobAllocationStatusUpdateRequested>(_onJobAllocationStatusUpdateRequested);
    on<AnalystsLoadRequested>(_onAnalystsLoadRequested);
  }

  Future<void> _onJobAllocationsLoadRequested(
    JobAllocationsLoadRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationLoading());

    final result = await _getJobAllocationsUseCase(
      GetJobAllocationsParams(
        page: event.page,
        perPage: event.perPage,
        search: event.search,
      ),
    );

    result.fold(
      (failure) => emit(JobAllocationError(failure: failure)),
      (paginatedResult) => emit(JobAllocationsLoaded(
        jobAllocations: paginatedResult.items,
        paginationMeta: paginatedResult.pagination,
        currentSearch: event.search,
      )),
    );
  }

  Future<void> _onJobAllocationsRefreshRequested(
    JobAllocationsRefreshRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationLoading());

    final result = await _getJobAllocationsUseCase(
      GetJobAllocationsParams(
        page: 1,
        perPage: 15,
        search: event.search,
      ),
    );

    result.fold(
      (failure) => emit(JobAllocationError(failure: failure)),
      (paginatedResult) => emit(JobAllocationsLoaded(
        jobAllocations: paginatedResult.items,
        paginationMeta: paginatedResult.pagination,
        currentSearch: event.search,
      )),
    );
  }

  Future<void> _onJobAllocationsLoadMoreRequested(
    JobAllocationsLoadMoreRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    if (state is JobAllocationsLoaded) {
      final currentState = state as JobAllocationsLoaded;
      
      if (!currentState.paginationMeta.hasMore || currentState.isLoadingMore) {
        return;
      }

      emit(currentState.copyWith(isLoadingMore: true));

      final result = await _getJobAllocationsUseCase(
        GetJobAllocationsParams(
          page: currentState.paginationMeta.currentPage + 1,
          perPage: currentState.paginationMeta.perPage,
          search: currentState.currentSearch,
        ),
      );

      result.fold(
        (failure) => emit(JobAllocationError(failure: failure)),
        (paginatedResult) => emit(JobAllocationsLoaded(
          jobAllocations: [...currentState.jobAllocations, ...paginatedResult.items],
          paginationMeta: paginatedResult.pagination,
          currentSearch: currentState.currentSearch,
          statusFilter: currentState.statusFilter,
          analystFilter: currentState.analystFilter,
          isLoadingMore: false,
        )),
      );
    }
  }

  Future<void> _onJobAllocationLoadRequested(
    JobAllocationLoadRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationOperationLoading(operation: 'loading_job_allocation'));

    final result = await _getJobAllocationByIdUseCase(
      GetJobAllocationByIdParams(id: event.id),
    );

    result.fold(
      (failure) => emit(JobAllocationError(failure: failure)),
      (jobAllocation) => emit(JobAllocationLoaded(jobAllocation: jobAllocation)),
    );
  }

  Future<void> _onJobAllocationCreateRequested(
    JobAllocationCreateRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationOperationLoading(operation: 'creating_job_allocation'));

    final result = await _createJobAllocationUseCase(
      CreateJobAllocationParams(jobAllocation: event.jobAllocation),
    );

    result.fold(
      (failure) => emit(JobAllocationError(failure: failure)),
      (jobAllocation) => emit(JobAllocationCreated(jobAllocation: jobAllocation)),
    );
  }

  Future<void> _onJobAllocationUpdateRequested(
    JobAllocationUpdateRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationOperationLoading(operation: 'updating_job_allocation'));

    final result = await _updateJobAllocationUseCase(
      UpdateJobAllocationParams(
        id: event.id,
        jobAllocation: event.jobAllocation,
      ),
    );

    result.fold(
      (failure) => emit(JobAllocationError(failure: failure)),
      (jobAllocation) => emit(JobAllocationUpdated(jobAllocation: jobAllocation)),
    );
  }

  Future<void> _onJobAllocationDeleteRequested(
    JobAllocationDeleteRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationOperationLoading(operation: 'deleting_job_allocation'));

    final result = await _deleteJobAllocationUseCase(
      DeleteJobAllocationParams(id: event.id),
    );

    result.fold(
      (failure) => emit(JobAllocationError(failure: failure)),
      (_) => emit(JobAllocationDeleted(deletedId: event.id)),
    );
  }

  void _onJobAllocationClearError(
    JobAllocationClearError event,
    Emitter<JobAllocationState> emit,
  ) {
    if (state is JobAllocationError) {
      emit(const JobAllocationInitial());
    }
  }

  Future<void> _onJobAllocationSearchRequested(
    JobAllocationSearchRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationLoading());

    final result = await _getJobAllocationsUseCase(
      GetJobAllocationsParams(
        page: 1,
        perPage: 15,
        search: event.query,
      ),
    );

    result.fold(
      (failure) => emit(JobAllocationError(failure: failure)),
      (paginatedResult) => emit(JobAllocationsLoaded(
        jobAllocations: paginatedResult.items,
        paginationMeta: paginatedResult.pagination,
        currentSearch: event.query,
      )),
    );
  }

  Future<void> _onJobAllocationSearchCleared(
    JobAllocationSearchCleared event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationLoading());

    final result = await _getJobAllocationsUseCase(
      const GetJobAllocationsParams(page: 1, perPage: 15),
    );

    result.fold(
      (failure) => emit(JobAllocationError(failure: failure)),
      (paginatedResult) => emit(JobAllocationsLoaded(
        jobAllocations: paginatedResult.items,
        paginationMeta: paginatedResult.pagination,
        currentSearch: null,
      )),
    );
  }

  Future<void> _onJobAllocationFilterByStatusRequested(
    JobAllocationFilterByStatusRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    if (state is JobAllocationsLoaded) {
      final currentState = state as JobAllocationsLoaded;
      emit(currentState.copyWith(statusFilter: event.status));
      
      // Reload with filter
      add(JobAllocationsRefreshRequested(search: currentState.currentSearch));
    }
  }

  Future<void> _onJobAllocationFilterByAnalystRequested(
    JobAllocationFilterByAnalystRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    if (state is JobAllocationsLoaded) {
      final currentState = state as JobAllocationsLoaded;
      emit(currentState.copyWith(analystFilter: event.analystId));
      
      // Reload with filter
      add(JobAllocationsRefreshRequested(search: currentState.currentSearch));
    }
  }

  Future<void> _onJobAllocationAssignRequested(
    JobAllocationAssignRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationOperationLoading(operation: 'assigning_job'));

    // This would typically call a specific assign use case
    // For now, we'll use the update use case with modified entity
    // In a real implementation, you'd have a dedicated AssignJobUseCase
    
    // Load current job allocation first
    final loadResult = await _getJobAllocationByIdUseCase(
      GetJobAllocationByIdParams(id: event.jobId),
    );

    await loadResult.fold(
      (failure) async => emit(JobAllocationError(failure: failure)),
      (jobAllocation) async {
        // Create updated entity with new analyst assignment
        // This is a simplified approach - in practice you'd have proper assignment logic
        final result = await _updateJobAllocationUseCase(
          UpdateJobAllocationParams(
            id: event.jobId,
            jobAllocation: jobAllocation, // Would be modified with analyst assignment
          ),
        );

        result.fold(
          (failure) => emit(JobAllocationError(failure: failure)),
          (updatedJobAllocation) => emit(JobAllocationAssigned(jobAllocation: updatedJobAllocation)),
        );
      },
    );
  }

  Future<void> _onJobAllocationUnassignRequested(
    JobAllocationUnassignRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationOperationLoading(operation: 'unassigning_job'));

    // Similar to assign, this would use a dedicated unassign use case
    final loadResult = await _getJobAllocationByIdUseCase(
      GetJobAllocationByIdParams(id: event.jobId),
    );

    await loadResult.fold(
      (failure) async => emit(JobAllocationError(failure: failure)),
      (jobAllocation) async {
        final result = await _updateJobAllocationUseCase(
          UpdateJobAllocationParams(
            id: event.jobId,
            jobAllocation: jobAllocation, // Would be modified to remove analyst assignment
          ),
        );

        result.fold(
          (failure) => emit(JobAllocationError(failure: failure)),
          (updatedJobAllocation) => emit(JobAllocationUnassigned(jobAllocation: updatedJobAllocation)),
        );
      },
    );
  }

  Future<void> _onJobAllocationStatusUpdateRequested(
    JobAllocationStatusUpdateRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const JobAllocationOperationLoading(operation: 'updating_job_status'));

    // Load current job allocation and update status
    final loadResult = await _getJobAllocationByIdUseCase(
      GetJobAllocationByIdParams(id: event.jobId),
    );

    await loadResult.fold(
      (failure) async => emit(JobAllocationError(failure: failure)),
      (jobAllocation) async {
        final result = await _updateJobAllocationUseCase(
          UpdateJobAllocationParams(
            id: event.jobId,
            jobAllocation: jobAllocation, // Would be modified with new status
          ),
        );

        result.fold(
          (failure) => emit(JobAllocationError(failure: failure)),
          (updatedJobAllocation) => emit(JobAllocationStatusUpdated(jobAllocation: updatedJobAllocation)),
        );
      },
    );
  }

  Future<void> _onAnalystsLoadRequested(
    AnalystsLoadRequested event,
    Emitter<JobAllocationState> emit,
  ) async {
    emit(const AnalystsLoading());

    final result = await _getAnalystsUseCase(NoParams());

    result.fold(
      (failure) => emit(AnalystsError(failure: failure)),
      (analysts) => emit(AnalystsLoaded(analysts: analysts)),
    );
  }
}
