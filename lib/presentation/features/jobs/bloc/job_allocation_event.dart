import 'package:equatable/equatable.dart';
import '../../../../domain/entities/job_allocation/job_allocation_entity.dart';

abstract class JobAllocationEvent extends Equatable {
  const JobAllocationEvent();

  @override
  List<Object?> get props => [];
}

// Get job allocations with pagination and search
class JobAllocationsLoadRequested extends JobAllocationEvent {
  final int page;
  final int perPage;
  final String? search;

  const JobAllocationsLoadRequested({
    this.page = 1,
    this.perPage = 15,
    this.search,
  });

  @override
  List<Object?> get props => [page, perPage, search];
}

// Refresh job allocations (reset to first page)
class JobAllocationsRefreshRequested extends JobAllocationEvent {
  final String? search;

  const JobAllocationsRefreshRequested({this.search});

  @override
  List<Object?> get props => [search];
}

// Load more job allocations (pagination)
class JobAllocationsLoadMoreRequested extends JobAllocationEvent {
  const JobAllocationsLoadMoreRequested();
}

// Get specific job allocation by ID
class JobAllocationLoadRequested extends JobAllocationEvent {
  final int id;

  const JobAllocationLoadRequested({required this.id});

  @override
  List<Object> get props => [id];
}

// Create new job allocation
class JobAllocationCreateRequested extends JobAllocationEvent {
  final JobAllocationEntity jobAllocation;

  const JobAllocationCreateRequested({required this.jobAllocation});

  @override
  List<Object> get props => [jobAllocation];
}

// Update job allocation
class JobAllocationUpdateRequested extends JobAllocationEvent {
  final int id;
  final JobAllocationEntity jobAllocation;

  const JobAllocationUpdateRequested({
    required this.id,
    required this.jobAllocation,
  });

  @override
  List<Object> get props => [id, jobAllocation];
}

// Delete job allocation
class JobAllocationDeleteRequested extends JobAllocationEvent {
  final int id;

  const JobAllocationDeleteRequested({required this.id});

  @override
  List<Object> get props => [id];
}

// Clear errors
class JobAllocationClearError extends JobAllocationEvent {
  const JobAllocationClearError();
}

// Search job allocations
class JobAllocationSearchRequested extends JobAllocationEvent {
  final String query;

  const JobAllocationSearchRequested({required this.query});

  @override
  List<Object> get props => [query];
}

// Clear search
class JobAllocationSearchCleared extends JobAllocationEvent {
  const JobAllocationSearchCleared();
}

// Filter by status
class JobAllocationFilterByStatusRequested extends JobAllocationEvent {
  final String? status;

  const JobAllocationFilterByStatusRequested({this.status});

  @override
  List<Object?> get props => [status];
}

// Filter by analyst
class JobAllocationFilterByAnalystRequested extends JobAllocationEvent {
  final int? analystId;

  const JobAllocationFilterByAnalystRequested({this.analystId});

  @override
  List<Object?> get props => [analystId];
}

// Assign job to analyst
class JobAllocationAssignRequested extends JobAllocationEvent {
  final int jobId;
  final int analystId;

  const JobAllocationAssignRequested({
    required this.jobId,
    required this.analystId,
  });

  @override
  List<Object> get props => [jobId, analystId];
}

// Unassign job from analyst
class JobAllocationUnassignRequested extends JobAllocationEvent {
  final int jobId;

  const JobAllocationUnassignRequested({required this.jobId});

  @override
  List<Object> get props => [jobId];
}

// Update job status
class JobAllocationStatusUpdateRequested extends JobAllocationEvent {
  final int jobId;
  final String status;

  const JobAllocationStatusUpdateRequested({
    required this.jobId,
    required this.status,
  });

  @override
  List<Object> get props => [jobId, status];
}

// Load analysts for assignment
class AnalystsLoadRequested extends JobAllocationEvent {
  const AnalystsLoadRequested();

  @override
  List<Object> get props => [];
}
