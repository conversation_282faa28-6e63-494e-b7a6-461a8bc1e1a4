import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/analyst_bloc.dart';
import '../bloc/analyst_event.dart';
import '../bloc/analyst_state.dart';
import '../widgets/analyst_test_card.dart';
import '../widgets/analyst_progress_summary.dart';

class AnalystTestsScreen extends StatefulWidget {
  const AnalystTestsScreen({super.key});

  @override
  State<AnalystTestsScreen> createState() => _AnalystTestsScreenState();
}

class _AnalystTestsScreenState extends State<AnalystTestsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<AnalystBloc>().add(const AnalystTestsLoadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Tests'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: BlocConsumer<AnalystBloc, AnalystState>(
        listener: (context, state) {
          if (state is AnalystTestResultSubmitted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Test result submitted successfully'),
                backgroundColor: Colors.green,
              ),
            );
            // Refresh the tests list
            context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
          }

          if (state is AnalystTestRejected) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Test rejected successfully'),
                backgroundColor: Colors.orange,
              ),
            );
            // Refresh the tests list
            context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
          }

          if (state is AnalystTestResultSubmissionError ||
              state is AnalystTestRejectionError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state is AnalystTestResultSubmissionError
                      ? state.failure.message
                      : (state as AnalystTestRejectionError).failure.message,
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is AnalystLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is AnalystError) {
            return _buildErrorWidget(context, state.failure.message);
          }

          if (state is AnalystTestsLoaded) {
            if (state.analystTests.isEmpty) {
              return _buildEmptyState();
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
              },
              child: CustomScrollView(
                slivers: [
                  // Progress Summary
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: AnalystProgressSummary(analystTests: state.analystTests),
                    ),
                  ),

                  // Tests List
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final analystTest = state.analystTests[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12.0),
                            child: AnalystTestCard(
                              analystTest: analystTest,
                              onSubmitResult: (testId, request) {
                                context.read<AnalystBloc>().add(
                                  AnalystTestResultSubmitRequested(
                                    testId: testId,
                                    request: request,
                                  ),
                                );
                              },
                              onRejectTest: (testId, request) {
                                context.read<AnalystBloc>().add(
                                  AnalystTestRejectRequested(
                                    testId: testId,
                                    request: request,
                                  ),
                                );
                              },
                            ),
                          );
                        },
                        childCount: state.analystTests.length,
                      ),
                    ),
                  ),

                  // Bottom padding
                  const SliverToBoxAdapter(
                    child: SizedBox(height: 16),
                  ),
                ],
              ),
            );
          }

          // Show loading overlay for submission/rejection
          if (state is AnalystTestResultSubmitting || state is AnalystTestRejecting) {
            return Stack(
              children: [
                // Previous content (if available)
                if (state is AnalystTestsLoaded)
                  RefreshIndicator(
                    onRefresh: () async {
                      context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
                    },
                    child: CustomScrollView(
                      slivers: [
                        SliverPadding(
                          padding: const EdgeInsets.all(16.0),
                          sliver: SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final analystTest = state.analystTests[index];
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 12.0),
                                  child: AnalystTestCard(
                                    analystTest: analystTest,
                                    onSubmitResult: null, // Disable during loading
                                    onRejectTest: null, // Disable during loading
                                  ),
                                );
                              },
                              childCount: state.analystTests.length,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  const SizedBox.shrink(),

                // Loading overlay
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: Card(
                      child: Padding(
                        padding: EdgeInsets.all(20.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('Processing...'),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          }

          return _buildErrorWidget(context, 'Unable to load your tests.');
        },
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Tests Assigned',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You have no tests assigned at the moment.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
