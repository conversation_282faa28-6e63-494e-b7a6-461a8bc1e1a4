import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/empty_state_widget.dart';
import '../bloc/analyst_bloc.dart';
import '../bloc/analyst_event.dart';
import '../bloc/analyst_state.dart';
import '../widgets/analyst_test_card.dart';
import '../widgets/analyst_progress_summary.dart';

class AnalystTestsScreen extends StatefulWidget {
  const AnalystTestsScreen({super.key});

  @override
  State<AnalystTestsScreen> createState() => _AnalystTestsScreenState();
}

class _AnalystTestsScreenState extends State<AnalystTestsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<AnalystBloc>().add(const AnalystTestsLoadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'My Tests',
        showBackButton: true,
      ),
      body: BlocConsumer<AnalystBloc, AnalystState>(
        listener: (context, state) {
          if (state is AnalystTestResultSubmitted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Test result submitted successfully'),
                backgroundColor: Colors.green,
              ),
            );
            // Refresh the tests list
            context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
          }

          if (state is AnalystTestRejected) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Test rejected successfully'),
                backgroundColor: Colors.orange,
              ),
            );
            // Refresh the tests list
            context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
          }

          if (state is AnalystTestResultSubmissionError ||
              state is AnalystTestRejectionError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state is AnalystTestResultSubmissionError
                      ? state.failure.message
                      : (state as AnalystTestRejectionError).failure.message,
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is AnalystLoading) {
            return const LoadingWidget();
          }

          if (state is AnalystError) {
            return CustomErrorWidget(
              message: state.failure.message,
              onRetry: () {
                context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
              },
            );
          }

          if (state is AnalystTestsLoaded) {
            if (state.analystTests.isEmpty) {
              return const EmptyStateWidget(
                title: 'No Tests Assigned',
                message: 'You have no tests assigned at the moment.',
                icon: Icons.assignment_outlined,
              );
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
              },
              child: CustomScrollView(
                slivers: [
                  // Progress Summary
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: AnalystProgressSummary(analystTests: state.analystTests),
                    ),
                  ),

                  // Tests List
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final analystTest = state.analystTests[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12.0),
                            child: AnalystTestCard(
                              analystTest: analystTest,
                              onSubmitResult: (testId, request) {
                                context.read<AnalystBloc>().add(
                                  AnalystTestResultSubmitRequested(
                                    testId: testId,
                                    request: request,
                                  ),
                                );
                              },
                              onRejectTest: (testId, request) {
                                context.read<AnalystBloc>().add(
                                  AnalystTestRejectRequested(
                                    testId: testId,
                                    request: request,
                                  ),
                                );
                              },
                            ),
                          );
                        },
                        childCount: state.analystTests.length,
                      ),
                    ),
                  ),

                  // Bottom padding
                  const SliverToBoxAdapter(
                    child: SizedBox(height: 16),
                  ),
                ],
              ),
            );
          }

          // Show loading overlay for submission/rejection
          if (state is AnalystTestResultSubmitting || state is AnalystTestRejecting) {
            return Stack(
              children: [
                // Previous content (if available)
                if (state is AnalystTestsLoaded)
                  RefreshIndicator(
                    onRefresh: () async {
                      context.read<AnalystBloc>().add(const AnalystTestsRefreshRequested());
                    },
                    child: CustomScrollView(
                      slivers: [
                        SliverPadding(
                          padding: const EdgeInsets.all(16.0),
                          sliver: SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final analystTest = state.analystTests[index];
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 12.0),
                                  child: AnalystTestCard(
                                    analystTest: analystTest,
                                    onSubmitResult: null, // Disable during loading
                                    onRejectTest: null, // Disable during loading
                                  ),
                                );
                              },
                              childCount: state.analystTests.length,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  const SizedBox.shrink(),

                // Loading overlay
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: Card(
                      child: Padding(
                        padding: EdgeInsets.all(20.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('Processing...'),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          }

          return const EmptyStateWidget(
            title: 'Something went wrong',
            message: 'Unable to load your tests.',
            icon: Icons.error_outline,
          );
        },
      ),
    );
  }
}
