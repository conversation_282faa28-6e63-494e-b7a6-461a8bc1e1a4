import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/analyst/analyst_test_entity.dart';
import '../../../../domain/entities/completed_test/completed_test_entity.dart';

abstract class AnalystState extends Equatable {
  const AnalystState();

  @override
  List<Object?> get props => [];
}

class AnalystInitial extends AnalystState {
  const AnalystInitial();
}

class AnalystLoading extends AnalystState {
  const AnalystLoading();
}

class AnalystTestsLoaded extends AnalystState {
  final List<AnalystTestEntity> analystTests;

  const AnalystTestsLoaded({
    required this.analystTests,
  });

  @override
  List<Object> get props => [analystTests];

  AnalystTestsLoaded copyWith({
    List<AnalystTestEntity>? analystTests,
  }) {
    return AnalystTestsLoaded(
      analystTests: analystTests ?? this.analystTests,
    );
  }
}

class AnalystError extends AnalystState {
  final Failure failure;

  const AnalystError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Test Result Submission States
class AnalystTestResultSubmitting extends AnalystState {
  const AnalystTestResultSubmitting();
}

class AnalystTestResultSubmitted extends AnalystState {
  final CompletedTestEntity submittedTest;

  const AnalystTestResultSubmitted({
    required this.submittedTest,
  });

  @override
  List<Object> get props => [submittedTest];
}

class AnalystTestResultSubmissionError extends AnalystState {
  final Failure failure;

  const AnalystTestResultSubmissionError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Test Rejection States
class AnalystTestRejecting extends AnalystState {
  const AnalystTestRejecting();
}

class AnalystTestRejected extends AnalystState {
  const AnalystTestRejected();
}

class AnalystTestRejectionError extends AnalystState {
  final Failure failure;

  const AnalystTestRejectionError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}
