import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../domain/usecases/analyst/get_my_tests_usecase.dart';
import '../../../../domain/usecases/analyst/submit_test_result_usecase.dart';
import '../../../../domain/usecases/analyst/reject_test_usecase.dart';
import 'analyst_event.dart';
import 'analyst_state.dart';

class AnalystBloc extends Bloc<AnalystEvent, AnalystState> {
  final GetMyTestsUseCase _getMyTestsUseCase;
  final SubmitTestResultUseCase _submitTestResultUseCase;
  final RejectTestUseCase _rejectTestUseCase;

  AnalystBloc({
    required GetMyTestsUseCase getMyTestsUseCase,
    required SubmitTestResultUseCase submitTestResultUseCase,
    required RejectTestUseCase rejectTestUseCase,
  })  : _getMyTestsUseCase = getMyTestsUseCase,
        _submitTestResultUseCase = submitTestResultUseCase,
        _rejectTestUseCase = rejectTestUseCase,
        super(const AnalystInitial()) {
    on<AnalystTestsLoadRequested>(_onLoadRequested);
    on<AnalystTestsRefreshRequested>(_onRefreshRequested);
    on<AnalystTestResultSubmitRequested>(_onSubmitRequested);
    on<AnalystTestRejectRequested>(_onRejectRequested);
    on<AnalystClearError>(_onClearError);
    on<AnalystClearSuccess>(_onClearSuccess);
  }

  Future<void> _onLoadRequested(
    AnalystTestsLoadRequested event,
    Emitter<AnalystState> emit,
  ) async {
    if (event.refresh || state is AnalystInitial) {
      emit(const AnalystLoading());
    }

    final result = await _getMyTestsUseCase(const NoParams());

    result.fold(
      (failure) => emit(AnalystError(failure: failure)),
      (analystTests) => emit(AnalystTestsLoaded(analystTests: analystTests)),
    );
  }

  Future<void> _onRefreshRequested(
    AnalystTestsRefreshRequested event,
    Emitter<AnalystState> emit,
  ) async {
    add(const AnalystTestsLoadRequested(refresh: true));
  }

  Future<void> _onSubmitRequested(
    AnalystTestResultSubmitRequested event,
    Emitter<AnalystState> emit,
  ) async {
    emit(const AnalystTestResultSubmitting());

    final result = await _submitTestResultUseCase(
      SubmitTestResultParams(
        testId: event.testId,
        request: event.request,
      ),
    );

    result.fold(
      (failure) => emit(AnalystTestResultSubmissionError(failure: failure)),
      (submittedTest) => emit(AnalystTestResultSubmitted(submittedTest: submittedTest)),
    );
  }

  Future<void> _onRejectRequested(
    AnalystTestRejectRequested event,
    Emitter<AnalystState> emit,
  ) async {
    emit(const AnalystTestRejecting());

    final result = await _rejectTestUseCase(
      RejectTestParams(
        testId: event.testId,
        request: event.request,
      ),
    );

    result.fold(
      (failure) => emit(AnalystTestRejectionError(failure: failure)),
      (_) => emit(const AnalystTestRejected()),
    );
  }

  void _onClearError(
    AnalystClearError event,
    Emitter<AnalystState> emit,
  ) {
    if (state is AnalystError || 
        state is AnalystTestResultSubmissionError || 
        state is AnalystTestRejectionError) {
      emit(const AnalystInitial());
    }
  }

  void _onClearSuccess(
    AnalystClearSuccess event,
    Emitter<AnalystState> emit,
  ) {
    if (state is AnalystTestResultSubmitted || state is AnalystTestRejected) {
      emit(const AnalystInitial());
    }
  }
}
