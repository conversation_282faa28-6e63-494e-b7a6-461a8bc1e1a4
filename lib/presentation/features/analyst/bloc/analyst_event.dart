import 'package:equatable/equatable.dart';
import '../../../../domain/entities/analyst/analyst_test_entity.dart';

abstract class AnalystEvent extends Equatable {
  const AnalystEvent();

  @override
  List<Object?> get props => [];
}

// Load analyst's assigned tests
class AnalystTestsLoadRequested extends AnalystEvent {
  final bool refresh;

  const AnalystTestsLoadRequested({
    this.refresh = false,
  });

  @override
  List<Object> get props => [refresh];
}

// Refresh analyst tests
class AnalystTestsRefreshRequested extends AnalystEvent {
  const AnalystTestsRefreshRequested();
}

// Submit test result
class AnalystTestResultSubmitRequested extends AnalystEvent {
  final int testId;
  final SubmitTestResultRequestEntity request;

  const AnalystTestResultSubmitRequested({
    required this.testId,
    required this.request,
  });

  @override
  List<Object> get props => [testId, request];
}

// Reject test
class AnalystTestRejectRequested extends AnalystEvent {
  final int testId;
  final RejectTestRequestEntity request;

  const AnalystTestRejectRequested({
    required this.testId,
    required this.request,
  });

  @override
  List<Object> get props => [testId, request];
}

// Clear error state
class AnalystClearError extends AnalystEvent {
  const AnalystClearError();
}

// Clear success state
class AnalystClearSuccess extends AnalystEvent {
  const AnalystClearSuccess();
}
