import 'package:flutter/material.dart';
import '../../../../domain/entities/analyst/analyst_test_entity.dart';

class AnalystProgressSummary extends StatelessWidget {
  final List<AnalystTestEntity> analystTests;

  const AnalystProgressSummary({
    super.key,
    required this.analystTests,
  });

  @override
  Widget build(BuildContext context) {
    final totalTests = analystTests.length;
    final completedTests = analystTests.where((test) => test.isFullyCompleted).length;
    final inProgressTests = analystTests.where((test) => 
        test.completionPercentage > 0 && !test.isFullyCompleted).length;
    final notStartedTests = analystTests.where((test) => 
        test.completionPercentage == 0).length;
    final testsWithRejections = analystTests.where((test) => 
        test.hasRejectedParameters).length;

    final overallProgress = totalTests > 0 
        ? analystTests.map((test) => test.completionPercentage).reduce((a, b) => a + b) / totalTests
        : 0.0;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'My Progress Summary',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Overall Progress
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Overall Progress',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: overallProgress / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor(overallProgress)),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '${overallProgress.toInt()}%',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getProgressColor(overallProgress),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Test Status Grid
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    context,
                    'Total Tests',
                    totalTests.toString(),
                    Icons.assignment_outlined,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    context,
                    'Completed',
                    completedTests.toString(),
                    Icons.check_circle_outline,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    context,
                    'In Progress',
                    inProgressTests.toString(),
                    Icons.pending_outlined,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    context,
                    'Not Started',
                    notStartedTests.toString(),
                    Icons.radio_button_unchecked,
                    Colors.grey,
                  ),
                ),
              ],
            ),

            // Rejections Alert (if any)
            if (testsWithRejections > 0)
              Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warning_outlined,
                        color: Colors.red[600],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '$testsWithRejections test${testsWithRejections > 1 ? 's have' : ' has'} rejected parameters that need attention',
                          style: TextStyle(
                            color: Colors.red[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Quick Actions
            if (totalTests > 0)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Row(
                  children: [
                    if (inProgressTests > 0 || notStartedTests > 0)
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // Navigate to first incomplete test
                          },
                          icon: const Icon(Icons.play_arrow, size: 18),
                          label: const Text('Continue Work'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    if (testsWithRejections > 0)
                      Padding(
                        padding: EdgeInsets.only(
                          left: (inProgressTests > 0 || notStartedTests > 0) ? 8.0 : 0,
                        ),
                        child: Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              // Navigate to tests with rejections
                            },
                            icon: const Icon(Icons.warning_outlined, size: 18),
                            label: const Text('Review Rejections'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.red,
                              side: const BorderSide(color: Colors.red),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getProgressColor(double progress) {
    if (progress >= 100) {
      return Colors.green;
    } else if (progress >= 75) {
      return Colors.lightGreen;
    } else if (progress >= 50) {
      return Colors.blue;
    } else if (progress >= 25) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
