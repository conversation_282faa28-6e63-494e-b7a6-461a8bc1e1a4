import 'package:flutter/material.dart';
import '../../../../domain/entities/analyst/analyst_test_entity.dart';

class AnalystTestCard extends StatelessWidget {
  final AnalystTestEntity analystTest;
  final Function(int testId, SubmitTestResultRequestEntity request)? onSubmitResult;
  final Function(int testId, RejectTestRequestEntity request)? onRejectTest;

  const AnalystTestCard({
    super.key,
    required this.analystTest,
    this.onSubmitResult,
    this.onRejectTest,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Test ID: ${analystTest.id}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _buildProgressChip(),
              ],
            ),
            const SizedBox(height: 8),

            // Test Details
            _buildDetailRow(
              icon: Icons.science_outlined,
              label: 'Nature',
              value: analystTest.nature,
            ),
            const SizedBox(height: 4),

            _buildDetailRow(
              icon: Icons.numbers_outlined,
              label: 'Code',
              value: analystTest.codeNumber,
            ),
            const SizedBox(height: 4),

            if (analystTest.dueDate != null)
              _buildDetailRow(
                icon: Icons.schedule_outlined,
                label: 'Due Date',
                value: _formatDate(analystTest.dueDate!),
                isOverdue: analystTest.dueDate!.isBefore(DateTime.now()),
              ),
            const SizedBox(height: 8),

            // Progress Information
            if (analystTest.totalParameters > 0)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Progress',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${analystTest.completedParameters}/${analystTest.totalParameters}',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _getProgressColor(),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: analystTest.completionPercentage / 100,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor()),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        if (analystTest.pendingParameters > 0)
                          _buildStatusChip(
                            'Pending: ${analystTest.pendingParameters}',
                            Colors.orange,
                          ),
                        if (analystTest.rejectedParameters > 0)
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: _buildStatusChip(
                              'Rejected: ${analystTest.rejectedParameters}',
                              Colors.red,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),

            // Quality Control Indicators
            if (analystTest.hasQualityControlTests)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  children: [
                    Icon(
                      Icons.verified_outlined,
                      size: 16,
                      color: Colors.blue[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Quality Control Tests Required',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.blue[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

            // Action Buttons
            if (onSubmitResult != null || onRejectTest != null)
              Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: Row(
                  children: [
                    if (onSubmitResult != null && !analystTest.isFullyCompleted)
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _showSubmitResultDialog(context),
                          icon: const Icon(Icons.upload_outlined, size: 18),
                          label: const Text('Submit Result'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    if (onSubmitResult != null && onRejectTest != null && !analystTest.isFullyCompleted)
                      const SizedBox(width: 8),
                    if (onRejectTest != null && !analystTest.isFullyCompleted)
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _showRejectTestDialog(context),
                          icon: const Icon(Icons.cancel_outlined, size: 18),
                          label: const Text('Reject'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                            side: const BorderSide(color: Colors.red),
                          ),
                        ),
                      ),
                    if (analystTest.isFullyCompleted)
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            color: Colors.green[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.check_circle, color: Colors.green[700], size: 18),
                              const SizedBox(width: 8),
                              Text(
                                'Completed',
                                style: TextStyle(
                                  color: Colors.green[700],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
    bool isOverdue = false,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isOverdue ? Colors.red[600] : Colors.grey[600],
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
            color: isOverdue ? Colors.red[600] : null,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: isOverdue ? Colors.red[600] : null,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressChip() {
    String text;
    Color backgroundColor;
    Color textColor;

    if (analystTest.isFullyCompleted) {
      text = 'Completed';
      backgroundColor = Colors.green[100]!;
      textColor = Colors.green[800]!;
    } else if (analystTest.completionPercentage > 0) {
      text = '${analystTest.completionPercentage.toInt()}%';
      backgroundColor = Colors.blue[100]!;
      textColor = Colors.blue[800]!;
    } else {
      text = 'Not Started';
      backgroundColor = Colors.grey[200]!;
      textColor = Colors.grey[800]!;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildStatusChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getProgressColor() {
    if (analystTest.completionPercentage >= 100) {
      return Colors.green;
    } else if (analystTest.completionPercentage >= 50) {
      return Colors.blue;
    } else if (analystTest.completionPercentage > 0) {
      return Colors.orange;
    } else {
      return Colors.grey;
    }
  }

  void _showSubmitResultDialog(BuildContext context) {
    // This would show a dialog for submitting test results
    // For now, just show a placeholder
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Submit Test Result'),
        content: const Text('Test result submission dialog would be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // onSubmitResult?.call(analystTest.id, request);
            },
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }

  void _showRejectTestDialog(BuildContext context) {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Test'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejecting this test:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Rejection Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.trim().isNotEmpty) {
                Navigator.pop(context);
                onRejectTest?.call(
                  analystTest.id,
                  RejectTestRequestEntity(rejectionReason: reasonController.text.trim()),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
