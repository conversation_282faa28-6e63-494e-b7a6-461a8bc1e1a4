import 'package:equatable/equatable.dart';
import '../../../../domain/entities/test_request/test_request_entity.dart';
import '../../../../domain/entities/test_request/sample_entity.dart';

abstract class TestRequestEvent extends Equatable {
  const TestRequestEvent();

  @override
  List<Object?> get props => [];
}

// Get test requests with pagination and search
class TestRequestsLoadRequested extends TestRequestEvent {
  final int page;
  final int perPage;
  final String? search;

  const TestRequestsLoadRequested({
    this.page = 1,
    this.perPage = 15,
    this.search,
  });

  @override
  List<Object?> get props => [page, perPage, search];
}

// Refresh test requests (reset to first page)
class TestRequestsRefreshRequested extends TestRequestEvent {
  final String? search;

  const TestRequestsRefreshRequested({this.search});

  @override
  List<Object?> get props => [search];
}

// Load more test requests (pagination)
class TestRequestsLoadMoreRequested extends TestRequestEvent {
  const TestRequestsLoadMoreRequested();
}

// Get specific test request by ID
class TestRequestLoadRequested extends TestRequestEvent {
  final int id;

  const TestRequestLoadRequested({required this.id});

  @override
  List<Object> get props => [id];
}

// Get test request with samples
class TestRequestWithSamplesLoadRequested extends TestRequestEvent {
  final int id;

  const TestRequestWithSamplesLoadRequested({required this.id});

  @override
  List<Object> get props => [id];
}

// Create new test request
class TestRequestCreateRequested extends TestRequestEvent {
  final TestRequestEntity testRequest;

  const TestRequestCreateRequested({required this.testRequest});

  @override
  List<Object> get props => [testRequest];
}

// Create test request with samples
class TestRequestWithSamplesCreateRequested extends TestRequestEvent {
  final TestRequestEntity testRequest;
  final List<SampleEntity> samples;

  const TestRequestWithSamplesCreateRequested({
    required this.testRequest,
    required this.samples,
  });

  @override
  List<Object> get props => [testRequest, samples];
}

// Update test request
class TestRequestUpdateRequested extends TestRequestEvent {
  final int id;
  final TestRequestEntity testRequest;

  const TestRequestUpdateRequested({
    required this.id,
    required this.testRequest,
  });

  @override
  List<Object> get props => [id, testRequest];
}

// Delete test request
class TestRequestDeleteRequested extends TestRequestEvent {
  final int id;

  const TestRequestDeleteRequested({required this.id});

  @override
  List<Object> get props => [id];
}

// Get samples for a test request
class TestRequestSamplesLoadRequested extends TestRequestEvent {
  final int testRequestId;

  const TestRequestSamplesLoadRequested({required this.testRequestId});

  @override
  List<Object> get props => [testRequestId];
}

// Add sample to test request
class TestRequestSampleAddRequested extends TestRequestEvent {
  final int testRequestId;
  final SampleEntity sample;

  const TestRequestSampleAddRequested({
    required this.testRequestId,
    required this.sample,
  });

  @override
  List<Object> get props => [testRequestId, sample];
}

// Clear errors
class TestRequestClearError extends TestRequestEvent {
  const TestRequestClearError();
}

// Search test requests
class TestRequestSearchRequested extends TestRequestEvent {
  final String query;

  const TestRequestSearchRequested({required this.query});

  @override
  List<Object> get props => [query];
}

// Clear search
class TestRequestSearchCleared extends TestRequestEvent {
  const TestRequestSearchCleared();
}
