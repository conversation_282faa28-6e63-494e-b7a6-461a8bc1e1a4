import 'package:equatable/equatable.dart';
import 'package:lims_app_flutter/domain/entities/common/pagination_entity.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/test_request/test_request_entity.dart';
import '../../../../domain/entities/test_request/sample_entity.dart';

abstract class TestRequestState extends Equatable {
  const TestRequestState();

  @override
  List<Object?> get props => [];
}

class TestRequestInitial extends TestRequestState {
  const TestRequestInitial();
}

class TestRequestLoading extends TestRequestState {
  const TestRequestLoading();
}

class TestRequestsLoaded extends TestRequestState {
  final List<TestRequestEntity> testRequests;
  final PaginationEntity paginationMeta;
  final String? currentSearch;
  final bool isLoadingMore;

  const TestRequestsLoaded({
    required this.testRequests,
    required this.paginationMeta,
    this.currentSearch,
    this.isLoadingMore = false,
  });

  @override
  List<Object?> get props => [
        testRequests,
        paginationMeta,
        currentSearch,
        isLoadingMore,
      ];

  TestRequestsLoaded copyWith({
    List<TestRequestEntity>? testRequests,
    PaginationEntity? paginationMeta,
    String? currentSearch,
    bool? isLoadingMore,
  }) {
    return TestRequestsLoaded(
      testRequests: testRequests ?? this.testRequests,
      paginationMeta: paginationMeta ?? this.paginationMeta,
      currentSearch: currentSearch ?? this.currentSearch,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }
}

class TestRequestLoaded extends TestRequestState {
  final TestRequestEntity testRequest;

  const TestRequestLoaded({required this.testRequest});

  @override
  List<Object> get props => [testRequest];
}

class TestRequestWithSamplesLoaded extends TestRequestState {
  final TestRequestEntity testRequest;
  final List<SampleEntity> samples;

  const TestRequestWithSamplesLoaded({
    required this.testRequest,
    required this.samples,
  });

  @override
  List<Object> get props => [testRequest, samples];
}

class TestRequestSamplesLoaded extends TestRequestState {
  final List<SampleEntity> samples;
  final int testRequestId;

  const TestRequestSamplesLoaded({
    required this.samples,
    required this.testRequestId,
  });

  @override
  List<Object> get props => [samples, testRequestId];
}

class TestRequestCreated extends TestRequestState {
  final TestRequestEntity testRequest;

  const TestRequestCreated({required this.testRequest});

  @override
  List<Object> get props => [testRequest];
}

class TestRequestWithSamplesCreated extends TestRequestState {
  final TestRequestEntity testRequest;
  final List<SampleEntity> samples;

  const TestRequestWithSamplesCreated({
    required this.testRequest,
    required this.samples,
  });

  @override
  List<Object> get props => [testRequest, samples];
}

class TestRequestUpdated extends TestRequestState {
  final TestRequestEntity testRequest;

  const TestRequestUpdated({required this.testRequest});

  @override
  List<Object> get props => [testRequest];
}

class TestRequestDeleted extends TestRequestState {
  final int deletedId;

  const TestRequestDeleted({required this.deletedId});

  @override
  List<Object> get props => [deletedId];
}

class TestRequestSampleAdded extends TestRequestState {
  final SampleEntity sample;
  final int testRequestId;

  const TestRequestSampleAdded({
    required this.sample,
    required this.testRequestId,
  });

  @override
  List<Object> get props => [sample, testRequestId];
}

class TestRequestError extends TestRequestState {
  final Failure failure;

  const TestRequestError({required this.failure});

  @override
  List<Object> get props => [failure];
}

class TestRequestOperationLoading extends TestRequestState {
  final String operation;

  const TestRequestOperationLoading({required this.operation});

  @override
  List<Object> get props => [operation];
}
