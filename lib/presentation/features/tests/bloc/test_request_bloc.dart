import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../domain/usecases/test_request/get_test_requests_usecase.dart';
import '../../../../domain/usecases/test_request/get_test_request_by_id_usecase.dart';
import '../../../../domain/usecases/test_request/get_test_request_with_samples_usecase.dart';
import '../../../../domain/usecases/test_request/create_test_request_usecase.dart';
import '../../../../domain/usecases/test_request/create_test_request_with_samples_usecase.dart';
import '../../../../domain/usecases/test_request/update_test_request_usecase.dart';
import '../../../../domain/usecases/test_request/delete_test_request_usecase.dart';
import '../../../../domain/usecases/test_request/get_samples_usecase.dart';
import '../../../../domain/usecases/test_request/add_sample_usecase.dart';
import 'test_request_event.dart';
import 'test_request_state.dart';

class TestRequestBloc extends Bloc<TestRequestEvent, TestRequestState> {
  final GetTestRequestsUseCase _getTestRequestsUseCase;
  final GetTestRequestByIdUseCase _getTestRequestByIdUseCase;
  final GetTestRequestWithSamplesUseCase _getTestRequestWithSamplesUseCase;
  final CreateTestRequestUseCase _createTestRequestUseCase;
  final CreateTestRequestWithSamplesUseCase _createTestRequestWithSamplesUseCase;
  final UpdateTestRequestUseCase _updateTestRequestUseCase;
  final DeleteTestRequestUseCase _deleteTestRequestUseCase;
  final GetSamplesUseCase _getSamplesUseCase;
  final AddSampleUseCase _addSampleUseCase;

  TestRequestBloc({
    required GetTestRequestsUseCase getTestRequestsUseCase,
    required GetTestRequestByIdUseCase getTestRequestByIdUseCase,
    required GetTestRequestWithSamplesUseCase getTestRequestWithSamplesUseCase,
    required CreateTestRequestUseCase createTestRequestUseCase,
    required CreateTestRequestWithSamplesUseCase createTestRequestWithSamplesUseCase,
    required UpdateTestRequestUseCase updateTestRequestUseCase,
    required DeleteTestRequestUseCase deleteTestRequestUseCase,
    required GetSamplesUseCase getSamplesUseCase,
    required AddSampleUseCase addSampleUseCase,
  })  : _getTestRequestsUseCase = getTestRequestsUseCase,
        _getTestRequestByIdUseCase = getTestRequestByIdUseCase,
        _getTestRequestWithSamplesUseCase = getTestRequestWithSamplesUseCase,
        _createTestRequestUseCase = createTestRequestUseCase,
        _createTestRequestWithSamplesUseCase = createTestRequestWithSamplesUseCase,
        _updateTestRequestUseCase = updateTestRequestUseCase,
        _deleteTestRequestUseCase = deleteTestRequestUseCase,
        _getSamplesUseCase = getSamplesUseCase,
        _addSampleUseCase = addSampleUseCase,
        super(const TestRequestInitial()) {
    on<TestRequestsLoadRequested>(_onTestRequestsLoadRequested);
    on<TestRequestsRefreshRequested>(_onTestRequestsRefreshRequested);
    on<TestRequestsLoadMoreRequested>(_onTestRequestsLoadMoreRequested);
    on<TestRequestLoadRequested>(_onTestRequestLoadRequested);
    on<TestRequestWithSamplesLoadRequested>(_onTestRequestWithSamplesLoadRequested);
    on<TestRequestCreateRequested>(_onTestRequestCreateRequested);
    on<TestRequestWithSamplesCreateRequested>(_onTestRequestWithSamplesCreateRequested);
    on<TestRequestUpdateRequested>(_onTestRequestUpdateRequested);
    on<TestRequestDeleteRequested>(_onTestRequestDeleteRequested);
    on<TestRequestSamplesLoadRequested>(_onTestRequestSamplesLoadRequested);
    on<TestRequestSampleAddRequested>(_onTestRequestSampleAddRequested);
    on<TestRequestClearError>(_onTestRequestClearError);
    on<TestRequestSearchRequested>(_onTestRequestSearchRequested);
    on<TestRequestSearchCleared>(_onTestRequestSearchCleared);
  }

  Future<void> _onTestRequestsLoadRequested(
    TestRequestsLoadRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestLoading());

    final result = await _getTestRequestsUseCase(
      GetTestRequestsParams(
        page: event.page,
        perPage: event.perPage,
        search: event.search,
      ),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (paginatedResult) => emit(TestRequestsLoaded(
        testRequests: paginatedResult.items,
        paginationMeta: paginatedResult.pagination,
        currentSearch: event.search,
      )),
    );
  }

  Future<void> _onTestRequestsRefreshRequested(
    TestRequestsRefreshRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestLoading());

    final result = await _getTestRequestsUseCase(
      GetTestRequestsParams(
        page: 1,
        perPage: 15,
        search: event.search,
      ),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (paginatedResult) => emit(TestRequestsLoaded(
        testRequests: paginatedResult.items,
        paginationMeta: paginatedResult.pagination,
        currentSearch: event.search,
      )),
    );
  }

  Future<void> _onTestRequestsLoadMoreRequested(
    TestRequestsLoadMoreRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    if (state is TestRequestsLoaded) {
      final currentState = state as TestRequestsLoaded;
      
      if (!currentState.paginationMeta.hasMore || currentState.isLoadingMore) {
        return;
      }

      emit(currentState.copyWith(isLoadingMore: true));

      final result = await _getTestRequestsUseCase(
        GetTestRequestsParams(
          page: currentState.paginationMeta.currentPage + 1,
          perPage: currentState.paginationMeta.perPage,
          search: currentState.currentSearch,
        ),
      );

      result.fold(
        (failure) => emit(TestRequestError(failure: failure)),
        (paginatedResult) => emit(TestRequestsLoaded(
          testRequests: [...currentState.testRequests, ...paginatedResult.items],
          paginationMeta: paginatedResult.pagination,
          currentSearch: currentState.currentSearch,
          isLoadingMore: false,
        )),
      );
    }
  }

  Future<void> _onTestRequestLoadRequested(
    TestRequestLoadRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestOperationLoading(operation: 'loading_test_request'));

    final result = await _getTestRequestByIdUseCase(
      GetTestRequestByIdParams(id: event.id),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (testRequest) => emit(TestRequestLoaded(testRequest: testRequest)),
    );
  }

  Future<void> _onTestRequestWithSamplesLoadRequested(
    TestRequestWithSamplesLoadRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestOperationLoading(operation: 'loading_test_request_with_samples'));

    final result = await _getTestRequestWithSamplesUseCase(
      GetTestRequestWithSamplesParams(id: event.id),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (result) => emit(TestRequestWithSamplesLoaded(
        testRequest: result.testRequest,
        samples: result.samples,
      )),
    );
  }

  Future<void> _onTestRequestCreateRequested(
    TestRequestCreateRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestOperationLoading(operation: 'creating_test_request'));

    final result = await _createTestRequestUseCase(
      CreateTestRequestParams(testRequest: event.testRequest),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (testRequest) => emit(TestRequestCreated(testRequest: testRequest)),
    );
  }

  Future<void> _onTestRequestWithSamplesCreateRequested(
    TestRequestWithSamplesCreateRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestOperationLoading(operation: 'creating_test_request_with_samples'));

    final result = await _createTestRequestWithSamplesUseCase(
      CreateTestRequestWithSamplesParams(
        testRequest: event.testRequest,
        samples: event.samples,
      ),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (result) => emit(TestRequestWithSamplesCreated(
        testRequest: result.testRequest,
        samples: result.samples,
      )),
    );
  }

  Future<void> _onTestRequestUpdateRequested(
    TestRequestUpdateRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestOperationLoading(operation: 'updating_test_request'));

    final result = await _updateTestRequestUseCase(
      UpdateTestRequestParams(
        id: event.id,
        testRequest: event.testRequest,
      ),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (testRequest) => emit(TestRequestUpdated(testRequest: testRequest)),
    );
  }

  Future<void> _onTestRequestDeleteRequested(
    TestRequestDeleteRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestOperationLoading(operation: 'deleting_test_request'));

    final result = await _deleteTestRequestUseCase(
      DeleteTestRequestParams(id: event.id),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (_) => emit(TestRequestDeleted(deletedId: event.id)),
    );
  }

  Future<void> _onTestRequestSamplesLoadRequested(
    TestRequestSamplesLoadRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestOperationLoading(operation: 'loading_samples'));

    final result = await _getSamplesUseCase(
      GetSamplesParams(testRequestId: event.testRequestId),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (samples) => emit(TestRequestSamplesLoaded(
        samples: samples,
        testRequestId: event.testRequestId,
      )),
    );
  }

  Future<void> _onTestRequestSampleAddRequested(
    TestRequestSampleAddRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestOperationLoading(operation: 'adding_sample'));

    final result = await _addSampleUseCase(
      AddSampleParams(
        testRequestId: event.testRequestId,
        sample: event.sample,
      ),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (sample) => emit(TestRequestSampleAdded(
        sample: sample,
        testRequestId: event.testRequestId,
      )),
    );
  }

  void _onTestRequestClearError(
    TestRequestClearError event,
    Emitter<TestRequestState> emit,
  ) {
    if (state is TestRequestError) {
      emit(const TestRequestInitial());
    }
  }

  Future<void> _onTestRequestSearchRequested(
    TestRequestSearchRequested event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestLoading());

    final result = await _getTestRequestsUseCase(
      GetTestRequestsParams(
        page: 1,
        perPage: 15,
        search: event.query,
      ),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (paginatedResult) => emit(TestRequestsLoaded(
        testRequests: paginatedResult.items,
        paginationMeta: paginatedResult.pagination,
        currentSearch: event.query,
      )),
    );
  }

  Future<void> _onTestRequestSearchCleared(
    TestRequestSearchCleared event,
    Emitter<TestRequestState> emit,
  ) async {
    emit(const TestRequestLoading());

    final result = await _getTestRequestsUseCase(
      const GetTestRequestsParams(page: 1, perPage: 15),
    );

    result.fold(
      (failure) => emit(TestRequestError(failure: failure)),
      (paginatedResult) => emit(TestRequestsLoaded(
        testRequests: paginatedResult.items,
        paginationMeta: paginatedResult.pagination,
        currentSearch: null,
      )),
    );
  }
}
