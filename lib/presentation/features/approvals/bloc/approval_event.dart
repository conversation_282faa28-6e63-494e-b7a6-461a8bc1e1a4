import 'package:equatable/equatable.dart';
import '../../../../domain/entities/approval/approval_entity.dart';

abstract class ApprovalEvent extends Equatable {
  const ApprovalEvent();

  @override
  List<Object?> get props => [];
}

// Load pending approvals
class PendingApprovalsLoadRequested extends ApprovalEvent {
  final int page;
  final int perPage;
  final bool refresh;

  const PendingApprovalsLoadRequested({
    this.page = 1,
    this.perPage = 15,
    this.refresh = false,
  });

  @override
  List<Object> get props => [page, perPage, refresh];
}

// Load approval history
class ApprovalHistoryLoadRequested extends ApprovalEvent {
  final int page;
  final int perPage;
  final bool refresh;

  const ApprovalHistoryLoadRequested({
    this.page = 1,
    this.perPage = 15,
    this.refresh = false,
  });

  @override
  List<Object> get props => [page, perPage, refresh];
}

// Load more pending approvals (pagination)
class PendingApprovalsLoadMoreRequested extends ApprovalEvent {
  const PendingApprovalsLoadMoreRequested();
}

// Load more approval history (pagination)
class ApprovalHistoryLoadMoreRequested extends ApprovalEvent {
  const ApprovalHistoryLoadMoreRequested();
}

// Refresh pending approvals
class PendingApprovalsRefreshRequested extends ApprovalEvent {
  const PendingApprovalsRefreshRequested();
}

// Refresh approval history
class ApprovalHistoryRefreshRequested extends ApprovalEvent {
  const ApprovalHistoryRefreshRequested();
}

// Load approval details by ID
class ApprovalDetailsLoadRequested extends ApprovalEvent {
  final int approvalId;

  const ApprovalDetailsLoadRequested({
    required this.approvalId,
  });

  @override
  List<Object> get props => [approvalId];
}

// Process single approval
class ApprovalProcessRequested extends ApprovalEvent {
  final int testId;
  final ApprovalRequestEntity request;

  const ApprovalProcessRequested({
    required this.testId,
    required this.request,
  });

  @override
  List<Object> get props => [testId, request];
}

// Process bulk approval
class BulkApprovalProcessRequested extends ApprovalEvent {
  final BulkApprovalRequestEntity request;

  const BulkApprovalProcessRequested({
    required this.request,
  });

  @override
  List<Object> get props => [request];
}

// Reassign test
class TestReassignRequested extends ApprovalEvent {
  final int testId;
  final int newApproverId;
  final String? remarks;

  const TestReassignRequested({
    required this.testId,
    required this.newApproverId,
    this.remarks,
  });

  @override
  List<Object?> get props => [testId, newApproverId, remarks];
}

// Clear error state
class ApprovalClearError extends ApprovalEvent {
  const ApprovalClearError();
}

// Clear success state
class ApprovalClearSuccess extends ApprovalEvent {
  const ApprovalClearSuccess();
}
