import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../domain/entities/approval/approval_entity.dart';
import '../../../../domain/entities/common/pagination_entity.dart';

abstract class ApprovalState extends Equatable {
  const ApprovalState();

  @override
  List<Object?> get props => [];
}

class ApprovalInitial extends ApprovalState {
  const ApprovalInitial();
}

class ApprovalLoading extends ApprovalState {
  const ApprovalLoading();
}

// Pending Approvals States
class PendingApprovalsLoaded extends ApprovalState {
  final List<ApprovalEntity> pendingApprovals;
  final PaginationEntity pagination;
  final bool hasReachedMax;

  const PendingApprovalsLoaded({
    required this.pendingApprovals,
    required this.pagination,
    this.hasReachedMax = false,
  });

  @override
  List<Object> get props => [pendingApprovals, pagination, hasReachedMax];

  PendingApprovalsLoaded copyWith({
    List<ApprovalEntity>? pendingApprovals,
    PaginationEntity? pagination,
    bool? hasReachedMax,
  }) {
    return PendingApprovalsLoaded(
      pendingApprovals: pendingApprovals ?? this.pendingApprovals,
      pagination: pagination ?? this.pagination,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }
}

class PendingApprovalsLoadingMore extends ApprovalState {
  final List<ApprovalEntity> pendingApprovals;
  final PaginationEntity pagination;

  const PendingApprovalsLoadingMore({
    required this.pendingApprovals,
    required this.pagination,
  });

  @override
  List<Object> get props => [pendingApprovals, pagination];
}

// Approval History States
class ApprovalHistoryLoaded extends ApprovalState {
  final List<ApprovalEntity> approvalHistory;
  final PaginationEntity pagination;
  final bool hasReachedMax;

  const ApprovalHistoryLoaded({
    required this.approvalHistory,
    required this.pagination,
    this.hasReachedMax = false,
  });

  @override
  List<Object> get props => [approvalHistory, pagination, hasReachedMax];

  ApprovalHistoryLoaded copyWith({
    List<ApprovalEntity>? approvalHistory,
    PaginationEntity? pagination,
    bool? hasReachedMax,
  }) {
    return ApprovalHistoryLoaded(
      approvalHistory: approvalHistory ?? this.approvalHistory,
      pagination: pagination ?? this.pagination,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }
}

class ApprovalHistoryLoadingMore extends ApprovalState {
  final List<ApprovalEntity> approvalHistory;
  final PaginationEntity pagination;

  const ApprovalHistoryLoadingMore({
    required this.approvalHistory,
    required this.pagination,
  });

  @override
  List<Object> get props => [approvalHistory, pagination];
}

// Approval Details States
class ApprovalDetailsLoading extends ApprovalState {
  const ApprovalDetailsLoading();
}

class ApprovalDetailsLoaded extends ApprovalState {
  final ApprovalEntity approval;

  const ApprovalDetailsLoaded({
    required this.approval,
  });

  @override
  List<Object> get props => [approval];
}

class ApprovalDetailsError extends ApprovalState {
  final Failure failure;

  const ApprovalDetailsError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Approval Processing States
class ApprovalProcessing extends ApprovalState {
  const ApprovalProcessing();
}

class ApprovalProcessed extends ApprovalState {
  final ApprovalEntity processedApproval;

  const ApprovalProcessed({
    required this.processedApproval,
  });

  @override
  List<Object> get props => [processedApproval];
}

class ApprovalProcessError extends ApprovalState {
  final Failure failure;

  const ApprovalProcessError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// Test Reassignment States
class TestReassigning extends ApprovalState {
  const TestReassigning();
}

class TestReassigned extends ApprovalState {
  final ApprovalEntity reassignedApproval;

  const TestReassigned({
    required this.reassignedApproval,
  });

  @override
  List<Object> get props => [reassignedApproval];
}

class TestReassignError extends ApprovalState {
  final Failure failure;

  const TestReassignError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

// General Error State
class ApprovalError extends ApprovalState {
  final Failure failure;

  const ApprovalError({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}
