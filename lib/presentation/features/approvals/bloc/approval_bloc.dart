import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../domain/usecases/approval/get_pending_approvals_usecase.dart';
import '../../../../domain/usecases/approval/process_approval_usecase.dart';
import 'approval_event.dart';
import 'approval_state.dart';

class ApprovalBloc extends Bloc<ApprovalEvent, ApprovalState> {
  final GetPendingApprovalsUseCase _getPendingApprovalsUseCase;
  final ProcessApprovalUseCase _processApprovalUseCase;

  ApprovalBloc({
    required GetPendingApprovalsUseCase getPendingApprovalsUseCase,
    required ProcessApprovalUseCase processApprovalUseCase,
  })  : _getPendingApprovalsUseCase = getPendingApprovalsUseCase,
        _processApprovalUseCase = processApprovalUseCase,
        super(const ApprovalInitial()) {
    on<PendingApprovalsLoadRequested>(_onPendingApprovalsLoadRequested);
    on<PendingApprovalsLoadMoreRequested>(_onPendingApprovalsLoadMoreRequested);
    on<PendingApprovalsRefreshRequested>(_onPendingApprovalsRefreshRequested);
    on<ApprovalProcessRequested>(_onApprovalProcessRequested);
    on<ApprovalClearError>(_onClearError);
    on<ApprovalClearSuccess>(_onClearSuccess);
  }

  Future<void> _onPendingApprovalsLoadRequested(
    PendingApprovalsLoadRequested event,
    Emitter<ApprovalState> emit,
  ) async {
    if (event.refresh || state is ApprovalInitial) {
      emit(const ApprovalLoading());
    }

    final result = await _getPendingApprovalsUseCase(
      GetPendingApprovalsParams(
        page: event.page,
        perPage: event.perPage,
      ),
    );

    result.fold(
      (failure) => emit(ApprovalError(failure: failure)),
      (approvalsResult) => emit(PendingApprovalsLoaded(
        pendingApprovals: approvalsResult.items,
        pagination: approvalsResult.pagination,
        hasReachedMax: !approvalsResult.pagination.hasMore,
      )),
    );
  }

  Future<void> _onPendingApprovalsLoadMoreRequested(
    PendingApprovalsLoadMoreRequested event,
    Emitter<ApprovalState> emit,
  ) async {
    final currentState = state;
    if (currentState is PendingApprovalsLoaded && !currentState.hasReachedMax) {
      emit(PendingApprovalsLoadingMore(
        pendingApprovals: currentState.pendingApprovals,
        pagination: currentState.pagination,
      ));

      final nextPage = currentState.pagination.currentPage + 1;
      final result = await _getPendingApprovalsUseCase(
        GetPendingApprovalsParams(page: nextPage),
      );

      result.fold(
        (failure) => emit(ApprovalError(failure: failure)),
        (approvalsResult) {
          final allApprovals = [
            ...currentState.pendingApprovals,
            ...approvalsResult.items,
          ];

          emit(PendingApprovalsLoaded(
            pendingApprovals: allApprovals,
            pagination: approvalsResult.pagination,
            hasReachedMax: !approvalsResult.pagination.hasMore,
          ));
        },
      );
    }
  }

  Future<void> _onPendingApprovalsRefreshRequested(
    PendingApprovalsRefreshRequested event,
    Emitter<ApprovalState> emit,
  ) async {
    add(const PendingApprovalsLoadRequested(refresh: true));
  }

  Future<void> _onApprovalProcessRequested(
    ApprovalProcessRequested event,
    Emitter<ApprovalState> emit,
  ) async {
    emit(const ApprovalProcessing());

    final result = await _processApprovalUseCase(
      ProcessApprovalParams(
        testId: event.testId,
        request: event.request,
      ),
    );

    result.fold(
      (failure) => emit(ApprovalProcessError(failure: failure)),
      (processedApproval) => emit(ApprovalProcessed(processedApproval: processedApproval)),
    );
  }

  void _onClearError(
    ApprovalClearError event,
    Emitter<ApprovalState> emit,
  ) {
    if (state is ApprovalError || 
        state is ApprovalProcessError || 
        state is TestReassignError ||
        state is ApprovalDetailsError) {
      emit(const ApprovalInitial());
    }
  }

  void _onClearSuccess(
    ApprovalClearSuccess event,
    Emitter<ApprovalState> emit,
  ) {
    if (state is ApprovalProcessed || 
        state is TestReassigned) {
      emit(const ApprovalInitial());
    }
  }
}
