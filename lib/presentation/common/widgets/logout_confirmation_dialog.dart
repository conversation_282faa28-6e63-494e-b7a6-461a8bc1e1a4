import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../features/auth/bloc/auth_bloc.dart';
import '../../features/auth/bloc/auth_event.dart';
import '../../features/auth/bloc/auth_state.dart';

/// A reusable logout confirmation dialog widget
/// 
/// This widget provides a consistent logout confirmation experience
/// across all screens in the application.
class LogoutConfirmationDialog extends StatelessWidget {
  const LogoutConfirmationDialog({super.key});

  /// Shows the logout confirmation dialog
  static Future<void> show(BuildContext context) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => const LogoutConfirmationDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(
            Icons.logout,
            color: Colors.red,
            size: 24,
          ),
          SizedBox(width: 8),
          Text('Confirm Logout'),
        ],
      ),
      content: const Text(
        'Are you sure you want to logout? You will need to login again to access the application.',
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            final isLoading = state is AuthLoading;
            return ElevatedButton(
              onPressed: isLoading
                  ? null
                  : () {
                      Navigator.of(context).pop();
                      context.read<AuthBloc>().add(const AuthLogoutRequested());
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('Logout'),
            );
          },
        ),
      ],
    );
  }
}

/// A reusable logout button widget
/// 
/// This widget provides a consistent logout button that can be used
/// in app bars, drawers, or other UI components.
class LogoutButton extends StatelessWidget {
  final IconData? icon;
  final String? text;
  final bool showIcon;
  final bool showText;
  final Color? iconColor;
  final Color? textColor;

  const LogoutButton({
    super.key,
    this.icon = Icons.logout,
    this.text = 'Logout',
    this.showIcon = true,
    this.showText = false,
    this.iconColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    if (showIcon && showText) {
      return TextButton.icon(
        onPressed: () => LogoutConfirmationDialog.show(context),
        icon: Icon(
          icon,
          color: iconColor ?? Colors.red,
        ),
        label: Text(
          text!,
          style: TextStyle(color: textColor ?? Colors.red),
        ),
      );
    } else if (showIcon) {
      return IconButton(
        icon: Icon(
          icon,
          color: iconColor,
        ),
        onPressed: () => LogoutConfirmationDialog.show(context),
        tooltip: 'Logout',
      );
    } else {
      return TextButton(
        onPressed: () => LogoutConfirmationDialog.show(context),
        child: Text(
          text!,
          style: TextStyle(color: textColor ?? Colors.red),
        ),
      );
    }
  }
}
