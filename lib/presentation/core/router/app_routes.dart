/// Route constants for the LIMS application
/// 
/// This class contains all the route paths used throughout the application
/// to avoid hardcoded strings and provide a centralized location for route management.
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // Authentication routes
  static const String login = '/login';
  static const String forgotPassword = '/forgot-password';

  // Dashboard route
  static const String dashboard = '/dashboard';

  // Front Desk routes
  static const String viewTests = '/view-tests';
  static const String createTest = '/create-test';
  static const String testDetails = '/test-details';
  static const String viewReports = '/view-reports';

  // Chief Chemist routes
  static const String jobAllocation = '/job-allocation';
  static const String createJob = '/create-job';
  static const String jobDetails = '/job-details';
  static const String approvals = '/approvals';
  static const String approvalDetails = '/approval-details';
  static const String qualityControl = '/quality-control';
  static const String retest = '/retest';
  static const String blindTest = '/blind-test';
  static const String replicateTest = '/replicate-test';

  // Analyst routes
  static const String analystJobs = '/analyst-jobs';
  static const String fillResults = '/fill-results';

  // Common routes
  static const String notifications = '/notifications';

  // Completed Tests routes
  static const String completedTests = '/completed-tests';
  static const String completedTestDetails = '/completed-test-details';

  // Test Results routes
  static const String testResults = '/test-results';
  static const String testResultDetails = '/test-result-details';
  static const String createTestResult = '/create-test-result';
  static const String editTestResult = '/edit-test-result';
  static const String myTestResults = '/my-test-results';
  static const String testResultsRequiringAttention = '/test-results-attention';

  // Enhanced Approval routes (extending existing)
  static const String approvalHistory = '/approval-history';
  static const String bulkApproval = '/bulk-approval';
  static const String reassignTest = '/reassign-test';

  // Analyst routes
  static const String analystTests = '/analyst-tests';

  // Master/Admin routes
  static const String userManagement = '/user-management';
  static const String systemSettings = '/system-settings';

  // Helper methods for parameterized routes
  static String testDetailsWithId(int id) => '$testDetails/$id';
  static String jobDetailsWithId(int id) => '$jobDetails/$id';
  static String approvalDetailsWithId(int id) => '$approvalDetails/$id';
  static String retestWithId(int id) => '$retest/$id';
  static String blindTestWithId(int id) => '$blindTest/$id';
  static String replicateTestWithId(int id) => '$replicateTest/$id';
  static String fillResultsWithId(int id) => '$fillResults/$id';

  // New parameterized routes
  static String completedTestDetailsWithId(int id) => '$completedTestDetails/$id';
  static String testResultDetailsWithId(int id) => '$testResultDetails/$id';
  static String editTestResultWithId(int id) => '$editTestResult/$id';
  static String reassignTestWithId(int id) => '$reassignTest/$id';
}
