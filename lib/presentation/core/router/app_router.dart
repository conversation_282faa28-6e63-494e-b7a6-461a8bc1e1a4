import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lims_app_flutter/presentation/core/router/app_routes.dart';
import 'package:lims_app_flutter/presentation/features/auth/bloc/auth_bloc.dart';
import 'package:lims_app_flutter/presentation/features/auth/bloc/auth_state.dart';
import 'package:lims_app_flutter/presentation/features/auth/screens/forgot_password_screen.dart';
import 'package:lims_app_flutter/presentation/features/auth/screens/login_screen.dart';
import 'package:lims_app_flutter/presentation/features/dashboard/screens/dashboard_screen.dart';
import 'package:lims_app_flutter/presentation/features/jobs/screens/analyst_jobs_screen.dart';
import 'package:lims_app_flutter/presentation/features/jobs/screens/job_allocation_screen.dart';
import 'package:lims_app_flutter/presentation/features/jobs/screens/create_job_screen.dart';
import 'package:lims_app_flutter/presentation/features/jobs/screens/job_details_screen.dart';
import 'package:lims_app_flutter/presentation/features/jobs/screens/fill_results_screen.dart';
import 'package:lims_app_flutter/presentation/features/notifications/screens/notifications_screen.dart';
import 'package:lims_app_flutter/presentation/features/tests/screens/create_test_screen.dart';
import 'package:lims_app_flutter/presentation/features/tests/screens/view_tests_screen.dart';
import 'package:lims_app_flutter/presentation/features/tests/screens/test_details_screen.dart';
import 'package:lims_app_flutter/presentation/features/reports/screens/view_reports_screen.dart';
import 'package:lims_app_flutter/presentation/features/approvals/screens/approvals_screen.dart';
import 'package:lims_app_flutter/presentation/features/approvals/screens/approval_details_screen.dart';
import 'package:lims_app_flutter/presentation/features/quality_control/screens/quality_control_screen.dart';
import 'package:lims_app_flutter/presentation/features/quality_control/screens/retest_screen.dart';
import 'package:lims_app_flutter/presentation/features/quality_control/screens/blind_test_screen.dart';
import 'package:lims_app_flutter/presentation/features/quality_control/screens/replicate_test_screen.dart';
import 'package:lims_app_flutter/presentation/features/completed_tests/screens/completed_tests_screen.dart';
import 'package:lims_app_flutter/presentation/features/analyst/screens/analyst_tests_screen.dart';

final GlobalKey<NavigatorState> _rootNavigatorKey = GlobalKey<NavigatorState>();

GoRouter createRouter(BuildContext context) {
  return GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: AppRoutes.login,
    redirect: (context, state) {
      final authState = context.read<AuthBloc>().state;
      final isAuthenticated = authState is AuthAuthenticated;
      final isLoggingIn = state.matchedLocation == AppRoutes.login;
      final isForgotPassword = state.matchedLocation == AppRoutes.forgotPassword;

      // If not authenticated and not on auth pages, redirect to login
      if (!isAuthenticated && !isLoggingIn && !isForgotPassword) {
        return AppRoutes.login;
      }

      // If authenticated and on login page, redirect to dashboard
      if (isAuthenticated && isLoggingIn) {
        return AppRoutes.dashboard;
      }

      return null; // No redirect needed
    },
  routes: [
    // Auth routes
    GoRoute(
      path: AppRoutes.login,
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: AppRoutes.forgotPassword,
      builder: (context, state) => const ForgotPasswordScreen(),
    ),

    // Dashboard route
    GoRoute(
      path: AppRoutes.dashboard,
      builder: (context, state) {
        final role = state.extra as String? ?? 'frontDesk';
        return DashboardScreen(
          userRole: _getUserRole(role),
        );
      },
    ),

    // Front Desk routes
    GoRoute(
      path: AppRoutes.viewTests,
      builder: (context, state) => const ViewTestsScreen(),
    ),
    GoRoute(
      path: AppRoutes.createTest,
      builder: (context, state) => const CreateTestScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.testDetails}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return TestDetailsScreen(testRequestId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.viewReports,
      builder: (context, state) => const ViewReportsScreen(),
    ),

    // Chief Chemist routes
    GoRoute(
      path: AppRoutes.jobAllocation,
      builder: (context, state) => const JobAllocationScreen(),
    ),
    GoRoute(
      path: AppRoutes.createJob,
      builder: (context, state) => const CreateJobScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.jobDetails}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return JobDetailsScreen(jobId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.approvals,
      builder: (context, state) => const ApprovalsScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.approvalDetails}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return ApprovalDetailsScreen(testRequestId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.qualityControl,
      builder: (context, state) => const QualityControlScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.retest}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return RetestScreen(originalTestId: id);
      },
    ),
    GoRoute(
      path: '${AppRoutes.blindTest}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return BlindTestScreen(originalTestId: id);
      },
    ),
    GoRoute(
      path: '${AppRoutes.replicateTest}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return ReplicateTestScreen(originalTestId: id);
      },
    ),

    // Analyst routes
    GoRoute(
      path: AppRoutes.analystJobs,
      builder: (context, state) => const AnalystJobsScreen(),
    ),
    GoRoute(
      path: AppRoutes.analystTests,
      builder: (context, state) => const AnalystTestsScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.fillResults}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return FillResultsScreen(jobId: id);
      },
    ),

    // New LIMS 2 routes
    GoRoute(
      path: AppRoutes.completedTests,
      builder: (context, state) => const CompletedTestsScreen(),
    ),
    GoRoute(
      path: AppRoutes.testResults,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('Test Results Screen - Coming Soon'),
        ),
      ),
    ),
    GoRoute(
      path: AppRoutes.myTestResults,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('My Test Results Screen - Coming Soon'),
        ),
      ),
    ),
    GoRoute(
      path: AppRoutes.approvalHistory,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('Approval History Screen - Coming Soon'),
        ),
      ),
    ),
    GoRoute(
      path: AppRoutes.bulkApproval,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('Bulk Approval Screen - Coming Soon'),
        ),
      ),
    ),

    // Common routes
    GoRoute(
      path: AppRoutes.notifications,
      builder: (context, state) => const NotificationsScreen(),
    ),

    // Master/Admin routes
    GoRoute(
      path: AppRoutes.userManagement,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('User Management Screen - Coming Soon'),
        ),
      ),
    ),
    GoRoute(
      path: AppRoutes.systemSettings,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('System Settings Screen - Coming Soon'),
        ),
      ),
    ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text('Error: ${state.error}'),
      ),
    ),
  );
}

// For backward compatibility, create a default router
final goRouter = GoRouter(
  navigatorKey: _rootNavigatorKey,
  initialLocation: AppRoutes.login,
  routes: [
    // Auth routes
    GoRoute(
      path: AppRoutes.login,
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: AppRoutes.forgotPassword,
      builder: (context, state) => const ForgotPasswordScreen(),
    ),

    // Dashboard route
    GoRoute(
      path: AppRoutes.dashboard,
      builder: (context, state) {
        final role = state.extra as String? ?? 'frontDesk';
        return DashboardScreen(
          userRole: _getUserRole(role),
        );
      },
    ),

    // Front Desk routes
    GoRoute(
      path: AppRoutes.viewTests,
      builder: (context, state) => const ViewTestsScreen(),
    ),
    GoRoute(
      path: AppRoutes.createTest,
      builder: (context, state) => const CreateTestScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.testDetails}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return TestDetailsScreen(testRequestId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.viewReports,
      builder: (context, state) => const ViewReportsScreen(),
    ),

    // Chief Chemist routes
    GoRoute(
      path: AppRoutes.jobAllocation,
      builder: (context, state) => const JobAllocationScreen(),
    ),
    GoRoute(
      path: AppRoutes.createJob,
      builder: (context, state) => const CreateJobScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.jobDetails}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return JobDetailsScreen(jobId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.approvals,
      builder: (context, state) => const ApprovalsScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.approvalDetails}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return ApprovalDetailsScreen(testRequestId: id);
      },
    ),
    GoRoute(
      path: AppRoutes.qualityControl,
      builder: (context, state) => const QualityControlScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.retest}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return RetestScreen(originalTestId: id);
      },
    ),
    GoRoute(
      path: '${AppRoutes.blindTest}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return BlindTestScreen(originalTestId: id);
      },
    ),
    GoRoute(
      path: '${AppRoutes.replicateTest}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return ReplicateTestScreen(originalTestId: id);
      },
    ),

    // Analyst routes
    GoRoute(
      path: AppRoutes.analystJobs,
      builder: (context, state) => const AnalystJobsScreen(),
    ),
    GoRoute(
      path: AppRoutes.analystTests,
      builder: (context, state) => const AnalystTestsScreen(),
    ),
    GoRoute(
      path: '${AppRoutes.fillResults}/:id',
      builder: (context, state) {
        final id = int.parse(state.pathParameters['id']!);
        return FillResultsScreen(jobId: id);
      },
    ),

    // New LIMS 2 routes
    GoRoute(
      path: AppRoutes.completedTests,
      builder: (context, state) => const CompletedTestsScreen(),
    ),
    GoRoute(
      path: AppRoutes.testResults,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('Test Results Screen - Coming Soon'),
        ),
      ),
    ),
    GoRoute(
      path: AppRoutes.myTestResults,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('My Test Results Screen - Coming Soon'),
        ),
      ),
    ),
    GoRoute(
      path: AppRoutes.approvalHistory,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('Approval History Screen - Coming Soon'),
        ),
      ),
    ),
    GoRoute(
      path: AppRoutes.bulkApproval,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('Bulk Approval Screen - Coming Soon'),
        ),
      ),
    ),

    // Common routes
    GoRoute(
      path: AppRoutes.notifications,
      builder: (context, state) => const NotificationsScreen(),
    ),

    // Master/Admin routes
    GoRoute(
      path: AppRoutes.userManagement,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('User Management Screen - Coming Soon'),
        ),
      ),
    ),
    GoRoute(
      path: AppRoutes.systemSettings,
      builder: (context, state) => const Scaffold(
        body: Center(
          child: Text('System Settings Screen - Coming Soon'),
        ),
      ),
    ),
  ],
  errorBuilder: (context, state) => Scaffold(
    body: Center(
      child: Text('Error: ${state.error}'),
    ),
  ),
);

UserRole _getUserRole(String role) {
  switch (role.toLowerCase()) {
    case 'master':
      return UserRole.master;
    case 'frontdesk':
      return UserRole.frontDesk;
    case 'chiefchemist':
      return UserRole.chiefChemist;
    case 'analyst':
      return UserRole.analyst;
    default:
      return UserRole.frontDesk;
  }
} 