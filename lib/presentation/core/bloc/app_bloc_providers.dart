import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/di/injection_container.dart' as di;
import '../../features/auth/bloc/auth_bloc.dart';
import '../../features/auth/bloc/auth_event.dart';
import '../../features/tests/bloc/test_request_bloc.dart';
import '../../features/jobs/bloc/job_allocation_bloc.dart';
import '../../features/dashboard/bloc/dashboard_bloc.dart';
import '../../features/master/bloc/master_data_bloc.dart';

/// Provides all BLoCs for the entire application
class AppBlocProviders extends StatelessWidget {
  final Widget child;

  const AppBlocProviders({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        // Authentication BLoC - Global
        BlocProvider<AuthBloc>(
          create: (context) => di.sl<AuthBloc>()
            ..add(const AuthCheckStatusRequested()),
        ),

        // Test Request BLoC
        BlocProvider<TestRequestBloc>(
          create: (context) => di.sl<TestRequestBloc>(),
        ),

        // Job Allocation BLoC
        BlocProvider<JobAllocationBloc>(
          create: (context) => di.sl<JobAllocationBloc>(),
        ),

        // Dashboard BLoC
        BlocProvider<DashboardBloc>(
          create: (context) => di.sl<DashboardBloc>(),
        ),

        // Master Data BLoC
        BlocProvider<MasterDataBloc>(
          create: (context) => di.sl<MasterDataBloc>(),
        ),
      ],
      child: child,
    );
  }
}

/// Provides specific BLoCs for feature modules
class FeatureBlocProviders {
  
  /// Provides BLoCs for Test Request features
  static Widget testRequestProviders({required Widget child}) {
    return BlocProvider<TestRequestBloc>(
      create: (context) => di.sl<TestRequestBloc>(),
      child: child,
    );
  }

  /// Provides BLoCs for Job Allocation features
  static Widget jobAllocationProviders({required Widget child}) {
    return BlocProvider<JobAllocationBloc>(
      create: (context) => di.sl<JobAllocationBloc>(),
      child: child,
    );
  }

  /// Provides BLoCs for Dashboard features
  static Widget dashboardProviders({required Widget child}) {
    return BlocProvider<DashboardBloc>(
      create: (context) => di.sl<DashboardBloc>(),
      child: child,
    );
  }

  /// Provides BLoCs for Master Data features
  static Widget masterDataProviders({required Widget child}) {
    return BlocProvider<MasterDataBloc>(
      create: (context) => di.sl<MasterDataBloc>(),
      child: child,
    );
  }

  /// Provides multiple BLoCs for complex features
  static Widget multipleProviders({
    required Widget child,
    required List<String> features,
  }) {
    final providers = <BlocProvider>[];

    for (final feature in features) {
      switch (feature) {
        case 'test_requests':
          providers.add(
            BlocProvider<TestRequestBloc>(
              create: (context) => di.sl<TestRequestBloc>(),
            ),
          );
          break;
        case 'job_allocations':
          providers.add(
            BlocProvider<JobAllocationBloc>(
              create: (context) => di.sl<JobAllocationBloc>(),
            ),
          );
          break;
        case 'dashboard':
          providers.add(
            BlocProvider<DashboardBloc>(
              create: (context) => di.sl<DashboardBloc>(),
            ),
          );
          break;
        case 'master_data':
          providers.add(
            BlocProvider<MasterDataBloc>(
              create: (context) => di.sl<MasterDataBloc>(),
            ),
          );
          break;
      }
    }

    return MultiBlocProvider(
      providers: providers,
      child: child,
    );
  }
}

/// BLoC observer for debugging and monitoring
class AppBlocObserver extends BlocObserver {
  @override
  void onCreate(BlocBase bloc) {
    super.onCreate(bloc);
    debugPrint('🔵 BLoC Created: ${bloc.runtimeType}');
  }

  @override
  void onEvent(Bloc bloc, Object? event) {
    super.onEvent(bloc, event);
    debugPrint('🟡 BLoC Event: ${bloc.runtimeType} - $event');
  }

  @override
  void onTransition(Bloc bloc, Transition transition) {
    super.onTransition(bloc, transition);
    debugPrint('🟢 BLoC Transition: ${bloc.runtimeType} - $transition');
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);
    debugPrint('🔴 BLoC Error: ${bloc.runtimeType} - $error');
    debugPrint('Stack Trace: $stackTrace');
  }

  @override
  void onClose(BlocBase bloc) {
    super.onClose(bloc);
    debugPrint('⚫ BLoC Closed: ${bloc.runtimeType}');
  }
}

/// Extension methods for easy BLoC access
extension BlocContextExtension on BuildContext {
  
  // Authentication
  AuthBloc get authBloc => read<AuthBloc>();
  
  // Test Requests
  TestRequestBloc get testRequestBloc => read<TestRequestBloc>();
  
  // Job Allocations
  JobAllocationBloc get jobAllocationBloc => read<JobAllocationBloc>();
  
  // Dashboard
  DashboardBloc get dashboardBloc => read<DashboardBloc>();
  
  // Master Data
  MasterDataBloc get masterDataBloc => read<MasterDataBloc>();
}

/// Utility class for BLoC management
class BlocUtils {
  
  /// Dispose all BLoCs safely
  static void disposeAllBlocs(BuildContext context) {
    try {
      context.read<TestRequestBloc>().close();
      context.read<JobAllocationBloc>().close();
      context.read<DashboardBloc>().close();
      context.read<MasterDataBloc>().close();
      // Note: AuthBloc is global and should not be disposed manually
    } catch (e) {
      debugPrint('Error disposing BLoCs: $e');
    }
  }

  /// Check if all required BLoCs are available
  static bool areAllBlocsAvailable(BuildContext context) {
    try {
      context.read<AuthBloc>();
      context.read<TestRequestBloc>();
      context.read<JobAllocationBloc>();
      context.read<DashboardBloc>();
      context.read<MasterDataBloc>();
      return true;
    } catch (e) {
      debugPrint('Not all BLoCs are available: $e');
      return false;
    }
  }

  /// Get BLoC status for debugging
  static Map<String, String> getBlocStatus(BuildContext context) {
    final status = <String, String>{};
    
    try {
      status['AuthBloc'] = context.read<AuthBloc>().state.runtimeType.toString();
      status['TestRequestBloc'] = context.read<TestRequestBloc>().state.runtimeType.toString();
      status['JobAllocationBloc'] = context.read<JobAllocationBloc>().state.runtimeType.toString();
      status['DashboardBloc'] = context.read<DashboardBloc>().state.runtimeType.toString();
      status['MasterDataBloc'] = context.read<MasterDataBloc>().state.runtimeType.toString();
    } catch (e) {
      status['Error'] = e.toString();
    }
    
    return status;
  }
}
