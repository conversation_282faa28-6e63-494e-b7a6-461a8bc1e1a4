import 'package:flutter/material.dart';
import '../../../core/error/failures.dart';
import '../../../core/utils/error_utils.dart';
import 'error_text_field.dart';

/// Example widget demonstrating enhanced error handling
/// This shows how to handle different types of Laravel errors
class ErrorHandlingExample extends StatefulWidget {
  const ErrorHandlingExample({super.key});

  @override
  State<ErrorHandlingExample> createState() => _ErrorHandlingExampleState();
}

class _ErrorHandlingExampleState extends State<ErrorHandlingExample> {
  final _formKey = GlobalKey<FormState>();
  final _requestNumberController = TextEditingController();
  final _customerNameController = TextEditingController();
  
  Failure? _currentFailure;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error Handling Demo'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Validation Error Summary (shows at top of form)
              ValidationErrorSummary(failure: _currentFailure),
              
              // Form fields with field-specific error handling
              ErrorTextField(
                fieldName: 'request_number',
                labelText: 'Request Number',
                controller: _requestNumberController,
                failure: _currentFailure,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Request number is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              ErrorTextField(
                fieldName: 'customer_name',
                labelText: 'Customer Name',
                controller: _customerNameController,
                failure: _currentFailure,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Customer name is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              
              // Action buttons to simulate different error types
              ElevatedButton(
                onPressed: () => _simulateValidationError(),
                child: const Text('Simulate Validation Error'),
              ),
              const SizedBox(height: 8),
              
              ElevatedButton(
                onPressed: () => _simulateGeneralError(),
                child: const Text('Simulate General Error (404)'),
              ),
              const SizedBox(height: 8),
              
              ElevatedButton(
                onPressed: () => _simulateNetworkError(),
                child: const Text('Simulate Network Error'),
              ),
              const SizedBox(height: 8),
              
              ElevatedButton(
                onPressed: () => _clearErrors(),
                child: const Text('Clear Errors'),
              ),
              const SizedBox(height: 24),
              
              // Error information display
              if (_currentFailure != null) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Error Information',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text('Type: ${_currentFailure!.errorType}'),
                        Text('Message: ${_currentFailure!.userMessage}'),
                        Text('Retryable: ${_currentFailure!.isRetryable}'),
                        Text('Requires Reauth: ${_currentFailure!.requiresReauth}'),
                        
                        if (_currentFailure is ValidationFailure) ...[
                          const SizedBox(height: 8),
                          const Text('Field Errors:'),
                          ...(_currentFailure as ValidationFailure).errorFields.map(
                            (field) => Text(
                              '  • $field: ${(_currentFailure as ValidationFailure).getFirstFieldError(field)}',
                            ),
                          ),
                        ],
                        
                        if (_currentFailure is GeneralFailure) ...[
                          const SizedBox(height: 8),
                          Text('Details: ${(_currentFailure as GeneralFailure).details ?? 'None'}'),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _simulateValidationError() {
    setState(() {
      _currentFailure = const ValidationFailure(
        message: 'Validation failed',
        fieldErrors: {
          'request_number': ['The request number has already been taken.'],
          'customer_name': ['The customer name field is required.', 'The customer name must be at least 3 characters.'],
        },
      );
    });
    
    _showSnackBar(_currentFailure!);
  }

  void _simulateGeneralError() {
    setState(() {
      _currentFailure = const GeneralFailure(
        message: 'Failed to delete test request',
        details: 'No query results for model [App\\Models\\TestRequest] 0',
      );
    });
    
    _showSnackBar(_currentFailure!);
  }

  void _simulateNetworkError() {
    setState(() {
      _currentFailure = const NetworkFailure(
        message: 'Connection timeout. Please check your internet connection.',
      );
    });
    
    _showSnackBar(_currentFailure!);
  }

  void _clearErrors() {
    setState(() {
      _currentFailure = null;
    });
  }

  void _showSnackBar(Failure failure) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(failure.snackBarMessage),
        backgroundColor: _getSnackBarColor(failure),
        action: failure.isRetryable
            ? SnackBarAction(
                label: 'Retry',
                onPressed: () {
                  // Implement retry logic
                },
              )
            : null,
      ),
    );
  }

  Color _getSnackBarColor(Failure failure) {
    switch (failure.errorType) {
      case 'validation':
        return Colors.orange;
      case 'general':
        return Colors.red;
      case 'network':
        return Colors.blue;
      case 'unauthorized':
        return Colors.purple;
      case 'server':
        return Colors.red.shade800;
      default:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    _requestNumberController.dispose();
    _customerNameController.dispose();
    super.dispose();
  }
}

/// Helper widget to demonstrate error handling in lists
class ErrorHandlingListExample extends StatelessWidget {
  final List<Failure> exampleFailures = const [
    ValidationFailure(
      message: 'Validation failed',
      fieldErrors: {
        'email': ['The email field is required.'],
        'password': ['The password must be at least 8 characters.'],
      },
    ),
    GeneralFailure(
      message: 'Resource not found',
      details: 'No query results for model [App\\Models\\Customer] 123',
    ),
    NetworkFailure(message: 'Connection timeout'),
    UnauthorizedFailure(message: 'Token has expired'),
    ServerFailure(message: 'Internal server error', statusCode: 500),
  ];

  const ErrorHandlingListExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error Types Demo'),
      ),
      body: ListView.builder(
        itemCount: exampleFailures.length,
        itemBuilder: (context, index) {
          final failure = exampleFailures[index];
          return Card(
            margin: const EdgeInsets.all(8.0),
            child: ListTile(
              leading: Icon(
                _getErrorIcon(failure.errorType),
                color: _getErrorColor(failure.errorType),
              ),
              title: Text(failure.errorType.toUpperCase()),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(failure.userMessage),
                  if (failure is ValidationFailure && failure.fieldErrors != null)
                    Text(
                      'Fields: ${failure.errorFields.join(', ')}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  if (failure is GeneralFailure && failure.details != null)
                    Text(
                      'Details: ${failure.details}',
                      style: const TextStyle(fontSize: 12),
                    ),
                ],
              ),
              trailing: failure.isRetryable
                  ? const Icon(Icons.refresh)
                  : failure.requiresReauth
                      ? const Icon(Icons.login)
                      : null,
            ),
          );
        },
      ),
    );
  }

  IconData _getErrorIcon(String errorType) {
    switch (errorType) {
      case 'validation':
        return Icons.warning;
      case 'general':
        return Icons.error;
      case 'network':
        return Icons.wifi_off;
      case 'unauthorized':
        return Icons.lock;
      case 'server':
        return Icons.dns;
      default:
        return Icons.help;
    }
  }

  Color _getErrorColor(String errorType) {
    switch (errorType) {
      case 'validation':
        return Colors.orange;
      case 'general':
        return Colors.red;
      case 'network':
        return Colors.blue;
      case 'unauthorized':
        return Colors.purple;
      case 'server':
        return Colors.red.shade800;
      default:
        return Colors.grey;
    }
  }
}
