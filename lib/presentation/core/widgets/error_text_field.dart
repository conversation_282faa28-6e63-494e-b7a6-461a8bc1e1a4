import 'package:flutter/material.dart';
import '../../../core/error/failures.dart';
import '../../../core/utils/error_utils.dart';

/// Enhanced TextFormField with built-in error handling for ValidationFailure
class ErrorTextField extends StatelessWidget {
  final String fieldName;
  final String labelText;
  final String? hintText;
  final TextEditingController? controller;
  final Failure? failure;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String?)? onSaved;
  final TextInputType? keyboardType;
  final bool obscureText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final int? maxLines;
  final int? maxLength;
  final bool enabled;
  final bool readOnly;
  final TextCapitalization textCapitalization;
  final TextInputAction? textInputAction;
  final void Function()? onTap;
  final FocusNode? focusNode;

  const ErrorTextField({
    super.key,
    required this.fieldName,
    required this.labelText,
    this.hintText,
    this.controller,
    this.failure,
    this.validator,
    this.onChanged,
    this.onSaved,
    this.keyboardType,
    this.obscureText = false,
    this.prefixIcon,
    this.suffixIcon,
    this.maxLines = 1,
    this.maxLength,
    this.enabled = true,
    this.readOnly = false,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction,
    this.onTap,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    final fieldError = ErrorUtils.getFieldError(failure, fieldName);
    final hasError = fieldError != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          decoration: InputDecoration(
            labelText: labelText,
            hintText: hintText,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
            errorText: hasError ? fieldError : null,
            errorMaxLines: 2,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Colors.grey,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Colors.grey.shade300,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
          ),
          validator: (value) {
            // First check for field-specific API validation errors
            if (hasError) {
              return fieldError;
            }
            // Then run custom validator if provided
            return validator?.call(value);
          },
          onChanged: onChanged,
          onSaved: onSaved,
          keyboardType: keyboardType,
          obscureText: obscureText,
          maxLines: maxLines,
          maxLength: maxLength,
          enabled: enabled,
          readOnly: readOnly,
          textCapitalization: textCapitalization,
          textInputAction: textInputAction,
          onTap: onTap,
        ),
        if (hasError) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 16,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  fieldError,
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}

/// Enhanced DropdownButtonFormField with built-in error handling
class ErrorDropdownField<T> extends StatelessWidget {
  final String fieldName;
  final String labelText;
  final String? hintText;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final Failure? failure;
  final String? Function(T?)? validator;
  final void Function(T?)? onChanged;
  final void Function(T?)? onSaved;
  final Widget? prefixIcon;
  final bool enabled;

  const ErrorDropdownField({
    super.key,
    required this.fieldName,
    required this.labelText,
    this.hintText,
    this.value,
    required this.items,
    this.failure,
    this.validator,
    this.onChanged,
    this.onSaved,
    this.prefixIcon,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final fieldError = ErrorUtils.getFieldError(failure, fieldName);
    final hasError = fieldError != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          decoration: InputDecoration(
            labelText: labelText,
            hintText: hintText,
            prefixIcon: prefixIcon,
            errorText: hasError ? fieldError : null,
            errorMaxLines: 2,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Colors.grey,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Colors.grey.shade300,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: hasError ? Colors.red : Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
          ),
          validator: (value) {
            // First check for field-specific API validation errors
            if (hasError) {
              return fieldError;
            }
            // Then run custom validator if provided
            return validator?.call(value);
          },
          onChanged: enabled ? onChanged : null,
          onSaved: onSaved,
        ),
        if (hasError) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 16,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  fieldError,
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}

/// Widget to display general validation errors at the top of forms
class ValidationErrorSummary extends StatelessWidget {
  final Failure? failure;
  final EdgeInsetsGeometry? margin;

  const ValidationErrorSummary({
    super.key,
    this.failure,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    if (failure is! ValidationFailure) return const SizedBox.shrink();
    
    final validationFailure = failure as ValidationFailure;
    if (validationFailure.fieldErrors == null || 
        validationFailure.fieldErrors!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        border: Border.all(color: Colors.red.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red.shade700,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Please fix the following errors:',
                style: TextStyle(
                  color: Colors.red.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...validationFailure.fieldErrors!.entries.map((entry) {
            final fieldName = entry.key.split('_')
                .map((word) => word.isNotEmpty 
                    ? '${word[0].toUpperCase()}${word.substring(1)}'
                    : word)
                .join(' ');
            
            return Padding(
              padding: const EdgeInsets.only(left: 28, bottom: 4),
              child: Text(
                '• $fieldName: ${entry.value.first}',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 14,
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
