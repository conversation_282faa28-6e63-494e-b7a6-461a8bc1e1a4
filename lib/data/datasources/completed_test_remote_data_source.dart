import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../../core/network/api_response.dart';
import '../models/completed_test/completed_test_model.dart';
import '../models/common/completed_tests_response_model.dart';

abstract class CompletedTestRemoteDataSource {
  Future<CompletedTestsDataModel> getCompletedTests({
    int page = 1,
    int perPage = 15,
  });
  Future<CompletedTestModel> getCompletedTestById(int id);

  // Export functionality (from Postman collection)
  Future<String> exportToExcel();
}

class CompletedTestRemoteDataSourceImpl implements CompletedTestRemoteDataSource {
  final DioClient _dioClient;

  CompletedTestRemoteDataSourceImpl(this._dioClient);

  @override
  Future<CompletedTestsDataModel> getCompletedTests({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      final response = await _dioClient.dio.get(
        ApiConstants.completedTests,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = CompletedTestsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: 'Failed to get completed tests',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get completed tests',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<CompletedTestModel> getCompletedTestById(int id) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.completedTestById(id),
      );

      if (response.statusCode == 200) {
        final apiResponse = CompletedTestResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.completedTest;
        } else {
          throw ServerException(
            message: 'Failed to get completed test',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get completed test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<String> exportToExcel() async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.completedTestsExportExcel,
      );

      if (response.statusCode == 200) {
        // Return the download URL or file path
        if (response.data['success'] == true) {
          return response.data['data']['download_url'] ?? response.data['data']['file_path'] ?? '';
        } else {
          throw ServerException(
            message: response.data['message'] ?? 'Failed to export completed tests to Excel',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to export completed tests to Excel',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
