import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../models/analyst/analyst_test_model.dart';
import '../models/common/analyst_tests_response_model.dart';
import '../models/completed_test/completed_test_model.dart';

abstract class AnalystRemoteDataSource {
  Future<List<AnalystTestModel>> getMyTests();
  Future<CompletedTestModel> submitTestResult(
    int testId,
    SubmitTestResultRequestModel request,
  );
  Future<void> rejectTest(int testId, RejectTestRequestModel request);
}

class AnalystRemoteDataSourceImpl implements AnalystRemoteDataSource {
  final DioClient _dioClient;

  AnalystRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<AnalystTestModel>> getMyTests() async {
    try {
      final response = await _dioClient.dio.get(ApiConstants.analystMyTests);

      if (response.statusCode == 200) {
        final apiResponse = AnalystTestsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.myTests;
        } else {
          throw ServerException(
            message: 'Failed to get analyst tests',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get analyst tests',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<CompletedTestModel> submitTestResult(
    int testId,
    SubmitTestResultRequestModel request,
  ) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.analystSubmitTestResult(testId),
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to submit test result',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to submit test result',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<void> rejectTest(int testId, RejectTestRequestModel request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.analystRejectTest(testId),
        data: request.toJson(),
      );

      if (response.statusCode != 200) {
        throw ServerException(
          message: 'Failed to reject test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
