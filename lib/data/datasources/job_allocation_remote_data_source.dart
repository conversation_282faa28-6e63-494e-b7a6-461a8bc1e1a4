import 'package:dio/dio.dart';
import 'package:lims_app_flutter/data/models/common/job_allocations_response_model.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../../core/network/api_response.dart';
import '../models/job_allocation/job_allocation_model.dart';

abstract class JobAllocationRemoteDataSource {
  Future<JobAllocationsResponseData> getJobAllocations({
    int page = 1,
    int perPage = 15,
    String? search,
  });
  Future<JobAllocationModel> getJobAllocationById(int id);
  Future<JobAllocationModel> createJobAllocation(JobAllocationModel jobAllocation);
  Future<JobAllocationModel> updateJobAllocation(int id, JobAllocationModel jobAllocation);
  Future<void> deleteJobAllocation(int id);

  // Additional endpoints from Postman collection
  Future<String> exportToExcel();
  Future<String> generatePdf(int id);
  Future<List<Map<String, dynamic>>> getAnalysts();
}

class JobAllocationRemoteDataSourceImpl implements JobAllocationRemoteDataSource {
  final DioClient _dioClient;

  JobAllocationRemoteDataSourceImpl(this._dioClient);

  @override
  Future<JobAllocationsResponseData> getJobAllocations({
    int page = 1,
    int perPage = 15,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      final response = await _dioClient.dio.get(ApiConstants.jobAllocations,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = JobAllocationsApiResponse.fromJson(
          response.data,
              (json) => JobAllocationsResponseData.fromJson(json as Map<String, dynamic>),
        );

        if (apiResponse.success && apiResponse.data != null) {
          return apiResponse.data!;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get job allocations',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get job allocations',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<JobAllocationModel> getJobAllocationById(int id) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.jobAllocationById(id),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return JobAllocationModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get job allocation',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get job allocation',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<JobAllocationModel> createJobAllocation(JobAllocationModel jobAllocation) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.jobAllocations,
        data: jobAllocation.toJson(),
      );

      if (response.statusCode == 201) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return JobAllocationModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to create job allocation',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to create job allocation',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<JobAllocationModel> updateJobAllocation(int id, JobAllocationModel jobAllocation) async {
    try {
      final response = await _dioClient.dio.put(
        ApiConstants.jobAllocationById(id),
        data: jobAllocation.toJson(),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return JobAllocationModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to update job allocation',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to update job allocation',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<void> deleteJobAllocation(int id) async {
    try {
      final response = await _dioClient.dio.delete(
        ApiConstants.jobAllocationById(id),
      );

      if (response.statusCode != 200) {
        throw ServerException(
          message: 'Failed to delete job allocation',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<String> exportToExcel() async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.jobAllocationExportExcel,
      );

      if (response.statusCode == 200) {
        if (response.data['success'] == true) {
          return response.data['data']['download_url'] ?? response.data['data']['file_path'] ?? '';
        } else {
          throw ServerException(
            message: response.data['message'] ?? 'Failed to export job allocations to Excel',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to export job allocations to Excel',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<String> generatePdf(int id) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.jobAllocationPdf(id),
      );

      if (response.statusCode == 200) {
        if (response.data['success'] == true) {
          return response.data['data']['download_url'] ?? response.data['data']['file_path'] ?? '';
        } else {
          throw ServerException(
            message: response.data['message'] ?? 'Failed to generate PDF',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to generate PDF',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAnalysts() async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.jobAllocationAnalysts,
      );

      if (response.statusCode == 200) {
        if (response.data['success'] == true) {
          final List<dynamic> analysts = response.data['data']['analysts'] ?? [];
          return analysts.cast<Map<String, dynamic>>();
        } else {
          throw ServerException(
            message: response.data['message'] ?? 'Failed to get analysts',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get analysts',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
