import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../models/approval/approval_model.dart';
import '../models/test_result/test_result_model.dart';
import '../models/common/test_result_response_model.dart';

abstract class TestResultRemoteDataSource {
  // Core CRUD operations (from Postman collection)
  Future<TestResultsDataModel> getTestResults({
    int page = 1,
    int perPage = 15,
    int? parameterId,
    int? sampleId,
    int? analystId,
    String? status,
  });
  Future<TestResultModel> getTestResultById(int id);
  Future<TestResultModel> addTestResult(Map<String, dynamic> request);
  Future<TestResultModel> updateTestResult(int id, Map<String, dynamic> request);

  // Approval workflow (from Postman collection)
  Future<TestResultModel> approveTestResult(int id, Map<String, dynamic> request);
  Future<TestResultModel> rejectTestResult(int id, Map<String, dynamic> request);

  // Active tests and analyst workflow (from Postman collection)
  Future<List<TestResultModel>> getActiveTests();
  Future<List<TestResultModel>> getMyTests();
  Future<TestResultModel> acceptTest(int id);
  Future<TestResultModel> rejectTest(int id, Map<String, dynamic> request);
  Future<TestResultModel> addResult(int id, Map<String, dynamic> request);
  Future<TestResultModel> reassignTest(int id, Map<String, dynamic> request);
}

class TestResultRemoteDataSourceImpl implements TestResultRemoteDataSource {
  final DioClient _dioClient;

  TestResultRemoteDataSourceImpl(this._dioClient);

  @override
  Future<TestResultsDataModel> getTestResults({
    int page = 1,
    int perPage = 15,
    int? parameterId,
    int? sampleId,
    int? analystId,
    String? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (parameterId != null) queryParams['parameter_id'] = parameterId;
      if (sampleId != null) queryParams['sample_id'] = sampleId;
      if (analystId != null) queryParams['analyst_id'] = analystId;
      if (status != null) queryParams['status'] = status;

      final response = await _dioClient.dio.get(
        ApiConstants.testResults,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = TestResultsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: 'Failed to get test results',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get test results',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestResultModel> getTestResultById(int id) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.testResultById(id),
      );

      if (response.statusCode == 200) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get test result',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get test result',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestResultModel> addTestResult(Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.addTestResult,
        data: request,
      );

      if (response.statusCode == 201) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to add test result',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to add test result',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestResultModel> updateTestResult(int id, Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.put(
        ApiConstants.testResultById(id),
        data: request,
      );

      if (response.statusCode == 200) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to update test result',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to update test result',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestResultModel> approveTestResult(int id, Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.approveTestResult(id),
        data: request,
      );

      if (response.statusCode == 200) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to approve test result',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to approve test result',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestResultModel> rejectTestResult(int id, Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.rejectTestResult(id),
        data: request,
      );

      if (response.statusCode == 200) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to reject test result',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to reject test result',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<List<TestResultModel>> getActiveTests() async {
    try {
      final response = await _dioClient.dio.get(ApiConstants.activeTests);

      if (response.statusCode == 200) {
        final apiResponse = TestResultsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResults;
        } else {
          throw ServerException(
            message: 'Failed to get active tests',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get active tests',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<List<TestResultModel>> getMyTests() async {
    try {
      final response = await _dioClient.dio.get(ApiConstants.listMyTests);

      if (response.statusCode == 200) {
        final apiResponse = TestResultsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResults;
        } else {
          throw ServerException(
            message: 'Failed to get my tests',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get my tests',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestResultModel> acceptTest(int id) async {
    try {
      final response = await _dioClient.dio.post(ApiConstants.acceptTest(id));

      if (response.statusCode == 200) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to accept test',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to accept test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestResultModel> rejectTest(int id, Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.rejectTest(id),
        data: request,
      );

      if (response.statusCode == 200) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to reject test',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to reject test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestResultModel> addResult(int id, Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.addResult(id),
        data: request,
      );

      if (response.statusCode == 200) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to add result',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to add result',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestResultModel> reassignTest(int id, Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.reassignTest(id),
        data: request,
      );

      if (response.statusCode == 200) {
        final apiResponse = TestResultResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.testResult;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to reassign test',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to reassign test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
