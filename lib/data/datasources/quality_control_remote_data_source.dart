import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../models/quality_control/qc_test_model.dart';
import '../models/common/qc_tests_response_model.dart';
import '../models/completed_test/completed_test_model.dart';

abstract class QualityControlRemoteDataSource {
  Future<QCTestsDataModel> getQCTests({
    int page = 1,
    int perPage = 15,
  });
  Future<CompletedTestModel> createQCTest(QCTestRequestModel request);
  Future<CompletedTestModel> approveTest(int testId, ApproveTestRequestModel request);
}

class QualityControlRemoteDataSourceImpl implements QualityControlRemoteDataSource {
  final DioClient _dioClient;

  QualityControlRemoteDataSourceImpl(this._dioClient);

  @override
  Future<QCTestsDataModel> getQCTests({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      final response = await _dioClient.dio.get(
        ApiConstants.qcTests,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = QCTestsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get QC tests',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get QC tests',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<CompletedTestModel> createQCTest(QCTestRequestModel request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.qcTests,
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        final apiResponse = QCTestResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.test;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to create QC test',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to create QC test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<CompletedTestModel> approveTest(int testId, ApproveTestRequestModel request) async {
    try {
      final response = await _dioClient.dio.put(
        ApiConstants.approveQCTest(testId),
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final apiResponse = QCTestResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.test;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to approve test',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to approve test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
