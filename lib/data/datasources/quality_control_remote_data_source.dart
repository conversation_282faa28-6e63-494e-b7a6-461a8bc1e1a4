import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../models/quality_control/qc_test_model.dart';
import '../models/common/qc_tests_response_model.dart';
import '../models/completed_test/completed_test_model.dart';
import '../models/master/parameter_model.dart';

abstract class QualityControlRemoteDataSource {
  Future<QCTestsDataModel> getQCTests({
    int page = 1,
    int perPage = 15,
  });

  // Specific QC operations (from Postman collection)
  Future<CompletedTestModel> addRetest(Map<String, dynamic> request);
  Future<CompletedTestModel> addBlind(Map<String, dynamic> request);
  Future<CompletedTestModel> addReplicate(Map<String, dynamic> request);
  Future<ParameterModel> viewParameter(int id);
}

class QualityControlRemoteDataSourceImpl implements QualityControlRemoteDataSource {
  final DioClient _dioClient;

  QualityControlRemoteDataSourceImpl(this._dioClient);

  @override
  Future<QCTestsDataModel> getQCTests({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      final response = await _dioClient.dio.get(
        ApiConstants.qualityControlTests,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = QCTestsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get QC tests',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get QC tests',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<CompletedTestModel> addRetest(Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.qualityControlRetest,
        data: request,
      );

      if (response.statusCode == 201) {
        final apiResponse = QCTestResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.test;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to add retest',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to add retest',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<CompletedTestModel> addBlind(Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.qualityControlBlind,
        data: request,
      );

      if (response.statusCode == 201) {
        final apiResponse = QCTestResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.test;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to add blind test',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to add blind test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<CompletedTestModel> addReplicate(Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.qualityControlReplicate,
        data: request,
      );

      if (response.statusCode == 201) {
        final apiResponse = QCTestResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.test;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to add replicate test',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to add replicate test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ParameterModel> viewParameter(int id) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.qualityControlParameter(id),
      );

      if (response.statusCode == 200) {
        // Simple response parsing for parameter
        if (response.data['success'] == true) {
          return ParameterModel.fromJson(response.data['data']['parameter']);
        } else {
          throw ServerException(
            message: response.data['message'] ?? 'Failed to view parameter',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to view parameter',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
