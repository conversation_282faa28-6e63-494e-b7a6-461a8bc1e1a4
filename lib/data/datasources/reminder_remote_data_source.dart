import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';

abstract class ReminderRemoteDataSource {
  // Core CRUD operations (from Postman collection)
  Future<Map<String, dynamic>> getReminders({
    int page = 1,
    int perPage = 15,
  });
  Future<Map<String, dynamic>> getReminderById(int id);
  Future<Map<String, dynamic>> createReminder(Map<String, dynamic> request);

  // Active reminders (from Postman collection)
  Future<List<Map<String, dynamic>>> getActiveReminders();
}

class ReminderRemoteDataSourceImpl implements ReminderRemoteDataSource {
  final DioClient _dioClient;

  ReminderRemoteDataSourceImpl(this._dioClient);

  @override
  Future<Map<String, dynamic>> getReminders({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      final response = await _dioClient.dio.get(
        ApiConstants.reminders,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        if (response.data['success'] == true) {
          return response.data['data'];
        } else {
          throw ServerException(
            message: response.data['message'] ?? 'Failed to get reminders',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get reminders',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<Map<String, dynamic>> getReminderById(int id) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.reminderById(id),
      );

      if (response.statusCode == 200) {
        if (response.data['success'] == true) {
          return response.data['data'];
        } else {
          throw ServerException(
            message: response.data['message'] ?? 'Failed to get reminder',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get reminder',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<Map<String, dynamic>> createReminder(Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.reminders,
        data: request,
      );

      if (response.statusCode == 201) {
        if (response.data['success'] == true) {
          return response.data['data'];
        } else {
          throw ServerException(
            message: response.data['message'] ?? 'Failed to create reminder',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to create reminder',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getActiveReminders() async {
    try {
      final response = await _dioClient.dio.get(ApiConstants.activeReminders);

      if (response.statusCode == 200) {
        if (response.data['success'] == true) {
          final List<dynamic> reminders = response.data['data']['reminders'] ?? [];
          return reminders.cast<Map<String, dynamic>>();
        } else {
          throw ServerException(
            message: response.data['message'] ?? 'Failed to get active reminders',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get active reminders',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
