import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../../core/network/api_response.dart';
import '../models/master/parameter_model.dart';
import '../models/master/customer_model.dart';

abstract class MasterRemoteDataSource {
  Future<List<ParameterModel>> getParameters();
  Future<List<CustomerModel>> getCustomers({String? search});
  Future<CustomerModel> addCustomer(CustomerModel customer);
}

class MasterRemoteDataSourceImpl implements MasterRemoteDataSource {
  final DioClient _dioClient;

  MasterRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<ParameterModel>> getParameters() async {
    try {
      final response = await _dioClient.dio.get(ApiConstants.parameters);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return apiResponse.data!
              .map((json) => ParameterModel.fromJson(json as Map<String, dynamic>))
              .toList();
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get parameters',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get parameters',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<List<CustomerModel>> getCustomers({String? search}) async {
    try {
      final queryParams = <String, dynamic>{};
      
      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      final response = await _dioClient.dio.get(
        ApiConstants.customers,
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return apiResponse.data!
              .map((json) => CustomerModel.fromJson(json as Map<String, dynamic>))
              .toList();
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get customers',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get customers',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<CustomerModel> addCustomer(CustomerModel customer) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.customers,
        data: customer.toJson(),
      );

      if (response.statusCode == 201) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return CustomerModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to add customer',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to add customer',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
