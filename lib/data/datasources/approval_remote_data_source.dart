import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../models/approval/approval_model.dart';
import '../models/common/approval_response_model.dart';

abstract class ApprovalRemoteDataSource {
  Future<ApprovalsDataModel> getPendingApprovals({
    int page = 1,
    int perPage = 15,
  });
  Future<ApprovalsDataModel> getApprovalHistory({
    int page = 1,
    int perPage = 15,
  });
  Future<ApprovalModel> getApprovalById(int id);
  Future<ApprovalModel> processApproval(int testId, ApprovalRequestModel request);
  Future<BulkApprovalDataModel> processBulkApproval(BulkApprovalRequestModel request);
  Future<ApprovalModel> reassignTest(int testId, int newApproverId, String? remarks);
}

class ApprovalRemoteDataSourceImpl implements ApprovalRemoteDataSource {
  final DioClient _dioClient;

  ApprovalRemoteDataSourceImpl(this._dioClient);

  @override
  Future<ApprovalsDataModel> getPendingApprovals({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
        'status': 'pending',
      };

      final response = await _dioClient.dio.get(
        ApiConstants.approvals,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: 'Failed to get pending approvals',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get pending approvals',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalsDataModel> getApprovalHistory({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      final response = await _dioClient.dio.get(
        ApiConstants.approvalHistory,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: 'Failed to get approval history',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get approval history',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalModel> getApprovalById(int id) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.approvalById(id),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.approval;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get approval',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get approval',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalModel> processApproval(int testId, ApprovalRequestModel request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.processApproval(testId),
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.approval;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to process approval',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to process approval',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<BulkApprovalDataModel> processBulkApproval(BulkApprovalRequestModel request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.bulkApproval,
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final apiResponse = BulkApprovalResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to process bulk approval',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to process bulk approval',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalModel> reassignTest(int testId, int newApproverId, String? remarks) async {
    try {
      final requestData = <String, dynamic>{
        'approval_status': 'reassigned',
        'reassigned_to': newApproverId,
      };

      if (remarks != null && remarks.isNotEmpty) {
        requestData['approval_remarks'] = remarks;
      }

      final response = await _dioClient.dio.post(
        ApiConstants.reassignTest(testId),
        data: requestData,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.approval;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to reassign test',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to reassign test',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
