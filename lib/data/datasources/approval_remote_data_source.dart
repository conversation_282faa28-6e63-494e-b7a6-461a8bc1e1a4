import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../models/approval/approval_model.dart';
import '../models/common/approval_response_model.dart';

abstract class ApprovalRemoteDataSource {
  // Core approval operations (from Postman collection)
  Future<ApprovalsDataModel> getApprovals({
    int page = 1,
    int perPage = 15,
  });
  Future<ApprovalsDataModel> getPendingApprovals({
    int page = 1,
    int perPage = 15,
  });
  Future<ApprovalsDataModel> getApprovedApprovals({
    int page = 1,
    int perPage = 15,
  });
  Future<ApprovalsDataModel> getRejectedApprovals({
    int page = 1,
    int perPage = 15,
  });
  Future<ApprovalModel> getApprovalById(int id);

  // Approval actions (from Postman collection)
  Future<ApprovalModel> processApproval(int id, ApprovalRequestModel request);
  Future<ApprovalModel> reassignApproval(int id, Map<String, dynamic> request);
  Future<ApprovalModel> rejectApproval(int id, Map<String, dynamic> request);
}

class ApprovalRemoteDataSourceImpl implements ApprovalRemoteDataSource {
  final DioClient _dioClient;

  ApprovalRemoteDataSourceImpl(this._dioClient);

  @override
  Future<ApprovalsDataModel> getApprovals({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      final response = await _dioClient.dio.get(
        ApiConstants.approvals,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: 'Failed to get approvals',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get approvals',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalsDataModel> getPendingApprovals({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      final response = await _dioClient.dio.get(
        ApiConstants.pendingApprovals,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: 'Failed to get pending approvals',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get pending approvals',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalsDataModel> getApprovedApprovals({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      final response = await _dioClient.dio.get(
        ApiConstants.approvedApprovals,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: 'Failed to get approved approvals',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get approved approvals',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalsDataModel> getRejectedApprovals({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      final response = await _dioClient.dio.get(
        ApiConstants.rejectedApprovals,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalsResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data;
        } else {
          throw ServerException(
            message: 'Failed to get rejected approvals',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get rejected approvals',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalModel> getApprovalById(int id) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.approvalById(id),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.approval;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get approval',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get approval',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalModel> processApproval(int id, ApprovalRequestModel request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.processApproval(id),
        data: request,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.approval;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to process approval',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to process approval',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalModel> reassignApproval(int id, Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.reassignApproval(id),
        data: request,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.approval;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to reassign approval',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to reassign approval',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<ApprovalModel> rejectApproval(int id, Map<String, dynamic> request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.rejectApproval(id),
        data: request,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApprovalResponseModel.fromJson(response.data);

        if (apiResponse.success) {
          return apiResponse.data.approval;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to reject approval',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to reject approval',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
