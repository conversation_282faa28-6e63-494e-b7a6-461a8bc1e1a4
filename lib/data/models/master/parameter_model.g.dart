// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parameter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ParameterModel _$ParameterModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'ParameterModel',
      json,
      ($checkedConvert) {
        final val = ParameterModel(
          id: $checkedConvert('id', (v) => (v as num).toInt()),
          name: $checkedConvert('name', (v) => v as String?),
          type: $checkedConvert('type', (v) => v as String?),
          status: $checkedConvert('status', (v) => (v as num?)?.toInt()),
          requirement: $checkedConvert('requirement', (v) => v as String?),
          permissibleLimit: $checkedConvert(
            'permissible_limit',
            (v) => v as String?,
          ),
          protocolUsed: $checkedConvert('protocol_used', (v) => v as String?),
          units: $checkedConvert('units', (v) => v as String?),
          createdAt: $checkedConvert('created_at', (v) => v as String?),
          updatedAt: $checkedConvert('updated_at', (v) => v as String?),
        );
        return val;
      },
      fieldKeyMap: const {
        'permissibleLimit': 'permissible_limit',
        'protocolUsed': 'protocol_used',
        'createdAt': 'created_at',
        'updatedAt': 'updated_at',
      },
    );

Map<String, dynamic> _$ParameterModelToJson(ParameterModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'status': instance.status,
      'requirement': instance.requirement,
      'permissible_limit': instance.permissibleLimit,
      'protocol_used': instance.protocolUsed,
      'units': instance.units,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
