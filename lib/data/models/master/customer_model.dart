import 'package:json_annotation/json_annotation.dart';

part 'customer_model.g.dart';

@JsonSerializable()
class CustomerModel {
  final int id;
  final String? name;
  final String? address;
  @J<PERSON><PERSON><PERSON>(name: 'contact_person')
  final String? contactPerson;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mobile_no')
  final String? mobileNo;
  final String? email;
  @<PERSON><PERSON><PERSON>ey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  const CustomerModel({
    required this.id,
    this.name,
    this.address,
    this.contactPerson,
    this.mobileNo,
    this.email,
    this.createdAt,
    this.updatedAt,
  });

  factory CustomerModel.fromJson(Map<String, dynamic> json) =>
      _$CustomerModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerModelToJson(this);
}
