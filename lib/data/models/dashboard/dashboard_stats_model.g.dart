// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TotalStatsModel _$TotalStatsModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'TotalStatsModel',
      json,
      ($checkedConvert) {
        final val = TotalStatsModel(
          totalTestRequests: $checkedConvert(
            'total_test_requests',
            (v) => (v as num).toInt(),
          ),
          totalJobAllocations: $checkedConvert(
            'total_job_allocations',
            (v) => (v as num).toInt(),
          ),
          totalUsers: $checkedConvert('total_users', (v) => (v as num).toInt()),
        );
        return val;
      },
      fieldKeyMap: const {
        'totalTestRequests': 'total_test_requests',
        'totalJobAllocations': 'total_job_allocations',
        'totalUsers': 'total_users',
      },
    );

Map<String, dynamic> _$TotalStatsModelToJson(TotalStatsModel instance) =>
    <String, dynamic>{
      'total_test_requests': instance.totalTestRequests,
      'total_job_allocations': instance.totalJobAllocations,
      'total_users': instance.totalUsers,
    };

MonthlyStatsModel _$MonthlyStatsModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'MonthlyStatsModel',
      json,
      ($checkedConvert) {
        final val = MonthlyStatsModel(
          monthlyTestRequests: $checkedConvert(
            'monthly_test_requests',
            (v) => (v as num).toInt(),
          ),
          monthlyJobAllocations: $checkedConvert(
            'monthly_job_allocations',
            (v) => (v as num).toInt(),
          ),
        );
        return val;
      },
      fieldKeyMap: const {
        'monthlyTestRequests': 'monthly_test_requests',
        'monthlyJobAllocations': 'monthly_job_allocations',
      },
    );

Map<String, dynamic> _$MonthlyStatsModelToJson(MonthlyStatsModel instance) =>
    <String, dynamic>{
      'monthly_test_requests': instance.monthlyTestRequests,
      'monthly_job_allocations': instance.monthlyJobAllocations,
    };

TaskStatsModel _$TaskStatsModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'TaskStatsModel',
      json,
      ($checkedConvert) {
        final val = TaskStatsModel(
          overdueTasks: $checkedConvert(
            'overdue_tasks',
            (v) => (v as num).toInt(),
          ),
          todayTasks: $checkedConvert('today_tasks', (v) => (v as num).toInt()),
          upcomingTasks: $checkedConvert(
            'upcoming_tasks',
            (v) => (v as num).toInt(),
          ),
        );
        return val;
      },
      fieldKeyMap: const {
        'overdueTasks': 'overdue_tasks',
        'todayTasks': 'today_tasks',
        'upcomingTasks': 'upcoming_tasks',
      },
    );

Map<String, dynamic> _$TaskStatsModelToJson(TaskStatsModel instance) =>
    <String, dynamic>{
      'overdue_tasks': instance.overdueTasks,
      'today_tasks': instance.todayTasks,
      'upcoming_tasks': instance.upcomingTasks,
    };

UserStatsModel _$UserStatsModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'UserStatsModel',
      json,
      ($checkedConvert) {
        final val = UserStatsModel(
          myJobs: $checkedConvert('my_jobs', (v) => (v as num).toInt()),
          myOverdueTasks: $checkedConvert(
            'my_overdue_tasks',
            (v) => (v as num).toInt(),
          ),
        );
        return val;
      },
      fieldKeyMap: const {
        'myJobs': 'my_jobs',
        'myOverdueTasks': 'my_overdue_tasks',
      },
    );

Map<String, dynamic> _$UserStatsModelToJson(UserStatsModel instance) =>
    <String, dynamic>{
      'my_jobs': instance.myJobs,
      'my_overdue_tasks': instance.myOverdueTasks,
    };

DashboardStatsModel _$DashboardStatsModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'DashboardStatsModel',
      json,
      ($checkedConvert) {
        final val = DashboardStatsModel(
          totalStats: $checkedConvert(
            'total_stats',
            (v) => TotalStatsModel.fromJson(v as Map<String, dynamic>),
          ),
          monthlyStats: $checkedConvert(
            'monthly_stats',
            (v) => MonthlyStatsModel.fromJson(v as Map<String, dynamic>),
          ),
          taskStats: $checkedConvert(
            'task_stats',
            (v) => TaskStatsModel.fromJson(v as Map<String, dynamic>),
          ),
          userStats: $checkedConvert(
            'user_stats',
            (v) => UserStatsModel.fromJson(v as Map<String, dynamic>),
          ),
        );
        return val;
      },
      fieldKeyMap: const {
        'totalStats': 'total_stats',
        'monthlyStats': 'monthly_stats',
        'taskStats': 'task_stats',
        'userStats': 'user_stats',
      },
    );

Map<String, dynamic> _$DashboardStatsModelToJson(
  DashboardStatsModel instance,
) => <String, dynamic>{
  'total_stats': instance.totalStats.toJson(),
  'monthly_stats': instance.monthlyStats.toJson(),
  'task_stats': instance.taskStats.toJson(),
  'user_stats': instance.userStats.toJson(),
};
