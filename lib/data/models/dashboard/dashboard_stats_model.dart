import 'package:json_annotation/json_annotation.dart';

part 'dashboard_stats_model.g.dart';

@JsonSerializable()
class TotalStatsModel {
  @JsonKey(name: 'total_test_requests')
  final int totalTestRequests;
  @Json<PERSON>ey(name: 'total_job_allocations')
  final int totalJobAllocations;
  @JsonKey(name: 'total_users')
  final int totalUsers;

  const TotalStatsModel({
    required this.totalTestRequests,
    required this.totalJobAllocations,
    required this.totalUsers,
  });

  factory TotalStatsModel.fromJson(Map<String, dynamic> json) =>
      _$TotalStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$TotalStatsModelToJson(this);
}

@JsonSerializable()
class MonthlyStatsModel {
  @JsonKey(name: 'monthly_test_requests')
  final int monthlyTestRequests;
  @<PERSON>son<PERSON>ey(name: 'monthly_job_allocations')
  final int monthlyJobAllocations;

  const MonthlyStatsModel({
    required this.monthlyTestRequests,
    required this.monthlyJobAllocations,
  });

  factory MonthlyStatsModel.fromJson(Map<String, dynamic> json) =>
      _$MonthlyStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$MonthlyStatsModelToJson(this);
}

@JsonSerializable()
class TaskStatsModel {
  @JsonKey(name: 'overdue_tasks')
  final int overdueTasks;
  @JsonKey(name: 'today_tasks')
  final int todayTasks;
  @JsonKey(name: 'upcoming_tasks')
  final int upcomingTasks;

  const TaskStatsModel({
    required this.overdueTasks,
    required this.todayTasks,
    required this.upcomingTasks,
  });

  factory TaskStatsModel.fromJson(Map<String, dynamic> json) =>
      _$TaskStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$TaskStatsModelToJson(this);
}

@JsonSerializable()
class UserStatsModel {
  @JsonKey(name: 'my_jobs')
  final int myJobs;
  @JsonKey(name: 'my_overdue_tasks')
  final int myOverdueTasks;

  const UserStatsModel({
    required this.myJobs,
    required this.myOverdueTasks,
  });

  factory UserStatsModel.fromJson(Map<String, dynamic> json) =>
      _$UserStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserStatsModelToJson(this);
}

@JsonSerializable()
class DashboardStatsModel {
  @JsonKey(name: 'total_stats')
  final TotalStatsModel totalStats;
  @JsonKey(name: 'monthly_stats')
  final MonthlyStatsModel monthlyStats;
  @JsonKey(name: 'task_stats')
  final TaskStatsModel taskStats;
  @JsonKey(name: 'user_stats')
  final UserStatsModel userStats;

  const DashboardStatsModel({
    required this.totalStats,
    required this.monthlyStats,
    required this.taskStats,
    required this.userStats,
  });

  factory DashboardStatsModel.fromJson(Map<String, dynamic> json) =>
      _$DashboardStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardStatsModelToJson(this);
}
