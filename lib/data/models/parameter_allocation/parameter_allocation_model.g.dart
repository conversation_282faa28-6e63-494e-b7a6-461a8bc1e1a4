// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parameter_allocation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ParameterAllocationModel _$ParameterAllocationModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'ParameterAllocationModel',
  json,
  ($checkedConvert) {
    final val = ParameterAllocationModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      jobDetailId: $checkedConvert('job_detail_id', (v) => (v as num).toInt()),
      sampleId: $checkedConvert('sample_id', (v) => (v as num).toInt()),
      analystId: $checkedConvert('analyst_id', (v) => (v as num).toInt()),
      isRetest: $checkedConvert('is_retest', (v) => (v as num?)?.toInt() ?? 0),
      isBlind: $checkedConvert('is_blind', (v) => (v as num?)?.toInt() ?? 0),
      isReplicate: $checkedConvert(
        'is_replicate',
        (v) => (v as num?)?.toInt() ?? 0,
      ),
      isSpiked: $checkedConvert('is_spiked', (v) => (v as num?)?.toInt() ?? 0),
      spikedResult: $checkedConvert('spiked_result', (v) => v as String?),
      originalAllocationId: $checkedConvert(
        'original_allocation_id',
        (v) => (v as num?)?.toInt(),
      ),
      status: $checkedConvert('status', (v) => v as String?),
      rejectionReason: $checkedConvert('rejection_reason', (v) => v as String?),
      rejectedAt: $checkedConvert('rejected_at', (v) => v as String?),
      createdAt: $checkedConvert('created_at', (v) => v as String?),
      updatedAt: $checkedConvert('updated_at', (v) => v as String?),
      sample: $checkedConvert(
        'sample',
        (v) =>
            v == null ? null : SampleModel.fromJson(v as Map<String, dynamic>),
      ),
      analyst: $checkedConvert(
        'analyst',
        (v) => v == null ? null : UserModel.fromJson(v as Map<String, dynamic>),
      ),
      approvedBy: $checkedConvert(
        'approved_by',
        (v) => v == null ? null : UserModel.fromJson(v as Map<String, dynamic>),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'jobDetailId': 'job_detail_id',
    'sampleId': 'sample_id',
    'analystId': 'analyst_id',
    'isRetest': 'is_retest',
    'isBlind': 'is_blind',
    'isReplicate': 'is_replicate',
    'isSpiked': 'is_spiked',
    'spikedResult': 'spiked_result',
    'originalAllocationId': 'original_allocation_id',
    'rejectionReason': 'rejection_reason',
    'rejectedAt': 'rejected_at',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
    'approvedBy': 'approved_by',
  },
);

Map<String, dynamic> _$ParameterAllocationModelToJson(
  ParameterAllocationModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'job_detail_id': instance.jobDetailId,
  'sample_id': instance.sampleId,
  'analyst_id': instance.analystId,
  'is_retest': instance.isRetest,
  'is_blind': instance.isBlind,
  'is_replicate': instance.isReplicate,
  'is_spiked': instance.isSpiked,
  'spiked_result': instance.spikedResult,
  'original_allocation_id': instance.originalAllocationId,
  'status': instance.status,
  'rejection_reason': instance.rejectionReason,
  'rejected_at': instance.rejectedAt,
  'created_at': instance.createdAt,
  'updated_at': instance.updatedAt,
  'sample': instance.sample?.toJson(),
  'analyst': instance.analyst?.toJson(),
  'approved_by': instance.approvedBy?.toJson(),
};
