import 'package:json_annotation/json_annotation.dart';
import '../test_request/sample_model.dart';
import '../auth/user_model.dart';

part 'parameter_allocation_model.g.dart';

@JsonSerializable()
class ParameterAllocationModel {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'job_detail_id')
  final int jobDetailId;
  @Json<PERSON>ey(name: 'sample_id')
  final int sampleId;
  @<PERSON>sonKey(name: 'analyst_id')
  final int analystId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_retest')
  final int isRetest;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_blind')
  final int isBlind;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_replicate')
  final int isReplicate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_spiked')
  final int isSpiked;
  @<PERSON>son<PERSON>ey(name: 'spiked_result')
  final String? spikedResult;
  @<PERSON>son<PERSON><PERSON>(name: 'original_allocation_id')
  final int? originalAllocationId;
  final String? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'rejection_reason')
  final String? rejectionReason;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'rejected_at')
  final String? rejectedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;
  final SampleModel? sample;
  final UserModel? analyst;
  @JsonKey(name: 'approved_by')
  final UserModel? approvedBy;

  const ParameterAllocationModel({
    required this.id,
    required this.jobDetailId,
    required this.sampleId,
    required this.analystId,
    this.isRetest = 0,
    this.isBlind = 0,
    this.isReplicate = 0,
    this.isSpiked = 0,
    this.spikedResult,
    this.originalAllocationId,
    this.status,
    this.rejectionReason,
    this.rejectedAt,
    this.createdAt,
    this.updatedAt,
    this.sample,
    this.analyst,
    this.approvedBy,
  });

  factory ParameterAllocationModel.fromJson(Map<String, dynamic> json) =>
      _$ParameterAllocationModelFromJson(json);

  Map<String, dynamic> toJson() => _$ParameterAllocationModelToJson(this);
}
