// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'approval_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApprovalModel _$ApprovalModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'ApprovalModel',
  json,
  ($checkedConvert) {
    final val = ApprovalModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      testId: $checkedConvert('test_id', (v) => (v as num).toInt()),
      approverId: $checkedConvert('approver_id', (v) => (v as num).toInt()),
      approvalStatus: $checkedConvert('approval_status', (v) => v as String),
      approvalDate: $checkedConvert('approval_date', (v) => v as String?),
      approvalRemarks: $checkedConvert('approval_remarks', (v) => v as String?),
      reassignedTo: $checkedConvert(
        'reassigned_to',
        (v) => (v as num?)?.toInt(),
      ),
      createdAt: $checkedConvert('created_at', (v) => v as String?),
      updatedAt: $checkedConvert('updated_at', (v) => v as String?),
      approver: $checkedConvert(
        'approver',
        (v) => v == null ? null : UserModel.fromJson(v as Map<String, dynamic>),
      ),
      reassignedUser: $checkedConvert(
        'reassigned_user',
        (v) => v == null ? null : UserModel.fromJson(v as Map<String, dynamic>),
      ),
      test: $checkedConvert(
        'test',
        (v) => v == null
            ? null
            : CompletedTestModel.fromJson(v as Map<String, dynamic>),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'testId': 'test_id',
    'approverId': 'approver_id',
    'approvalStatus': 'approval_status',
    'approvalDate': 'approval_date',
    'approvalRemarks': 'approval_remarks',
    'reassignedTo': 'reassigned_to',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
    'reassignedUser': 'reassigned_user',
  },
);

Map<String, dynamic> _$ApprovalModelToJson(ApprovalModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'test_id': instance.testId,
      'approver_id': instance.approverId,
      'approval_status': instance.approvalStatus,
      'approval_date': instance.approvalDate,
      'approval_remarks': instance.approvalRemarks,
      'reassigned_to': instance.reassignedTo,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'approver': instance.approver?.toJson(),
      'reassigned_user': instance.reassignedUser?.toJson(),
      'test': instance.test?.toJson(),
    };

ApprovalRequestModel _$ApprovalRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'ApprovalRequestModel',
  json,
  ($checkedConvert) {
    final val = ApprovalRequestModel(
      approvalStatus: $checkedConvert('approval_status', (v) => v as String),
      approvalRemarks: $checkedConvert('approval_remarks', (v) => v as String?),
      reassignedTo: $checkedConvert(
        'reassigned_to',
        (v) => (v as num?)?.toInt(),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'approvalStatus': 'approval_status',
    'approvalRemarks': 'approval_remarks',
    'reassignedTo': 'reassigned_to',
  },
);

Map<String, dynamic> _$ApprovalRequestModelToJson(
  ApprovalRequestModel instance,
) => <String, dynamic>{
  'approval_status': instance.approvalStatus,
  'approval_remarks': instance.approvalRemarks,
  'reassigned_to': instance.reassignedTo,
};

BulkApprovalRequestModel _$BulkApprovalRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'BulkApprovalRequestModel',
  json,
  ($checkedConvert) {
    final val = BulkApprovalRequestModel(
      testIds: $checkedConvert(
        'test_ids',
        (v) => (v as List<dynamic>).map((e) => (e as num).toInt()).toList(),
      ),
      approvalStatus: $checkedConvert('approval_status', (v) => v as String),
      approvalRemarks: $checkedConvert('approval_remarks', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'testIds': 'test_ids',
    'approvalStatus': 'approval_status',
    'approvalRemarks': 'approval_remarks',
  },
);

Map<String, dynamic> _$BulkApprovalRequestModelToJson(
  BulkApprovalRequestModel instance,
) => <String, dynamic>{
  'test_ids': instance.testIds,
  'approval_status': instance.approvalStatus,
  'approval_remarks': instance.approvalRemarks,
};
