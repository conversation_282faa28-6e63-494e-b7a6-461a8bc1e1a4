import 'package:json_annotation/json_annotation.dart';
import '../auth/user_model.dart';
import '../completed_test/completed_test_model.dart';

part 'approval_model.g.dart';

@JsonSerializable()
class ApprovalModel {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'test_id')
  final int testId;
  @J<PERSON><PERSON><PERSON>(name: 'approver_id')
  final int approverId;
  @Json<PERSON>ey(name: 'approval_status')
  final String approvalStatus;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'approval_date')
  final String? approvalDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'approval_remarks')
  final String? approvalRemarks;
  @Json<PERSON>ey(name: 'reassigned_to')
  final int? reassignedTo;
  @J<PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final String? updatedAt;
  final UserModel? approver;
  @<PERSON><PERSON><PERSON>ey(name: 'reassigned_user')
  final UserModel? reassignedUser;
  final CompletedTestModel? test;

  const ApprovalModel({
    required this.id,
    required this.testId,
    required this.approverId,
    required this.approvalStatus,
    this.approvalDate,
    this.approvalRemarks,
    this.reassignedTo,
    this.createdAt,
    this.updatedAt,
    this.approver,
    this.reassignedUser,
    this.test,
  });

  factory ApprovalModel.fromJson(Map<String, dynamic> json) =>
      _$ApprovalModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApprovalModelToJson(this);
}

@JsonSerializable()
class ApprovalRequestModel {
  @JsonKey(name: 'approval_status')
  final String approvalStatus;
  @JsonKey(name: 'approval_remarks')
  final String? approvalRemarks;
  @JsonKey(name: 'reassigned_to')
  final int? reassignedTo;

  const ApprovalRequestModel({
    required this.approvalStatus,
    this.approvalRemarks,
    this.reassignedTo,
  });

  factory ApprovalRequestModel.fromJson(Map<String, dynamic> json) =>
      _$ApprovalRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApprovalRequestModelToJson(this);
}

@JsonSerializable()
class BulkApprovalRequestModel {
  @JsonKey(name: 'test_ids')
  final List<int> testIds;
  @JsonKey(name: 'approval_status')
  final String approvalStatus;
  @JsonKey(name: 'approval_remarks')
  final String? approvalRemarks;

  const BulkApprovalRequestModel({
    required this.testIds,
    required this.approvalStatus,
    this.approvalRemarks,
  });

  factory BulkApprovalRequestModel.fromJson(Map<String, dynamic> json) =>
      _$BulkApprovalRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$BulkApprovalRequestModelToJson(this);
}
