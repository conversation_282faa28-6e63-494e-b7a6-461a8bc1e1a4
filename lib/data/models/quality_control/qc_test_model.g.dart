// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'qc_test_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QCTestModel _$QCTestModelFromJson(Map<String, dynamic> json) => $checkedCreate(
  'QCTestModel',
  json,
  ($checkedConvert) {
    final val = QCTestModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      jobDetailId: $checkedConvert('job_detail_id', (v) => (v as num).toInt()),
      testRequestSampleId: $checkedConvert(
        'test_request_sample_id',
        (v) => (v as num).toInt(),
      ),
      result: $checkedConvert('result', (v) => v as String),
      analysisStartDate: $checkedConvert(
        'analysis_start_date',
        (v) => v as String,
      ),
      analysisCompletionDate: $checkedConvert(
        'analysis_completion_date',
        (v) => v as String,
      ),
      analysisSubmissionDate: $checkedConvert(
        'analysis_submission_date',
        (v) => v as String,
      ),
      isRetest: $checkedConvert('is_retest', (v) => v as bool? ?? false),
      isBlind: $checkedConvert('is_blind', (v) => v as bool? ?? false),
      isReplicate: $checkedConvert('is_replicate', (v) => v as bool? ?? false),
      originalTestId: $checkedConvert(
        'original_test_id',
        (v) => (v as num?)?.toInt(),
      ),
      createdAt: $checkedConvert('created_at', (v) => v as String?),
      updatedAt: $checkedConvert('updated_at', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'jobDetailId': 'job_detail_id',
    'testRequestSampleId': 'test_request_sample_id',
    'analysisStartDate': 'analysis_start_date',
    'analysisCompletionDate': 'analysis_completion_date',
    'analysisSubmissionDate': 'analysis_submission_date',
    'isRetest': 'is_retest',
    'isBlind': 'is_blind',
    'isReplicate': 'is_replicate',
    'originalTestId': 'original_test_id',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
  },
);

Map<String, dynamic> _$QCTestModelToJson(QCTestModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'job_detail_id': instance.jobDetailId,
      'test_request_sample_id': instance.testRequestSampleId,
      'result': instance.result,
      'analysis_start_date': instance.analysisStartDate,
      'analysis_completion_date': instance.analysisCompletionDate,
      'analysis_submission_date': instance.analysisSubmissionDate,
      'is_retest': instance.isRetest,
      'is_blind': instance.isBlind,
      'is_replicate': instance.isReplicate,
      'original_test_id': instance.originalTestId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

QCTestRequestModel _$QCTestRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'QCTestRequestModel',
  json,
  ($checkedConvert) {
    final val = QCTestRequestModel(
      jobDetailId: $checkedConvert('job_detail_id', (v) => (v as num).toInt()),
      testRequestSampleId: $checkedConvert(
        'test_request_sample_id',
        (v) => (v as num).toInt(),
      ),
      result: $checkedConvert('result', (v) => v as String),
      analysisStartDate: $checkedConvert(
        'analysis_start_date',
        (v) => v as String,
      ),
      analysisCompletionDate: $checkedConvert(
        'analysis_completion_date',
        (v) => v as String,
      ),
      analysisSubmissionDate: $checkedConvert(
        'analysis_submission_date',
        (v) => v as String,
      ),
      isRetest: $checkedConvert('is_retest', (v) => v as bool? ?? false),
      isBlind: $checkedConvert('is_blind', (v) => v as bool? ?? false),
      isReplicate: $checkedConvert('is_replicate', (v) => v as bool? ?? false),
      originalTestId: $checkedConvert(
        'original_test_id',
        (v) => (v as num?)?.toInt(),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'jobDetailId': 'job_detail_id',
    'testRequestSampleId': 'test_request_sample_id',
    'analysisStartDate': 'analysis_start_date',
    'analysisCompletionDate': 'analysis_completion_date',
    'analysisSubmissionDate': 'analysis_submission_date',
    'isRetest': 'is_retest',
    'isBlind': 'is_blind',
    'isReplicate': 'is_replicate',
    'originalTestId': 'original_test_id',
  },
);

Map<String, dynamic> _$QCTestRequestModelToJson(QCTestRequestModel instance) =>
    <String, dynamic>{
      'job_detail_id': instance.jobDetailId,
      'test_request_sample_id': instance.testRequestSampleId,
      'result': instance.result,
      'analysis_start_date': instance.analysisStartDate,
      'analysis_completion_date': instance.analysisCompletionDate,
      'analysis_submission_date': instance.analysisSubmissionDate,
      'is_retest': instance.isRetest,
      'is_blind': instance.isBlind,
      'is_replicate': instance.isReplicate,
      'original_test_id': instance.originalTestId,
    };

ApproveTestRequestModel _$ApproveTestRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'ApproveTestRequestModel',
  json,
  ($checkedConvert) {
    final val = ApproveTestRequestModel(
      approvalStatus: $checkedConvert('approval_status', (v) => v as String),
      approvalRemarks: $checkedConvert('approval_remarks', (v) => v as String?),
      reassignedTo: $checkedConvert(
        'reassigned_to',
        (v) => (v as num?)?.toInt(),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'approvalStatus': 'approval_status',
    'approvalRemarks': 'approval_remarks',
    'reassignedTo': 'reassigned_to',
  },
);

Map<String, dynamic> _$ApproveTestRequestModelToJson(
  ApproveTestRequestModel instance,
) => <String, dynamic>{
  'approval_status': instance.approvalStatus,
  'approval_remarks': instance.approvalRemarks,
  'reassigned_to': instance.reassignedTo,
};
