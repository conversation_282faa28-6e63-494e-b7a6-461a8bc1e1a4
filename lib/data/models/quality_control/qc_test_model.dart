import 'package:json_annotation/json_annotation.dart';
import 'package:lims_app_flutter/data/models/auth/user_model.dart';

part 'qc_test_model.g.dart';

@JsonSerializable()
class QCTestModel {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'job_detail_id')
  final int jobDetailId;
  @<PERSON>son<PERSON><PERSON>(name: 'test_request_sample_id')
  final int testRequestSampleId;
  final String result;
  @<PERSON>sonKey(name: 'analysis_start_date')
  final String analysisStartDate;
  @<PERSON>son<PERSON>ey(name: 'analysis_completion_date')
  final String analysisCompletionDate;
  @<PERSON>sonKey(name: 'analysis_submission_date')
  final String analysisSubmissionDate;
  @<PERSON>son<PERSON>ey(name: 'is_retest')
  final bool isRetest;
  @<PERSON>son<PERSON>ey(name: 'is_blind')
  final bool isBlind;
  @<PERSON>son<PERSON>ey(name: 'is_replicate')
  final bool isReplicate;
  @<PERSON>sonKey(name: 'original_test_id')
  final int? originalTestId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final String? updatedAt;

  const QCTestModel({
    required this.id,
    required this.jobDetailId,
    required this.testRequestSampleId,
    required this.result,
    required this.analysisStartDate,
    required this.analysisCompletionDate,
    required this.analysisSubmissionDate,
    this.isRetest = false,
    this.isBlind = false,
    this.isReplicate = false,
    this.originalTestId,
    this.createdAt,
    this.updatedAt,
  });

  factory QCTestModel.fromJson(Map<String, dynamic> json) =>
      _$QCTestModelFromJson(json);

  Map<String, dynamic> toJson() => _$QCTestModelToJson(this);
}

@JsonSerializable()
class QCTestRequestModel {
  @JsonKey(name: 'job_detail_id')
  final int jobDetailId;
  @JsonKey(name: 'test_request_sample_id')
  final int testRequestSampleId;
  final String result;
  @JsonKey(name: 'analysis_start_date')
  final String analysisStartDate;
  @JsonKey(name: 'analysis_completion_date')
  final String analysisCompletionDate;
  @JsonKey(name: 'analysis_submission_date')
  final String analysisSubmissionDate;
  @JsonKey(name: 'is_retest')
  final bool isRetest;
  @JsonKey(name: 'is_blind')
  final bool isBlind;
  @JsonKey(name: 'is_replicate')
  final bool isReplicate;
  @JsonKey(name: 'original_test_id')
  final int? originalTestId;

  const QCTestRequestModel({
    required this.jobDetailId,
    required this.testRequestSampleId,
    required this.result,
    required this.analysisStartDate,
    required this.analysisCompletionDate,
    required this.analysisSubmissionDate,
    this.isRetest = false,
    this.isBlind = false,
    this.isReplicate = false,
    this.originalTestId,
  });

  factory QCTestRequestModel.fromJson(Map<String, dynamic> json) =>
      _$QCTestRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$QCTestRequestModelToJson(this);
}

@JsonSerializable()
class ApproveTestRequestModel {
  @JsonKey(name: 'approval_status')
  final String approvalStatus;
  @JsonKey(name: 'approval_remarks')
  final String? approvalRemarks;
  @JsonKey(name: 'reassigned_to')
  final UserModel? reassignedTo;

  const ApproveTestRequestModel({
    required this.approvalStatus,
    this.approvalRemarks,
    this.reassignedTo,
  });

  factory ApproveTestRequestModel.fromJson(Map<String, dynamic> json) =>
      _$ApproveTestRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApproveTestRequestModelToJson(this);
}
