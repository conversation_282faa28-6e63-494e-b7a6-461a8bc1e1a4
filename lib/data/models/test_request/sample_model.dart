import 'package:json_annotation/json_annotation.dart';
import '../master/parameter_model.dart';

part 'sample_model.g.dart';

@JsonSerializable()
class SampleModel {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'parameter_id')
  final int parameterId;
  final String? particulars;
  final String? type;
  final String? quantity;
  final String? remarks;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'test_request_id')
  final int? testRequestId;
  final ParameterModel? parameter; // Nested parameter object
  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final String? updatedAt;

  factory SampleModel.fromJson(Map<String, dynamic> json) =>
      _$SampleModelFromJson(json);

  const SampleModel({
    required this.id,
    required this.parameterId,
    this.particulars,
    this.type,
    this.quantity,
    this.remarks,
    this.testRequestId,
    this.parameter,
    this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toJson() => _$SampleModelToJson(this);
}
