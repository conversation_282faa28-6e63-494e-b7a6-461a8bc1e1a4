// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'test_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TestRequestModel _$TestRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'TestRequestModel',
  json,
  ($checkedConvert) {
    final val = TestRequestModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      requestNumber: $checkedConvert('request_number', (v) => v as String?),
      requestDate: $checkedConvert('request_date', (v) => v as String?),
      customerName: $checkedConvert('customer_name', (v) => v as String?),
      customerAddress: $checkedConvert('customer_address', (v) => v as String?),
      contactPerson: $checkedConvert('contact_person', (v) => v as String?),
      mobileNo: $checkedConvert('mobile_no', (v) => v as String?),
      email: $checkedConvert('email', (v) => v as String?),
      specialRequest: $checkedConvert('special_request', (v) => v as String?),
      submittedByName: $checkedConvert(
        'submitted_by_name',
        (v) => v as String?,
      ),
      submittedByDesignation: $checkedConvert(
        'submitted_by_designation',
        (v) => v as String?,
      ),
      submittedByDate: $checkedConvert(
        'submitted_by_date',
        (v) => v as String?,
      ),
      submittedByIdProof: $checkedConvert(
        'submitted_by_id_proof',
        (v) => v as String?,
      ),
      receivedByName: $checkedConvert('received_by_name', (v) => v as String?),
      receivedByDesignation: $checkedConvert(
        'received_by_designation',
        (v) => v as String?,
      ),
      receivedByDate: $checkedConvert('received_by_date', (v) => v as String?),
      receivedByIdProof: $checkedConvert(
        'received_by_id_proof',
        (v) => v as String?,
      ),
      sampleReceivedTime: $checkedConvert(
        'sample_received_time',
        (v) => v as String?,
      ),
      sampleCollectionTime: $checkedConvert(
        'sample_collection_time',
        (v) => v as String?,
      ),
      quantityOfSample: $checkedConvert(
        'quantity_of_sample',
        (v) => v as String?,
      ),
      typeOfSample: $checkedConvert('type_of_sample', (v) => v as String?),
      sampleDetails: $checkedConvert('sample_details', (v) => v as String?),
      sampleCode: $checkedConvert('sample_code', (v) => v as String?),
      samples: $checkedConvert(
        'samples',
        (v) => (v as List<dynamic>?)
            ?.map((e) => SampleModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      ),
      deleted: $checkedConvert('deleted', (v) => (v as num?)?.toInt()),
      createdAt: $checkedConvert('created_at', (v) => v as String?),
      updatedAt: $checkedConvert('updated_at', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'requestNumber': 'request_number',
    'requestDate': 'request_date',
    'customerName': 'customer_name',
    'customerAddress': 'customer_address',
    'contactPerson': 'contact_person',
    'mobileNo': 'mobile_no',
    'specialRequest': 'special_request',
    'submittedByName': 'submitted_by_name',
    'submittedByDesignation': 'submitted_by_designation',
    'submittedByDate': 'submitted_by_date',
    'submittedByIdProof': 'submitted_by_id_proof',
    'receivedByName': 'received_by_name',
    'receivedByDesignation': 'received_by_designation',
    'receivedByDate': 'received_by_date',
    'receivedByIdProof': 'received_by_id_proof',
    'sampleReceivedTime': 'sample_received_time',
    'sampleCollectionTime': 'sample_collection_time',
    'quantityOfSample': 'quantity_of_sample',
    'typeOfSample': 'type_of_sample',
    'sampleDetails': 'sample_details',
    'sampleCode': 'sample_code',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
  },
);

Map<String, dynamic> _$TestRequestModelToJson(TestRequestModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'request_number': instance.requestNumber,
      'request_date': instance.requestDate,
      'customer_name': instance.customerName,
      'customer_address': instance.customerAddress,
      'contact_person': instance.contactPerson,
      'mobile_no': instance.mobileNo,
      'email': instance.email,
      'special_request': instance.specialRequest,
      'submitted_by_name': instance.submittedByName,
      'submitted_by_designation': instance.submittedByDesignation,
      'submitted_by_date': instance.submittedByDate,
      'submitted_by_id_proof': instance.submittedByIdProof,
      'received_by_name': instance.receivedByName,
      'received_by_designation': instance.receivedByDesignation,
      'received_by_date': instance.receivedByDate,
      'received_by_id_proof': instance.receivedByIdProof,
      'sample_received_time': instance.sampleReceivedTime,
      'sample_collection_time': instance.sampleCollectionTime,
      'quantity_of_sample': instance.quantityOfSample,
      'type_of_sample': instance.typeOfSample,
      'sample_details': instance.sampleDetails,
      'sample_code': instance.sampleCode,
      'samples': instance.samples?.map((e) => e.toJson()).toList(),
      'deleted': instance.deleted,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
