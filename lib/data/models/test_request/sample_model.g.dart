// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sample_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SampleModel _$SampleModelFromJson(Map<String, dynamic> json) => $checkedCreate(
  'SampleModel',
  json,
  ($checkedConvert) {
    final val = SampleModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      parameterId: $checkedConvert('parameter_id', (v) => (v as num).toInt()),
      particulars: $checkedConvert('particulars', (v) => v as String?),
      type: $checkedConvert('type', (v) => v as String?),
      quantity: $checkedConvert('quantity', (v) => v as String?),
      remarks: $checkedConvert('remarks', (v) => v as String?),
      testRequestId: $checkedConvert(
        'test_request_id',
        (v) => (v as num?)?.toInt(),
      ),
      parameter: $checkedConvert(
        'parameter',
        (v) => v == null
            ? null
            : ParameterModel.fromJson(v as Map<String, dynamic>),
      ),
      createdAt: $checkedConvert('created_at', (v) => v as String?),
      updatedAt: $checkedConvert('updated_at', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'parameterId': 'parameter_id',
    'testRequestId': 'test_request_id',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
  },
);

Map<String, dynamic> _$SampleModelToJson(SampleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'parameter_id': instance.parameterId,
      'particulars': instance.particulars,
      'type': instance.type,
      'quantity': instance.quantity,
      'remarks': instance.remarks,
      'test_request_id': instance.testRequestId,
      'parameter': instance.parameter?.toJson(),
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
