import 'package:json_annotation/json_annotation.dart';
import '../job_allocation/job_allocation_model.dart';

part 'analyst_test_model.g.dart';

@JsonSerializable()
class AnalystTestModel extends JobAllocationModel {
  @JsonKey(name: 'parameter_allocations')
  final List<AnalystParameterAllocationModel>? parameterAllocations;

  const AnalystTestModel({
    required super.id,
    required super.serialNo,
    required super.creationDate,
    required super.codeNumber,
    required super.nature,
    required super.quantity,
    required super.collectionDate,
    required super.submissionDate,
    required super.dueDate,
    required super.userId,
    required super.testRequestId,
    super.reportType,
    required super.designation,
    super.remarks,
    required super.nablStatus,
    super.deleted = 0,
    super.createdAt,
    super.updatedAt,
    this.parameterAllocations,
  });

  factory AnalystTestModel.fromJson(Map<String, dynamic> json) =>
      _$AnalystTestModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AnalystTestModelToJson(this);
}

@JsonSerializable()
class AnalystParameterAllocationModel {
  final int id;
  @JsonKey(name: 'job_detail_id')
  final int jobDetailId;
  @JsonKey(name: 'sample_id')
  final int sampleId;
  @JsonKey(name: 'analyst_id')
  final int analystId;
  @JsonKey(name: 'is_retest')
  final int isRetest;
  @JsonKey(name: 'is_blind')
  final int isBlind;
  @JsonKey(name: 'is_replicate')
  final int isReplicate;
  final String? status;
  @JsonKey(name: 'rejection_reason')
  final String? rejectionReason;
  @JsonKey(name: 'rejected_at')
  final String? rejectedAt;

  const AnalystParameterAllocationModel({
    required this.id,
    required this.jobDetailId,
    required this.sampleId,
    required this.analystId,
    this.isRetest = 0,
    this.isBlind = 0,
    this.isReplicate = 0,
    this.status,
    this.rejectionReason,
    this.rejectedAt,
  });

  factory AnalystParameterAllocationModel.fromJson(Map<String, dynamic> json) =>
      _$AnalystParameterAllocationModelFromJson(json);

  Map<String, dynamic> toJson() => _$AnalystParameterAllocationModelToJson(this);
}

@JsonSerializable()
class SubmitTestResultRequestModel {
  final String result;
  @JsonKey(name: 'analysis_start_date')
  final String analysisStartDate;
  @JsonKey(name: 'analysis_completion_date')
  final String analysisCompletionDate;
  @JsonKey(name: 'analysis_submission_date')
  final String analysisSubmissionDate;
  @JsonKey(name: 'raw_water')
  final String? rawWater;
  @JsonKey(name: 'filtered_water')
  final String? filteredWater;
  @JsonKey(name: 'treated_water')
  final String? treatedWater;
  @JsonKey(name: 'location_1')
  final String? location1;
  @JsonKey(name: 'location_2')
  final String? location2;
  @JsonKey(name: 'location_3')
  final String? location3;
  @JsonKey(name: 'location_4')
  final String? location4;
  @JsonKey(name: 'location_5')
  final String? location5;
  @JsonKey(name: 'location_6')
  final String? location6;

  const SubmitTestResultRequestModel({
    required this.result,
    required this.analysisStartDate,
    required this.analysisCompletionDate,
    required this.analysisSubmissionDate,
    this.rawWater,
    this.filteredWater,
    this.treatedWater,
    this.location1,
    this.location2,
    this.location3,
    this.location4,
    this.location5,
    this.location6,
  });

  factory SubmitTestResultRequestModel.fromJson(Map<String, dynamic> json) =>
      _$SubmitTestResultRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$SubmitTestResultRequestModelToJson(this);
}

@JsonSerializable()
class RejectTestRequestModel {
  @JsonKey(name: 'rejection_reason')
  final String rejectionReason;

  const RejectTestRequestModel({
    required this.rejectionReason,
  });

  factory RejectTestRequestModel.fromJson(Map<String, dynamic> json) =>
      _$RejectTestRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$RejectTestRequestModelToJson(this);
}
