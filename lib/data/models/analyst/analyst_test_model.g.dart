// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analyst_test_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalystTestModel _$AnalystTestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'AnalystTestModel',
  json,
  ($checkedConvert) {
    final val = AnalystTestModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      serialNo: $checkedConvert('serial_no', (v) => v as String),
      creationDate: $checkedConvert('creation_date', (v) => v as String),
      codeNumber: $checkedConvert('code_number', (v) => v as String),
      nature: $checkedConvert('nature', (v) => v as String),
      quantity: $checkedConvert('quantity', (v) => v as String),
      collectionDate: $checkedConvert('collection_date', (v) => v as String),
      submissionDate: $checkedConvert('submission_date', (v) => v as String),
      dueDate: $checkedConvert('due_date', (v) => v as String),
      userId: $checkedConvert('user_id', (v) => (v as num).toInt()),
      testRequestId: $checkedConvert(
        'test_request_id',
        (v) => (v as num).toInt(),
      ),
      reportType: $checkedConvert('report_type', (v) => v as String?),
      designation: $checkedConvert('designation', (v) => v as String),
      remarks: $checkedConvert('remarks', (v) => v as String?),
      nablStatus: $checkedConvert('nabl_status', (v) => v as String),
      deleted: $checkedConvert('deleted', (v) => (v as num?)?.toInt() ?? 0),
      createdAt: $checkedConvert('created_at', (v) => v as String?),
      updatedAt: $checkedConvert('updated_at', (v) => v as String?),
      parameterAllocations: $checkedConvert(
        'parameter_allocations',
        (v) => (v as List<dynamic>?)
            ?.map(
              (e) => AnalystParameterAllocationModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList(),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'serialNo': 'serial_no',
    'creationDate': 'creation_date',
    'codeNumber': 'code_number',
    'collectionDate': 'collection_date',
    'submissionDate': 'submission_date',
    'dueDate': 'due_date',
    'userId': 'user_id',
    'testRequestId': 'test_request_id',
    'reportType': 'report_type',
    'nablStatus': 'nabl_status',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
    'parameterAllocations': 'parameter_allocations',
  },
);

Map<String, dynamic> _$AnalystTestModelToJson(AnalystTestModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'serial_no': instance.serialNo,
      'creation_date': instance.creationDate,
      'code_number': instance.codeNumber,
      'nature': instance.nature,
      'quantity': instance.quantity,
      'collection_date': instance.collectionDate,
      'submission_date': instance.submissionDate,
      'due_date': instance.dueDate,
      'user_id': instance.userId,
      'test_request_id': instance.testRequestId,
      'report_type': instance.reportType,
      'designation': instance.designation,
      'remarks': instance.remarks,
      'nabl_status': instance.nablStatus,
      'deleted': instance.deleted,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'parameter_allocations': instance.parameterAllocations
          ?.map((e) => e.toJson())
          .toList(),
    };

AnalystParameterAllocationModel _$AnalystParameterAllocationModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'AnalystParameterAllocationModel',
  json,
  ($checkedConvert) {
    final val = AnalystParameterAllocationModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      jobDetailId: $checkedConvert('job_detail_id', (v) => (v as num).toInt()),
      sampleId: $checkedConvert('sample_id', (v) => (v as num).toInt()),
      analystId: $checkedConvert('analyst_id', (v) => (v as num).toInt()),
      isRetest: $checkedConvert('is_retest', (v) => (v as num?)?.toInt() ?? 0),
      isBlind: $checkedConvert('is_blind', (v) => (v as num?)?.toInt() ?? 0),
      isReplicate: $checkedConvert(
        'is_replicate',
        (v) => (v as num?)?.toInt() ?? 0,
      ),
      status: $checkedConvert('status', (v) => v as String?),
      rejectionReason: $checkedConvert('rejection_reason', (v) => v as String?),
      rejectedAt: $checkedConvert('rejected_at', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'jobDetailId': 'job_detail_id',
    'sampleId': 'sample_id',
    'analystId': 'analyst_id',
    'isRetest': 'is_retest',
    'isBlind': 'is_blind',
    'isReplicate': 'is_replicate',
    'rejectionReason': 'rejection_reason',
    'rejectedAt': 'rejected_at',
  },
);

Map<String, dynamic> _$AnalystParameterAllocationModelToJson(
  AnalystParameterAllocationModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'job_detail_id': instance.jobDetailId,
  'sample_id': instance.sampleId,
  'analyst_id': instance.analystId,
  'is_retest': instance.isRetest,
  'is_blind': instance.isBlind,
  'is_replicate': instance.isReplicate,
  'status': instance.status,
  'rejection_reason': instance.rejectionReason,
  'rejected_at': instance.rejectedAt,
};

SubmitTestResultRequestModel _$SubmitTestResultRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'SubmitTestResultRequestModel',
  json,
  ($checkedConvert) {
    final val = SubmitTestResultRequestModel(
      result: $checkedConvert('result', (v) => v as String),
      analysisStartDate: $checkedConvert(
        'analysis_start_date',
        (v) => v as String,
      ),
      analysisCompletionDate: $checkedConvert(
        'analysis_completion_date',
        (v) => v as String,
      ),
      analysisSubmissionDate: $checkedConvert(
        'analysis_submission_date',
        (v) => v as String,
      ),
      rawWater: $checkedConvert('raw_water', (v) => v as String?),
      filteredWater: $checkedConvert('filtered_water', (v) => v as String?),
      treatedWater: $checkedConvert('treated_water', (v) => v as String?),
      location1: $checkedConvert('location_1', (v) => v as String?),
      location2: $checkedConvert('location_2', (v) => v as String?),
      location3: $checkedConvert('location_3', (v) => v as String?),
      location4: $checkedConvert('location_4', (v) => v as String?),
      location5: $checkedConvert('location_5', (v) => v as String?),
      location6: $checkedConvert('location_6', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'analysisStartDate': 'analysis_start_date',
    'analysisCompletionDate': 'analysis_completion_date',
    'analysisSubmissionDate': 'analysis_submission_date',
    'rawWater': 'raw_water',
    'filteredWater': 'filtered_water',
    'treatedWater': 'treated_water',
    'location1': 'location_1',
    'location2': 'location_2',
    'location3': 'location_3',
    'location4': 'location_4',
    'location5': 'location_5',
    'location6': 'location_6',
  },
);

Map<String, dynamic> _$SubmitTestResultRequestModelToJson(
  SubmitTestResultRequestModel instance,
) => <String, dynamic>{
  'result': instance.result,
  'analysis_start_date': instance.analysisStartDate,
  'analysis_completion_date': instance.analysisCompletionDate,
  'analysis_submission_date': instance.analysisSubmissionDate,
  'raw_water': instance.rawWater,
  'filtered_water': instance.filteredWater,
  'treated_water': instance.treatedWater,
  'location_1': instance.location1,
  'location_2': instance.location2,
  'location_3': instance.location3,
  'location_4': instance.location4,
  'location_5': instance.location5,
  'location_6': instance.location6,
};

RejectTestRequestModel _$RejectTestRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'RejectTestRequestModel',
  json,
  ($checkedConvert) {
    final val = RejectTestRequestModel(
      rejectionReason: $checkedConvert('rejection_reason', (v) => v as String),
    );
    return val;
  },
  fieldKeyMap: const {'rejectionReason': 'rejection_reason'},
);

Map<String, dynamic> _$RejectTestRequestModelToJson(
  RejectTestRequestModel instance,
) => <String, dynamic>{'rejection_reason': instance.rejectionReason};
