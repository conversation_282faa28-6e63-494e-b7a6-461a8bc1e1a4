// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'job_allocation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JobAllocationModel _$JobAllocationModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'JobAllocationModel',
  json,
  ($checkedConvert) {
    final val = JobAllocationModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      serialNo: $checkedConvert('serial_no', (v) => v as String),
      creationDate: $checkedConvert('creation_date', (v) => v as String),
      codeNumber: $checkedConvert('code_number', (v) => v as String),
      nature: $checkedConvert('nature', (v) => v as String),
      quantity: $checkedConvert('quantity', (v) => v as String),
      collectionDate: $checkedConvert('collection_date', (v) => v as String),
      submissionDate: $checkedConvert('submission_date', (v) => v as String),
      dueDate: $checkedConvert('due_date', (v) => v as String),
      userId: $checkedConvert('user_id', (v) => (v as num).toInt()),
      testRequestId: $checkedConvert(
        'test_request_id',
        (v) => (v as num).toInt(),
      ),
      reportType: $checkedConvert('report_type', (v) => v as String?),
      designation: $checkedConvert('designation', (v) => v as String),
      remarks: $checkedConvert('remarks', (v) => v as String?),
      nablStatus: $checkedConvert('nabl_status', (v) => v as String),
      deleted: $checkedConvert('deleted', (v) => (v as num?)?.toInt() ?? 0),
      createdAt: $checkedConvert('created_at', (v) => v as String?),
      updatedAt: $checkedConvert('updated_at', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'serialNo': 'serial_no',
    'creationDate': 'creation_date',
    'codeNumber': 'code_number',
    'collectionDate': 'collection_date',
    'submissionDate': 'submission_date',
    'dueDate': 'due_date',
    'userId': 'user_id',
    'testRequestId': 'test_request_id',
    'reportType': 'report_type',
    'nablStatus': 'nabl_status',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
  },
);

Map<String, dynamic> _$JobAllocationModelToJson(JobAllocationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'serial_no': instance.serialNo,
      'creation_date': instance.creationDate,
      'code_number': instance.codeNumber,
      'nature': instance.nature,
      'quantity': instance.quantity,
      'collection_date': instance.collectionDate,
      'submission_date': instance.submissionDate,
      'due_date': instance.dueDate,
      'user_id': instance.userId,
      'test_request_id': instance.testRequestId,
      'report_type': instance.reportType,
      'designation': instance.designation,
      'remarks': instance.remarks,
      'nabl_status': instance.nablStatus,
      'deleted': instance.deleted,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
