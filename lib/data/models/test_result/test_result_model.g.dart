// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'test_result_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TestResultModel _$TestResultModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'TestResultModel',
  json,
  ($checkedConvert) {
    final val = TestResultModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      parameterAllocationId: $checkedConvert(
        'parameter_allocation_id',
        (v) => (v as num).toInt(),
      ),
      parameterId: $checkedConvert('parameter_id', (v) => (v as num).toInt()),
      sampleId: $checkedConvert('sample_id', (v) => (v as num).toInt()),
      analystId: $checkedConvert('analyst_id', (v) => (v as num).toInt()),
      result: $checkedConvert('result', (v) => v as String),
      unit: $checkedConvert('unit', (v) => v as String?),
      testMethod: $checkedConvert('test_method', (v) => v as String?),
      instrumentUsed: $checkedConvert('instrument_used', (v) => v as String?),
      analysisDate: $checkedConvert('analysis_date', (v) => v as String?),
      resultStatus: $checkedConvert(
        'result_status',
        (v) => v as String? ?? 'pending',
      ),
      isWithinLimit: $checkedConvert(
        'is_within_limit',
        (v) => v as bool? ?? true,
      ),
      lowerLimit: $checkedConvert('lower_limit', (v) => v as String?),
      upperLimit: $checkedConvert('upper_limit', (v) => v as String?),
      detectionLimit: $checkedConvert('detection_limit', (v) => v as String?),
      uncertainty: $checkedConvert('uncertainty', (v) => v as String?),
      dilutionFactor: $checkedConvert('dilution_factor', (v) => v as String?),
      remarks: $checkedConvert('remarks', (v) => v as String?),
      createdAt: $checkedConvert('created_at', (v) => v as String?),
      updatedAt: $checkedConvert('updated_at', (v) => v as String?),
      parameter: $checkedConvert(
        'parameter',
        (v) => v == null
            ? null
            : ParameterModel.fromJson(v as Map<String, dynamic>),
      ),
      sample: $checkedConvert(
        'sample',
        (v) =>
            v == null ? null : SampleModel.fromJson(v as Map<String, dynamic>),
      ),
      analyst: $checkedConvert(
        'analyst',
        (v) => v == null ? null : UserModel.fromJson(v as Map<String, dynamic>),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'parameterAllocationId': 'parameter_allocation_id',
    'parameterId': 'parameter_id',
    'sampleId': 'sample_id',
    'analystId': 'analyst_id',
    'testMethod': 'test_method',
    'instrumentUsed': 'instrument_used',
    'analysisDate': 'analysis_date',
    'resultStatus': 'result_status',
    'isWithinLimit': 'is_within_limit',
    'lowerLimit': 'lower_limit',
    'upperLimit': 'upper_limit',
    'detectionLimit': 'detection_limit',
    'dilutionFactor': 'dilution_factor',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
  },
);

Map<String, dynamic> _$TestResultModelToJson(TestResultModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'parameter_allocation_id': instance.parameterAllocationId,
      'parameter_id': instance.parameterId,
      'sample_id': instance.sampleId,
      'analyst_id': instance.analystId,
      'result': instance.result,
      'unit': instance.unit,
      'test_method': instance.testMethod,
      'instrument_used': instance.instrumentUsed,
      'analysis_date': instance.analysisDate,
      'result_status': instance.resultStatus,
      'is_within_limit': instance.isWithinLimit,
      'lower_limit': instance.lowerLimit,
      'upper_limit': instance.upperLimit,
      'detection_limit': instance.detectionLimit,
      'uncertainty': instance.uncertainty,
      'dilution_factor': instance.dilutionFactor,
      'remarks': instance.remarks,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'parameter': instance.parameter?.toJson(),
      'sample': instance.sample?.toJson(),
      'analyst': instance.analyst?.toJson(),
    };

CreateTestResultRequestModel _$CreateTestResultRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'CreateTestResultRequestModel',
  json,
  ($checkedConvert) {
    final val = CreateTestResultRequestModel(
      parameterAllocationId: $checkedConvert(
        'parameter_allocation_id',
        (v) => (v as num).toInt(),
      ),
      result: $checkedConvert('result', (v) => v as String),
      unit: $checkedConvert('unit', (v) => v as String?),
      testMethod: $checkedConvert('test_method', (v) => v as String?),
      instrumentUsed: $checkedConvert('instrument_used', (v) => v as String?),
      analysisDate: $checkedConvert('analysis_date', (v) => v as String),
      lowerLimit: $checkedConvert('lower_limit', (v) => v as String?),
      upperLimit: $checkedConvert('upper_limit', (v) => v as String?),
      detectionLimit: $checkedConvert('detection_limit', (v) => v as String?),
      uncertainty: $checkedConvert('uncertainty', (v) => v as String?),
      dilutionFactor: $checkedConvert('dilution_factor', (v) => v as String?),
      remarks: $checkedConvert('remarks', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'parameterAllocationId': 'parameter_allocation_id',
    'testMethod': 'test_method',
    'instrumentUsed': 'instrument_used',
    'analysisDate': 'analysis_date',
    'lowerLimit': 'lower_limit',
    'upperLimit': 'upper_limit',
    'detectionLimit': 'detection_limit',
    'dilutionFactor': 'dilution_factor',
  },
);

Map<String, dynamic> _$CreateTestResultRequestModelToJson(
  CreateTestResultRequestModel instance,
) => <String, dynamic>{
  'parameter_allocation_id': instance.parameterAllocationId,
  'result': instance.result,
  'unit': instance.unit,
  'test_method': instance.testMethod,
  'instrument_used': instance.instrumentUsed,
  'analysis_date': instance.analysisDate,
  'lower_limit': instance.lowerLimit,
  'upper_limit': instance.upperLimit,
  'detection_limit': instance.detectionLimit,
  'uncertainty': instance.uncertainty,
  'dilution_factor': instance.dilutionFactor,
  'remarks': instance.remarks,
};

UpdateTestResultRequestModel _$UpdateTestResultRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'UpdateTestResultRequestModel',
  json,
  ($checkedConvert) {
    final val = UpdateTestResultRequestModel(
      result: $checkedConvert('result', (v) => v as String),
      unit: $checkedConvert('unit', (v) => v as String?),
      testMethod: $checkedConvert('test_method', (v) => v as String?),
      instrumentUsed: $checkedConvert('instrument_used', (v) => v as String?),
      analysisDate: $checkedConvert('analysis_date', (v) => v as String),
      lowerLimit: $checkedConvert('lower_limit', (v) => v as String?),
      upperLimit: $checkedConvert('upper_limit', (v) => v as String?),
      detectionLimit: $checkedConvert('detection_limit', (v) => v as String?),
      uncertainty: $checkedConvert('uncertainty', (v) => v as String?),
      dilutionFactor: $checkedConvert('dilution_factor', (v) => v as String?),
      remarks: $checkedConvert('remarks', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'testMethod': 'test_method',
    'instrumentUsed': 'instrument_used',
    'analysisDate': 'analysis_date',
    'lowerLimit': 'lower_limit',
    'upperLimit': 'upper_limit',
    'detectionLimit': 'detection_limit',
    'dilutionFactor': 'dilution_factor',
  },
);

Map<String, dynamic> _$UpdateTestResultRequestModelToJson(
  UpdateTestResultRequestModel instance,
) => <String, dynamic>{
  'result': instance.result,
  'unit': instance.unit,
  'test_method': instance.testMethod,
  'instrument_used': instance.instrumentUsed,
  'analysis_date': instance.analysisDate,
  'lower_limit': instance.lowerLimit,
  'upper_limit': instance.upperLimit,
  'detection_limit': instance.detectionLimit,
  'uncertainty': instance.uncertainty,
  'dilution_factor': instance.dilutionFactor,
  'remarks': instance.remarks,
};
