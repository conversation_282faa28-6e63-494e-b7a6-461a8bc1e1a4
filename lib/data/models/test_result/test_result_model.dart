import 'package:json_annotation/json_annotation.dart';
import '../master/parameter_model.dart';
import '../test_request/sample_model.dart';
import '../auth/user_model.dart';

part 'test_result_model.g.dart';

@JsonSerializable()
class TestResultModel {
  final int id;
  @Json<PERSON><PERSON>(name: 'parameter_allocation_id')
  final int parameterAllocationId;
  @<PERSON>son<PERSON>ey(name: 'parameter_id')
  final int parameterId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sample_id')
  final int sampleId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'analyst_id')
  final int analystId;
  final String result;
  final String? unit;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'test_method')
  final String? testMethod;
  @J<PERSON><PERSON><PERSON>(name: 'instrument_used')
  final String? instrumentUsed;
  @Json<PERSON>ey(name: 'analysis_date')
  final String? analysisDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'result_status')
  final String resultStatus;
  @<PERSON>sonKey(name: 'is_within_limit')
  final bool isWithinLimit;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'lower_limit')
  final String? lowerLimit;
  @J<PERSON><PERSON><PERSON>(name: 'upper_limit')
  final String? upperLimit;
  @Json<PERSON><PERSON>(name: 'detection_limit')
  final String? detectionLimit;
  @Json<PERSON><PERSON>(name: 'uncertainty')
  final String? uncertainty;
  @JsonKey(name: 'dilution_factor')
  final String? dilutionFactor;
  final String? remarks;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;
  final ParameterModel? parameter;
  final SampleModel? sample;
  final UserModel? analyst;

  const TestResultModel({
    required this.id,
    required this.parameterAllocationId,
    required this.parameterId,
    required this.sampleId,
    required this.analystId,
    required this.result,
    this.unit,
    this.testMethod,
    this.instrumentUsed,
    this.analysisDate,
    this.resultStatus = 'pending',
    this.isWithinLimit = true,
    this.lowerLimit,
    this.upperLimit,
    this.detectionLimit,
    this.uncertainty,
    this.dilutionFactor,
    this.remarks,
    this.createdAt,
    this.updatedAt,
    this.parameter,
    this.sample,
    this.analyst,
  });

  factory TestResultModel.fromJson(Map<String, dynamic> json) =>
      _$TestResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$TestResultModelToJson(this);
}

@JsonSerializable()
class CreateTestResultRequestModel {
  @JsonKey(name: 'parameter_allocation_id')
  final int parameterAllocationId;
  final String result;
  final String? unit;
  @JsonKey(name: 'test_method')
  final String? testMethod;
  @JsonKey(name: 'instrument_used')
  final String? instrumentUsed;
  @JsonKey(name: 'analysis_date')
  final String analysisDate;
  @JsonKey(name: 'lower_limit')
  final String? lowerLimit;
  @JsonKey(name: 'upper_limit')
  final String? upperLimit;
  @JsonKey(name: 'detection_limit')
  final String? detectionLimit;
  @JsonKey(name: 'uncertainty')
  final String? uncertainty;
  @JsonKey(name: 'dilution_factor')
  final String? dilutionFactor;
  final String? remarks;

  const CreateTestResultRequestModel({
    required this.parameterAllocationId,
    required this.result,
    this.unit,
    this.testMethod,
    this.instrumentUsed,
    required this.analysisDate,
    this.lowerLimit,
    this.upperLimit,
    this.detectionLimit,
    this.uncertainty,
    this.dilutionFactor,
    this.remarks,
  });

  factory CreateTestResultRequestModel.fromJson(Map<String, dynamic> json) =>
      _$CreateTestResultRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreateTestResultRequestModelToJson(this);
}

@JsonSerializable()
class UpdateTestResultRequestModel {
  final String result;
  final String? unit;
  @JsonKey(name: 'test_method')
  final String? testMethod;
  @JsonKey(name: 'instrument_used')
  final String? instrumentUsed;
  @JsonKey(name: 'analysis_date')
  final String analysisDate;
  @JsonKey(name: 'lower_limit')
  final String? lowerLimit;
  @JsonKey(name: 'upper_limit')
  final String? upperLimit;
  @JsonKey(name: 'detection_limit')
  final String? detectionLimit;
  @JsonKey(name: 'uncertainty')
  final String? uncertainty;
  @JsonKey(name: 'dilution_factor')
  final String? dilutionFactor;
  final String? remarks;

  const UpdateTestResultRequestModel({
    required this.result,
    this.unit,
    this.testMethod,
    this.instrumentUsed,
    required this.analysisDate,
    this.lowerLimit,
    this.upperLimit,
    this.detectionLimit,
    this.uncertainty,
    this.dilutionFactor,
    this.remarks,
  });

  factory UpdateTestResultRequestModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateTestResultRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateTestResultRequestModelToJson(this);
}
