import 'package:json_annotation/json_annotation.dart';
import '../job_allocation/job_allocation_model.dart';
import '../test_request/sample_model.dart';
import '../auth/user_model.dart';

part 'completed_test_model.g.dart';

@JsonSerializable()
class CompletedTestModel {
  final int id;
  @Json<PERSON>ey(name: 'job_detail_id')
  final int jobDetailId;
  @JsonKey(name: 'test_request_sample_id')
  final int testRequestSampleId;
  @Json<PERSON>ey(name: 'user_id')
  final int userId;
  final String result;
  @JsonKey(name: 'analysis_start_date')
  final String? analysisStartDate;
  @Json<PERSON>ey(name: 'analysis_completion_date')
  final String? analysisCompletionDate;
  @JsonKey(name: 'analysis_submission_date')
  final String? analysisSubmissionDate;
  @<PERSON>son<PERSON><PERSON>(name: 'is_retest')
  final int isRetest;
  @<PERSON>son<PERSON>ey(name: 'is_blind')
  final int isBlind;
  @Json<PERSON>ey(name: 'is_replicate')
  final int isReplicate;
  @Json<PERSON>ey(name: 'original_test_id')
  final int? originalTestId;
  @Json<PERSON>ey(name: 'raw_water')
  final String? rawWater;
  @JsonKey(name: 'filtered_water')
  final String? filteredWater;
  @JsonKey(name: 'treated_water')
  final String? treatedWater;
  @JsonKey(name: 'location_1')
  final String? location1;
  @JsonKey(name: 'location_2')
  final String? location2;
  @JsonKey(name: 'location_3')
  final String? location3;
  @JsonKey(name: 'location_4')
  final String? location4;
  @JsonKey(name: 'location_5')
  final String? location5;
  @JsonKey(name: 'location_6')
  final String? location6;
  @JsonKey(name: 'approval_status')
  final String? approvalStatus;
  @JsonKey(name: 'approved_by_id')
  final int? approvedById;
  @JsonKey(name: 'approval_date')
  final String? approvalDate;
  @JsonKey(name: 'approval_remarks')
  final String? approvalRemarks;
  @JsonKey(name: 'reassigned_to')
  final UserModel? reassignedTo;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;
  @JsonKey(name: 'job_detail')
  final JobAllocationModel? jobDetail;
  @JsonKey(name: 'test_request_sample')
  final SampleModel? testRequestSample;
  final UserModel? admin;
  @JsonKey(name: 'approved_by')
  final UserModel? approvedBy;

  const CompletedTestModel({
    required this.id,
    required this.jobDetailId,
    required this.testRequestSampleId,
    required this.userId,
    required this.result,
    this.analysisStartDate,
    this.analysisCompletionDate,
    this.analysisSubmissionDate,
    this.isRetest = 0,
    this.isBlind = 0,
    this.isReplicate = 0,
    this.originalTestId,
    this.rawWater,
    this.filteredWater,
    this.treatedWater,
    this.location1,
    this.location2,
    this.location3,
    this.location4,
    this.location5,
    this.location6,
    this.approvalStatus,
    this.approvedById,
    this.approvalDate,
    this.approvalRemarks,
    this.reassignedTo,
    this.createdAt,
    this.updatedAt,
    this.jobDetail,
    this.testRequestSample,
    this.admin,
    this.approvedBy,
  });

  factory CompletedTestModel.fromJson(Map<String, dynamic> json) =>
      _$CompletedTestModelFromJson(json);

  Map<String, dynamic> toJson() => _$CompletedTestModelToJson(this);
}
