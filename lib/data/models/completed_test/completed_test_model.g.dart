// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'completed_test_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CompletedTestModel _$CompletedTestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'CompletedTestModel',
  json,
  ($checkedConvert) {
    final val = CompletedTestModel(
      id: $checkedConvert('id', (v) => (v as num).toInt()),
      jobDetailId: $checkedConvert('job_detail_id', (v) => (v as num).toInt()),
      testRequestSampleId: $checkedConvert(
        'test_request_sample_id',
        (v) => (v as num).toInt(),
      ),
      userId: $checkedConvert('user_id', (v) => (v as num).toInt()),
      result: $checkedConvert('result', (v) => v as String),
      analysisStartDate: $checkedConvert(
        'analysis_start_date',
        (v) => v as String?,
      ),
      analysisCompletionDate: $checkedConvert(
        'analysis_completion_date',
        (v) => v as String?,
      ),
      analysisSubmissionDate: $checkedConvert(
        'analysis_submission_date',
        (v) => v as String?,
      ),
      isRetest: $checkedConvert('is_retest', (v) => (v as num?)?.toInt() ?? 0),
      isBlind: $checkedConvert('is_blind', (v) => (v as num?)?.toInt() ?? 0),
      isReplicate: $checkedConvert(
        'is_replicate',
        (v) => (v as num?)?.toInt() ?? 0,
      ),
      originalTestId: $checkedConvert(
        'original_test_id',
        (v) => (v as num?)?.toInt(),
      ),
      rawWater: $checkedConvert('raw_water', (v) => v as String?),
      filteredWater: $checkedConvert('filtered_water', (v) => v as String?),
      treatedWater: $checkedConvert('treated_water', (v) => v as String?),
      location1: $checkedConvert('location_1', (v) => v as String?),
      location2: $checkedConvert('location_2', (v) => v as String?),
      location3: $checkedConvert('location_3', (v) => v as String?),
      location4: $checkedConvert('location_4', (v) => v as String?),
      location5: $checkedConvert('location_5', (v) => v as String?),
      location6: $checkedConvert('location_6', (v) => v as String?),
      approvalStatus: $checkedConvert('approval_status', (v) => v as String?),
      approvedById: $checkedConvert(
        'approved_by_id',
        (v) => (v as num?)?.toInt(),
      ),
      approvalDate: $checkedConvert('approval_date', (v) => v as String?),
      approvalRemarks: $checkedConvert('approval_remarks', (v) => v as String?),
      reassignedTo: $checkedConvert(
        'reassigned_to',
        (v) => (v as num?)?.toInt(),
      ),
      createdAt: $checkedConvert('created_at', (v) => v as String?),
      updatedAt: $checkedConvert('updated_at', (v) => v as String?),
      jobDetail: $checkedConvert(
        'job_detail',
        (v) => v == null
            ? null
            : JobAllocationModel.fromJson(v as Map<String, dynamic>),
      ),
      testRequestSample: $checkedConvert(
        'test_request_sample',
        (v) =>
            v == null ? null : SampleModel.fromJson(v as Map<String, dynamic>),
      ),
      admin: $checkedConvert(
        'admin',
        (v) => v == null ? null : UserModel.fromJson(v as Map<String, dynamic>),
      ),
      approvedBy: $checkedConvert(
        'approved_by',
        (v) => v == null ? null : UserModel.fromJson(v as Map<String, dynamic>),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'jobDetailId': 'job_detail_id',
    'testRequestSampleId': 'test_request_sample_id',
    'userId': 'user_id',
    'analysisStartDate': 'analysis_start_date',
    'analysisCompletionDate': 'analysis_completion_date',
    'analysisSubmissionDate': 'analysis_submission_date',
    'isRetest': 'is_retest',
    'isBlind': 'is_blind',
    'isReplicate': 'is_replicate',
    'originalTestId': 'original_test_id',
    'rawWater': 'raw_water',
    'filteredWater': 'filtered_water',
    'treatedWater': 'treated_water',
    'location1': 'location_1',
    'location2': 'location_2',
    'location3': 'location_3',
    'location4': 'location_4',
    'location5': 'location_5',
    'location6': 'location_6',
    'approvalStatus': 'approval_status',
    'approvedById': 'approved_by_id',
    'approvalDate': 'approval_date',
    'approvalRemarks': 'approval_remarks',
    'reassignedTo': 'reassigned_to',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
    'jobDetail': 'job_detail',
    'testRequestSample': 'test_request_sample',
    'approvedBy': 'approved_by',
  },
);

Map<String, dynamic> _$CompletedTestModelToJson(CompletedTestModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'job_detail_id': instance.jobDetailId,
      'test_request_sample_id': instance.testRequestSampleId,
      'user_id': instance.userId,
      'result': instance.result,
      'analysis_start_date': instance.analysisStartDate,
      'analysis_completion_date': instance.analysisCompletionDate,
      'analysis_submission_date': instance.analysisSubmissionDate,
      'is_retest': instance.isRetest,
      'is_blind': instance.isBlind,
      'is_replicate': instance.isReplicate,
      'original_test_id': instance.originalTestId,
      'raw_water': instance.rawWater,
      'filtered_water': instance.filteredWater,
      'treated_water': instance.treatedWater,
      'location_1': instance.location1,
      'location_2': instance.location2,
      'location_3': instance.location3,
      'location_4': instance.location4,
      'location_5': instance.location5,
      'location_6': instance.location6,
      'approval_status': instance.approvalStatus,
      'approved_by_id': instance.approvedById,
      'approval_date': instance.approvalDate,
      'approval_remarks': instance.approvalRemarks,
      'reassigned_to': instance.reassignedTo,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'job_detail': instance.jobDetail?.toJson(),
      'test_request_sample': instance.testRequestSample?.toJson(),
      'admin': instance.admin?.toJson(),
      'approved_by': instance.approvedBy?.toJson(),
    };
