import 'package:json_annotation/json_annotation.dart';
import '../test_result/test_result_model.dart';
import 'pagination_response_model.dart';

part 'test_result_response_model.g.dart';

@JsonSerializable()
class TestResultsResponseModel {
  final bool success;
  final TestResultsDataModel data;

  const TestResultsResponseModel({
    required this.success,
    required this.data,
  });

  factory TestResultsResponseModel.fromJson(Map<String, dynamic> json) =>
      _$TestResultsResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$TestResultsResponseModelToJson(this);
}

@JsonSerializable()
class TestResultsDataModel {
  @JsonKey(name: 'test_results')
  final List<TestResultModel> testResults;
  final PaginationResponseModel pagination;

  const TestResultsDataModel({
    required this.testResults,
    required this.pagination,
  });

  factory TestResultsDataModel.fromJson(Map<String, dynamic> json) =>
      _$TestResultsDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$TestResultsDataModelToJson(this);
}

@JsonSerializable()
class TestResultResponseModel {
  final bool success;
  final String? message;
  final TestResultDataModel data;

  const TestResultResponseModel({
    required this.success,
    this.message,
    required this.data,
  });

  factory TestResultResponseModel.fromJson(Map<String, dynamic> json) =>
      _$TestResultResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$TestResultResponseModelToJson(this);
}

@JsonSerializable()
class TestResultDataModel {
  @JsonKey(name: 'test_result')
  final TestResultModel testResult;

  const TestResultDataModel({
    required this.testResult,
  });

  factory TestResultDataModel.fromJson(Map<String, dynamic> json) =>
      _$TestResultDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$TestResultDataModelToJson(this);
}
