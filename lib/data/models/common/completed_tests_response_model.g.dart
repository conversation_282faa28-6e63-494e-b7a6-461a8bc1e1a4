// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'completed_tests_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CompletedTestsResponseModel _$CompletedTestsResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('CompletedTestsResponseModel', json, ($checkedConvert) {
  final val = CompletedTestsResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) => CompletedTestsDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$CompletedTestsResponseModelToJson(
  CompletedTestsResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.data.toJson(),
};

CompletedTestsDataModel _$CompletedTestsDataModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'CompletedTestsDataModel',
  json,
  ($checkedConvert) {
    final val = CompletedTestsDataModel(
      completedTests: $checkedConvert(
        'completed_tests',
        (v) => (v as List<dynamic>)
            .map((e) => CompletedTestModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      ),
      pagination: $checkedConvert(
        'pagination',
        (v) => PaginationMeta.fromJson(v as Map<String, dynamic>),
      ),
      summary: $checkedConvert(
        'summary',
        (v) => v == null
            ? null
            : CompletedTestsSummaryModel.fromJson(v as Map<String, dynamic>),
      ),
    );
    return val;
  },
  fieldKeyMap: const {'completedTests': 'completed_tests'},
);

Map<String, dynamic> _$CompletedTestsDataModelToJson(
  CompletedTestsDataModel instance,
) => <String, dynamic>{
  'completed_tests': instance.completedTests.map((e) => e.toJson()).toList(),
  'pagination': instance.pagination.toJson(),
  'summary': instance.summary?.toJson(),
};

CompletedTestsSummaryModel _$CompletedTestsSummaryModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'CompletedTestsSummaryModel',
  json,
  ($checkedConvert) {
    final val = CompletedTestsSummaryModel(
      totalCompleted: $checkedConvert(
        'total_completed',
        (v) => (v as num).toInt(),
      ),
      pendingApproval: $checkedConvert(
        'pending_approval',
        (v) => (v as num).toInt(),
      ),
      approved: $checkedConvert('approved', (v) => (v as num).toInt()),
      rejected: $checkedConvert('rejected', (v) => (v as num).toInt()),
    );
    return val;
  },
  fieldKeyMap: const {
    'totalCompleted': 'total_completed',
    'pendingApproval': 'pending_approval',
  },
);

Map<String, dynamic> _$CompletedTestsSummaryModelToJson(
  CompletedTestsSummaryModel instance,
) => <String, dynamic>{
  'total_completed': instance.totalCompleted,
  'pending_approval': instance.pendingApproval,
  'approved': instance.approved,
  'rejected': instance.rejected,
};

CompletedTestResponseModel _$CompletedTestResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('CompletedTestResponseModel', json, ($checkedConvert) {
  final val = CompletedTestResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) => CompletedTestDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$CompletedTestResponseModelToJson(
  CompletedTestResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.data.toJson(),
};

CompletedTestDataModel _$CompletedTestDataModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'CompletedTestDataModel',
  json,
  ($checkedConvert) {
    final val = CompletedTestDataModel(
      completedTest: $checkedConvert(
        'completed_test',
        (v) => CompletedTestModel.fromJson(v as Map<String, dynamic>),
      ),
    );
    return val;
  },
  fieldKeyMap: const {'completedTest': 'completed_test'},
);

Map<String, dynamic> _$CompletedTestDataModelToJson(
  CompletedTestDataModel instance,
) => <String, dynamic>{'completed_test': instance.completedTest.toJson()};
