import 'package:json_annotation/json_annotation.dart';
import '../../../core/network/api_response.dart';
import '../job_allocation/job_allocation_model.dart';

part 'job_allocations_response_model.g.dart';

// Job allocations response data
@JsonSerializable()
class JobAllocationsResponseData {
  @JsonKey(name: 'job_allocations')
  final List<JobAllocationModel> jobAllocations;
  final PaginationMeta pagination;

  const JobAllocationsResponseData({
    required this.jobAllocations,
    required this.pagination,
  });

  factory JobAllocationsResponseData.fromJson(Map<String, dynamic> json) =>
      _$JobAllocationsResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$JobAllocationsResponseDataToJson(this);
}

// Complete job allocations API response
typedef JobAllocationsApiResponse = ApiResponse<JobAllocationsResponseData>;
