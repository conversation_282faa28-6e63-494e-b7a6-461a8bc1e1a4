import 'package:json_annotation/json_annotation.dart';
import '../analyst/analyst_test_model.dart';
import '../completed_test/completed_test_model.dart';

part 'analyst_tests_response_model.g.dart';

@JsonSerializable()
class AnalystTestsResponseModel {
  final bool success;
  final AnalystTestsDataModel data;

  const AnalystTestsResponseModel({
    required this.success,
    required this.data,
  });

  factory AnalystTestsResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AnalystTestsResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$AnalystTestsResponseModelToJson(this);
}

@JsonSerializable()
class AnalystTestsDataModel {
  @JsonKey(name: 'my_tests')
  final List<AnalystTestModel> myTests;

  const AnalystTestsDataModel({
    required this.myTests,
  });

  factory AnalystTestsDataModel.fromJson(Map<String, dynamic> json) =>
      _$AnalystTestsDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$AnalystTestsDataModelToJson(this);
}

@JsonSerializable()
class TestResultResponseModel {
  final bool success;
  final String? message;
  final TestResultDataModel data;

  const TestResultResponseModel({
    required this.success,
    this.message,
    required this.data,
  });

  factory TestResultResponseModel.fromJson(Map<String, dynamic> json) =>
      _$TestResultResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$TestResultResponseModelToJson(this);
}

@JsonSerializable()
class TestResultDataModel {
  @JsonKey(name: 'test_result')
  final CompletedTestModel testResult;

  const TestResultDataModel({
    required this.testResult,
  });

  factory TestResultDataModel.fromJson(Map<String, dynamic> json) =>
      _$TestResultDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$TestResultDataModelToJson(this);
}
