// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'test_result_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TestResultsResponseModel _$TestResultsResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('TestResultsResponseModel', json, ($checkedConvert) {
  final val = TestResultsResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) => TestResultsDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$TestResultsResponseModelToJson(
  TestResultsResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.data.toJson(),
};

TestResultsDataModel _$TestResultsDataModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('TestResultsDataModel', json, ($checkedConvert) {
  final val = TestResultsDataModel(
    testResults: $checkedConvert(
      'test_results',
      (v) => (v as List<dynamic>)
          .map((e) => TestResultModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
    pagination: $checkedConvert(
      'pagination',
      (v) => PaginationMeta.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
}, fieldKeyMap: const {'testResults': 'test_results'});

Map<String, dynamic> _$TestResultsDataModelToJson(
  TestResultsDataModel instance,
) => <String, dynamic>{
  'test_results': instance.testResults.map((e) => e.toJson()).toList(),
  'pagination': instance.pagination.toJson(),
};

TestResultResponseModel _$TestResultResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('TestResultResponseModel', json, ($checkedConvert) {
  final val = TestResultResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    message: $checkedConvert('message', (v) => v as String?),
    data: $checkedConvert(
      'data',
      (v) => TestResultDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$TestResultResponseModelToJson(
  TestResultResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
  'data': instance.data.toJson(),
};

TestResultDataModel _$TestResultDataModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('TestResultDataModel', json, ($checkedConvert) {
      final val = TestResultDataModel(
        testResult: $checkedConvert(
          'test_result',
          (v) => TestResultModel.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    }, fieldKeyMap: const {'testResult': 'test_result'});

Map<String, dynamic> _$TestResultDataModelToJson(
  TestResultDataModel instance,
) => <String, dynamic>{'test_result': instance.testResult.toJson()};
