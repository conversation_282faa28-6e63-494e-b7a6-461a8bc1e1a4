import 'package:json_annotation/json_annotation.dart';
import '../approval/approval_model.dart';
import 'pagination_response_model.dart';

part 'approval_response_model.g.dart';

@JsonSerializable()
class ApprovalsResponseModel {
  final bool success;
  final ApprovalsDataModel data;

  const ApprovalsResponseModel({
    required this.success,
    required this.data,
  });

  factory ApprovalsResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ApprovalsResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApprovalsResponseModelToJson(this);
}

@JsonSerializable()
class ApprovalsDataModel {
  final List<ApprovalModel> approvals;
  final PaginationResponseModel pagination;

  const ApprovalsDataModel({
    required this.approvals,
    required this.pagination,
  });

  factory ApprovalsDataModel.fromJson(Map<String, dynamic> json) =>
      _$ApprovalsDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApprovalsDataModelToJson(this);
}

@JsonSerializable()
class ApprovalResponseModel {
  final bool success;
  final String? message;
  final ApprovalDataModel data;

  const ApprovalResponseModel({
    required this.success,
    this.message,
    required this.data,
  });

  factory ApprovalResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ApprovalResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApprovalResponseModelToJson(this);
}

@JsonSerializable()
class ApprovalDataModel {
  final ApprovalModel approval;

  const ApprovalDataModel({
    required this.approval,
  });

  factory ApprovalDataModel.fromJson(Map<String, dynamic> json) =>
      _$ApprovalDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApprovalDataModelToJson(this);
}

@JsonSerializable()
class BulkApprovalResponseModel {
  final bool success;
  final String? message;
  final BulkApprovalDataModel data;

  const BulkApprovalResponseModel({
    required this.success,
    this.message,
    required this.data,
  });

  factory BulkApprovalResponseModel.fromJson(Map<String, dynamic> json) =>
      _$BulkApprovalResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$BulkApprovalResponseModelToJson(this);
}

@JsonSerializable()
class BulkApprovalDataModel {
  @JsonKey(name: 'approved_count')
  final int approvedCount;
  @JsonKey(name: 'failed_count')
  final int failedCount;
  @JsonKey(name: 'failed_tests')
  final List<int>? failedTests;

  const BulkApprovalDataModel({
    required this.approvedCount,
    required this.failedCount,
    this.failedTests,
  });

  factory BulkApprovalDataModel.fromJson(Map<String, dynamic> json) =>
      _$BulkApprovalDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$BulkApprovalDataModelToJson(this);
}
