// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'approval_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApprovalsResponseModel _$ApprovalsResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('ApprovalsResponseModel', json, ($checkedConvert) {
  final val = ApprovalsResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) => ApprovalsDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$ApprovalsResponseModelToJson(
  ApprovalsResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.data.toJson(),
};

ApprovalsDataModel _$ApprovalsDataModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('ApprovalsDataModel', json, ($checkedConvert) {
      final val = ApprovalsDataModel(
        approvals: $checkedConvert(
          'approvals',
          (v) => (v as List<dynamic>)
              .map((e) => ApprovalModel.fromJson(e as Map<String, dynamic>))
              .toList(),
        ),
        pagination: $checkedConvert(
          'pagination',
          (v) => PaginationMeta.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ApprovalsDataModelToJson(ApprovalsDataModel instance) =>
    <String, dynamic>{
      'approvals': instance.approvals.map((e) => e.toJson()).toList(),
      'pagination': instance.pagination.toJson(),
    };

ApprovalResponseModel _$ApprovalResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('ApprovalResponseModel', json, ($checkedConvert) {
  final val = ApprovalResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    message: $checkedConvert('message', (v) => v as String?),
    data: $checkedConvert(
      'data',
      (v) => ApprovalDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$ApprovalResponseModelToJson(
  ApprovalResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
  'data': instance.data.toJson(),
};

ApprovalDataModel _$ApprovalDataModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('ApprovalDataModel', json, ($checkedConvert) {
      final val = ApprovalDataModel(
        approval: $checkedConvert(
          'approval',
          (v) => ApprovalModel.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ApprovalDataModelToJson(ApprovalDataModel instance) =>
    <String, dynamic>{'approval': instance.approval.toJson()};

BulkApprovalResponseModel _$BulkApprovalResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('BulkApprovalResponseModel', json, ($checkedConvert) {
  final val = BulkApprovalResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    message: $checkedConvert('message', (v) => v as String?),
    data: $checkedConvert(
      'data',
      (v) => BulkApprovalDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$BulkApprovalResponseModelToJson(
  BulkApprovalResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
  'data': instance.data.toJson(),
};

BulkApprovalDataModel _$BulkApprovalDataModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'BulkApprovalDataModel',
  json,
  ($checkedConvert) {
    final val = BulkApprovalDataModel(
      approvedCount: $checkedConvert(
        'approved_count',
        (v) => (v as num).toInt(),
      ),
      failedCount: $checkedConvert('failed_count', (v) => (v as num).toInt()),
      failedTests: $checkedConvert(
        'failed_tests',
        (v) => (v as List<dynamic>?)?.map((e) => (e as num).toInt()).toList(),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'approvedCount': 'approved_count',
    'failedCount': 'failed_count',
    'failedTests': 'failed_tests',
  },
);

Map<String, dynamic> _$BulkApprovalDataModelToJson(
  BulkApprovalDataModel instance,
) => <String, dynamic>{
  'approved_count': instance.approvedCount,
  'failed_count': instance.failedCount,
  'failed_tests': instance.failedTests,
};
