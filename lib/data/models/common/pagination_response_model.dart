import 'package:json_annotation/json_annotation.dart';

part 'pagination_response_model.g.dart';

@JsonSerializable()
class PaginationResponseModel {
  @Json<PERSON>ey(name: 'current_page')
  final int currentPage;
  @Json<PERSON>ey(name: 'total_pages')
  final int totalPages;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'per_page')
  final int perPage;
  final int total;
  @<PERSON>son<PERSON>ey(name: 'has_more')
  final bool hasMore;

  const PaginationResponseModel({
    required this.currentPage,
    required this.totalPages,
    required this.perPage,
    required this.total,
    required this.hasMore,
  });

  factory PaginationResponseModel.fromJson(Map<String, dynamic> json) =>
      _$PaginationResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationResponseModelToJson(this);
}
