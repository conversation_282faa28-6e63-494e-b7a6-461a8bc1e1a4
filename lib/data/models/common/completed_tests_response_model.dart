import 'package:json_annotation/json_annotation.dart';
import '../completed_test/completed_test_model.dart';
import 'pagination_response_model.dart';

part 'completed_tests_response_model.g.dart';

@JsonSerializable()
class CompletedTestsResponseModel {
  final bool success;
  final CompletedTestsDataModel data;

  const CompletedTestsResponseModel({
    required this.success,
    required this.data,
  });

  factory CompletedTestsResponseModel.fromJson(Map<String, dynamic> json) =>
      _$CompletedTestsResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$CompletedTestsResponseModelToJson(this);
}

@JsonSerializable()
class CompletedTestsDataModel {
  @JsonKey(name: 'completed_tests')
  final List<CompletedTestModel> completedTests;
  final PaginationResponseModel pagination;
  final CompletedTestsSummaryModel? summary;

  const CompletedTestsDataModel({
    required this.completedTests,
    required this.pagination,
    this.summary,
  });

  factory CompletedTestsDataModel.fromJson(Map<String, dynamic> json) =>
      _$CompletedTestsDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$CompletedTestsDataModelToJson(this);
}

@JsonSerializable()
class CompletedTestsSummaryModel {
  @JsonKey(name: 'total_completed')
  final int totalCompleted;
  @JsonKey(name: 'pending_approval')
  final int pendingApproval;
  final int approved;
  final int rejected;

  const CompletedTestsSummaryModel({
    required this.totalCompleted,
    required this.pendingApproval,
    required this.approved,
    required this.rejected,
  });

  factory CompletedTestsSummaryModel.fromJson(Map<String, dynamic> json) =>
      _$CompletedTestsSummaryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CompletedTestsSummaryModelToJson(this);
}

@JsonSerializable()
class CompletedTestResponseModel {
  final bool success;
  final CompletedTestDataModel data;

  const CompletedTestResponseModel({
    required this.success,
    required this.data,
  });

  factory CompletedTestResponseModel.fromJson(Map<String, dynamic> json) =>
      _$CompletedTestResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$CompletedTestResponseModelToJson(this);
}

@JsonSerializable()
class CompletedTestDataModel {
  @JsonKey(name: 'completed_test')
  final CompletedTestModel completedTest;

  const CompletedTestDataModel({
    required this.completedTest,
  });

  factory CompletedTestDataModel.fromJson(Map<String, dynamic> json) =>
      _$CompletedTestDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$CompletedTestDataModelToJson(this);
}
