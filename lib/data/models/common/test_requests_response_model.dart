import 'package:json_annotation/json_annotation.dart';
import '../../../core/network/api_response.dart';
import '../test_request/test_request_model.dart';

part 'test_requests_response_model.g.dart';

// Test requests paginated response data
@JsonSerializable()
class TestRequestsResponseData {
  @JsonKey(name: 'test_requests')
  final List<TestRequestModel> testRequests;
  final PaginationMeta pagination;

  const TestRequestsResponseData({
    required this.testRequests,
    required this.pagination,
  });

  factory TestRequestsResponseData.fromJson(Map<String, dynamic> json) =>
      _$TestRequestsResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$TestRequestsResponseDataToJson(this);
}

// Complete test requests API response
typedef TestRequestsApiResponse = ApiResponse<TestRequestsResponseData>;
