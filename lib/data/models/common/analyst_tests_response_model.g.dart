// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analyst_tests_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalystTestsResponseModel _$AnalystTestsResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('AnalystTestsResponseModel', json, ($checkedConvert) {
  final val = AnalystTestsResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) => AnalystTestsDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$AnalystTestsResponseModelToJson(
  AnalystTestsResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.data.toJson(),
};

AnalystTestsDataModel _$AnalystTestsDataModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('AnalystTestsDataModel', json, ($checkedConvert) {
  final val = AnalystTestsDataModel(
    myTests: $checkedConvert(
      'my_tests',
      (v) => (v as List<dynamic>)
          .map((e) => AnalystTestModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
  );
  return val;
}, fieldKeyMap: const {'myTests': 'my_tests'});

Map<String, dynamic> _$AnalystTestsDataModelToJson(
  AnalystTestsDataModel instance,
) => <String, dynamic>{
  'my_tests': instance.myTests.map((e) => e.toJson()).toList(),
};

TestResultResponseModel _$TestResultResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('TestResultResponseModel', json, ($checkedConvert) {
  final val = TestResultResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    message: $checkedConvert('message', (v) => v as String?),
    data: $checkedConvert(
      'data',
      (v) => TestResultDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$TestResultResponseModelToJson(
  TestResultResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
  'data': instance.data.toJson(),
};

TestResultDataModel _$TestResultDataModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('TestResultDataModel', json, ($checkedConvert) {
      final val = TestResultDataModel(
        testResult: $checkedConvert(
          'test_result',
          (v) => CompletedTestModel.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    }, fieldKeyMap: const {'testResult': 'test_result'});

Map<String, dynamic> _$TestResultDataModelToJson(
  TestResultDataModel instance,
) => <String, dynamic>{'test_result': instance.testResult.toJson()};
