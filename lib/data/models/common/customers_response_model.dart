import 'package:json_annotation/json_annotation.dart';
import '../../../core/network/api_response.dart';
import '../master/customer_model.dart';

part 'customers_response_model.g.dart';

// Customers response data
@JsonSerializable()
class CustomersResponseData {
  final List<CustomerModel> customers;

  const CustomersResponseData({
    required this.customers,
  });

  factory CustomersResponseData.fromJson(Map<String, dynamic> json) =>
      _$CustomersResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$CustomersResponseDataToJson(this);
}

// Single customer response data
@JsonSerializable()
class CustomerResponseData {
  final CustomerModel customer;

  const CustomerResponseData({
    required this.customer,
  });

  factory CustomerResponseData.fromJson(Map<String, dynamic> json) =>
      _$CustomerResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerResponseDataToJson(this);
}

// Complete customers API responses
typedef CustomersApiResponse = ApiResponse<CustomersResponseData>;
typedef CustomerApiResponse = ApiResponse<CustomerResponseData>;
