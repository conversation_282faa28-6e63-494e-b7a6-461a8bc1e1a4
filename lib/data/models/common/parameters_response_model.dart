import 'package:json_annotation/json_annotation.dart';
import '../../../core/network/api_response.dart';
import '../master/parameter_model.dart';

part 'parameters_response_model.g.dart';

// Parameters response data
@JsonSerializable()
class ParametersResponseData {
  final List<ParameterModel> parameters;

  const ParametersResponseData({
    required this.parameters,
  });

  factory ParametersResponseData.fromJson(Map<String, dynamic> json) =>
      _$ParametersResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$ParametersResponseDataToJson(this);
}

// Complete parameters API response
typedef ParametersApiResponse = ApiResponse<ParametersResponseData>;
