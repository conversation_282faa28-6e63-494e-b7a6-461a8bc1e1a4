import 'package:json_annotation/json_annotation.dart';
import '../../../core/network/api_response.dart';
import '../test_request/sample_model.dart';

part 'samples_response_model.g.dart';

// Samples response data
@JsonSerializable()
class SamplesResponseData {
  final List<SampleModel> samples;

  const SamplesResponseData({
    required this.samples,
  });

  factory SamplesResponseData.fromJson(Map<String, dynamic> json) =>
      _$SamplesResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$SamplesResponseDataToJson(this);
}

// Single sample response data
@JsonSerializable()
class SampleResponseData {
  final SampleModel sample;

  const SampleResponseData({
    required this.sample,
  });

  factory SampleResponseData.fromJson(Map<String, dynamic> json) =>
      _$SampleResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$SampleResponseDataToJson(this);
}

// Complete samples API responses
typedef SamplesApiResponse = ApiResponse<SamplesResponseData>;
typedef SampleApiResponse = ApiResponse<SampleResponseData>;
