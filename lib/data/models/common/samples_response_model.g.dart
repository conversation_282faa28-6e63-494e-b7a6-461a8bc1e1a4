// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'samples_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SamplesResponseData _$SamplesResponseDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('SamplesResponseData', json, ($checkedConvert) {
      final val = SamplesResponseData(
        samples: $checkedConvert(
          'samples',
          (v) => (v as List<dynamic>)
              .map((e) => SampleModel.fromJson(e as Map<String, dynamic>))
              .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$SamplesResponseDataToJson(
  SamplesResponseData instance,
) => <String, dynamic>{
  'samples': instance.samples.map((e) => e.toJson()).toList(),
};

SampleResponseData _$SampleResponseDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('SampleResponseData', json, ($checkedConvert) {
      final val = SampleResponseData(
        sample: $checkedConvert(
          'sample',
          (v) => SampleModel.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$SampleResponseDataToJson(SampleResponseData instance) =>
    <String, dynamic>{'sample': instance.sample.toJson()};
