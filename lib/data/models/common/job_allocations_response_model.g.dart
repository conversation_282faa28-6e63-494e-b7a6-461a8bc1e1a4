// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'job_allocations_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JobAllocationsResponseData _$JobAllocationsResponseDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'JobAllocationsResponseData',
  json,
  ($checkedConvert) {
    final val = JobAllocationsResponseData(
      jobAllocations: $checkedConvert(
        'job_allocations',
        (v) => (v as List<dynamic>)
            .map((e) => JobAllocationModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      ),
      pagination: $checkedConvert(
        'pagination',
        (v) => PaginationMeta.fromJson(v as Map<String, dynamic>),
      ),
    );
    return val;
  },
  fieldKeyMap: const {'jobAllocations': 'job_allocations'},
);

Map<String, dynamic> _$JobAllocationsResponseDataToJson(
  JobAllocationsResponseData instance,
) => <String, dynamic>{
  'job_allocations': instance.jobAllocations.map((e) => e.toJson()).toList(),
  'pagination': instance.pagination.toJson(),
};
