// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'test_requests_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TestRequestsResponseData _$TestRequestsResponseDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  'TestRequestsResponseData',
  json,
  ($checkedConvert) {
    final val = TestRequestsResponseData(
      testRequests: $checkedConvert(
        'test_requests',
        (v) => (v as List<dynamic>)
            .map((e) => TestRequestModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      ),
      pagination: $checkedConvert(
        'pagination',
        (v) => PaginationMeta.fromJson(v as Map<String, dynamic>),
      ),
    );
    return val;
  },
  fieldKeyMap: const {'testRequests': 'test_requests'},
);

Map<String, dynamic> _$TestRequestsResponseDataToJson(
  TestRequestsResponseData instance,
) => <String, dynamic>{
  'test_requests': instance.testRequests.map((e) => e.toJson()).toList(),
  'pagination': instance.pagination.toJson(),
};
