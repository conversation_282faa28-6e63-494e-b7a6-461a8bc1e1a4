// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'qc_tests_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QCTestsResponseModel _$QCTestsResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('QCTestsResponseModel', json, ($checkedConvert) {
  final val = QCTestsResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    message: $checkedConvert('message', (v) => v as String?),
    data: $checkedConvert(
      'data',
      (v) => QCTestsDataModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$QCTestsResponseModelToJson(
  QCTestsResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
  'data': instance.data.toJson(),
};

QCTestsDataModel _$QCTestsDataModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('QCTestsDataModel', json, ($checkedConvert) {
      final val = QCTestsDataModel(
        currentPage: $checkedConvert('current_page', (v) => (v as num).toInt()),
        data: $checkedConvert(
          'data',
          (v) => (v as List<dynamic>)
              .map(
                (e) => CompletedTestModel.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
        ),
      );
      return val;
    }, fieldKeyMap: const {'currentPage': 'current_page'});

Map<String, dynamic> _$QCTestsDataModelToJson(QCTestsDataModel instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'data': instance.data.map((e) => e.toJson()).toList(),
    };

QCTestResponseModel _$QCTestResponseModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('QCTestResponseModel', json, ($checkedConvert) {
      final val = QCTestResponseModel(
        success: $checkedConvert('success', (v) => v as bool),
        message: $checkedConvert('message', (v) => v as String?),
        data: $checkedConvert(
          'data',
          (v) => QCTestDataModel.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$QCTestResponseModelToJson(
  QCTestResponseModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
  'data': instance.data.toJson(),
};

QCTestDataModel _$QCTestDataModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('QCTestDataModel', json, ($checkedConvert) {
      final val = QCTestDataModel(
        test: $checkedConvert(
          'test',
          (v) => CompletedTestModel.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$QCTestDataModelToJson(QCTestDataModel instance) =>
    <String, dynamic>{'test': instance.test.toJson()};
