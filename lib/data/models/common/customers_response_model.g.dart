// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customers_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomersResponseData _$CustomersResponseDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('CustomersResponseData', json, ($checkedConvert) {
  final val = CustomersResponseData(
    customers: $checkedConvert(
      'customers',
      (v) => (v as List<dynamic>)
          .map((e) => CustomerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
  );
  return val;
});

Map<String, dynamic> _$CustomersResponseDataToJson(
  CustomersResponseData instance,
) => <String, dynamic>{
  'customers': instance.customers.map((e) => e.toJson()).toList(),
};

CustomerResponseData _$CustomerResponseDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('CustomerResponseData', json, ($checkedConvert) {
  final val = CustomerResponseData(
    customer: $checkedConvert(
      'customer',
      (v) => CustomerModel.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$CustomerResponseDataToJson(
  CustomerResponseData instance,
) => <String, dynamic>{'customer': instance.customer.toJson()};
