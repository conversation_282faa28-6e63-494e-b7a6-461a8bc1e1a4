import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/completed_test/completed_test_entity.dart';
import '../../domain/entities/quality_control/qc_test_entity.dart';
import '../../domain/repositories/quality_control_repository.dart';
import '../datasources/quality_control_remote_data_source.dart';
import '../models/quality_control/qc_test_model.dart';
import '../models/completed_test/completed_test_model.dart';

class QualityControlRepositoryImpl implements QualityControlRepository {
  final QualityControlRemoteDataSource _remoteDataSource;

  QualityControlRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, QCTestsResult>> getQCTests({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final result = await _remoteDataSource.getQCTests(
        page: page,
        perPage: perPage,
      );

      final entities = result.data.map(_mapCompletedTestModelToEntity).toList();
      final qcTestsResult = QCTestsResult(
        currentPage: result.currentPage,
        tests: entities,
      );

      return Right(qcTestsResult);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, CompletedTestEntity>> createQCTest(
    QCTestRequestEntity request,
  ) async {
    try {
      final model = _mapQCTestRequestEntityToModel(request);
      final result = await _remoteDataSource.createQCTest(model);
      final entity = _mapCompletedTestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, CompletedTestEntity>> approveTest(
    int testId,
    ApproveTestRequestEntity request,
  ) async {
    try {
      final model = _mapApproveTestRequestEntityToModel(request);
      final result = await _remoteDataSource.approveTest(testId, model);
      final entity = _mapCompletedTestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper methods for mapping between models and entities
  CompletedTestEntity _mapCompletedTestModelToEntity(CompletedTestModel model) {
    return CompletedTestEntity(
      id: model.id,
      jobDetailId: model.jobDetailId,
      testRequestSampleId: model.testRequestSampleId,
      userId: model.userId,
      result: model.result,
      analysisStartDate: model.analysisStartDate != null 
          ? DateTime.tryParse(model.analysisStartDate!) : null,
      analysisCompletionDate: model.analysisCompletionDate != null 
          ? DateTime.tryParse(model.analysisCompletionDate!) : null,
      analysisSubmissionDate: model.analysisSubmissionDate != null 
          ? DateTime.tryParse(model.analysisSubmissionDate!) : null,
      isRetest: model.isRetest == 1,
      isBlind: model.isBlind == 1,
      isReplicate: model.isReplicate == 1,
      originalTestId: model.originalTestId,
      rawWater: model.rawWater,
      filteredWater: model.filteredWater,
      treatedWater: model.treatedWater,
      location1: model.location1,
      location2: model.location2,
      location3: model.location3,
      location4: model.location4,
      location5: model.location5,
      location6: model.location6,
      approvalStatus: _mapApprovalStatus(model.approvalStatus),
      approvedById: model.approvedById,
      approvalDate: model.approvalDate != null 
          ? DateTime.tryParse(model.approvalDate!) : null,
      approvalRemarks: model.approvalRemarks,
      reassignedTo: model.reassignedTo,
      createdAt: model.createdAt != null 
          ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null 
          ? DateTime.tryParse(model.updatedAt!) : null,
    );
  }

  QCTestRequestModel _mapQCTestRequestEntityToModel(QCTestRequestEntity entity) {
    return QCTestRequestModel(
      jobDetailId: entity.jobDetailId,
      testRequestSampleId: entity.testRequestSampleId,
      result: entity.result,
      analysisStartDate: entity.analysisStartDate.toIso8601String().split('T')[0],
      analysisCompletionDate: entity.analysisCompletionDate.toIso8601String().split('T')[0],
      analysisSubmissionDate: entity.analysisSubmissionDate.toIso8601String().split('T')[0],
      isRetest: entity.isRetest,
      isBlind: entity.isBlind,
      isReplicate: entity.isReplicate,
      originalTestId: entity.originalTestId,
    );
  }

  ApproveTestRequestModel _mapApproveTestRequestEntityToModel(ApproveTestRequestEntity entity) {
    return ApproveTestRequestModel(
      approvalStatus: entity.isApproved ? 'approved' : 'rejected',
      approvalRemarks: entity.approvalRemarks,
      reassignedTo: entity.reassignedTo,
    );
  }

  ApprovalStatus _mapApprovalStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return ApprovalStatus.approved;
      case 'rejected':
        return ApprovalStatus.rejected;
      default:
        return ApprovalStatus.pending;
    }
  }
}
