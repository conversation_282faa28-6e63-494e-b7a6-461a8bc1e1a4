import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/completed_test/completed_test_entity.dart';
import '../../domain/entities/master/parameter_entity.dart';
import '../../domain/repositories/quality_control_repository.dart';
import '../datasources/quality_control_remote_data_source.dart';
import '../mappers/user_mapper.dart';
import '../models/completed_test/completed_test_model.dart';
import '../models/master/parameter_model.dart';

class QualityControlRepositoryImpl implements QualityControlRepository {
  final QualityControlRemoteDataSource _remoteDataSource;

  QualityControlRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, QCTestsResult>> getQCTests({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final result = await _remoteDataSource.getQCTests(
        page: page,
        perPage: perPage,
      );

      final entities = result.data.map(_mapCompletedTestModelToEntity).toList();
      final qcTestsResult = QCTestsResult(
        currentPage: result.currentPage,
        tests: entities,
      );

      return Right(qcTestsResult);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, CompletedTestEntity>> addRetest(
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.addRetest(request);
      final entity = _mapCompletedTestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, CompletedTestEntity>> addBlind(
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.addBlind(request);
      final entity = _mapCompletedTestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, CompletedTestEntity>> addReplicate(
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.addReplicate(request);
      final entity = _mapCompletedTestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, ParameterEntity>> viewParameter(int id) async {
    try {
      final result = await _remoteDataSource.viewParameter(id);
      final entity = _mapParameterModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper methods for mapping between models and entities
  CompletedTestEntity _mapCompletedTestModelToEntity(CompletedTestModel model) {
    return CompletedTestEntity(
      id: model.id,
      jobDetailId: model.jobDetailId,
      testRequestSampleId: model.testRequestSampleId,
      userId: model.userId,
      result: model.result,
      analysisStartDate: model.analysisStartDate != null 
          ? DateTime.tryParse(model.analysisStartDate!) : null,
      analysisCompletionDate: model.analysisCompletionDate != null 
          ? DateTime.tryParse(model.analysisCompletionDate!) : null,
      analysisSubmissionDate: model.analysisSubmissionDate != null 
          ? DateTime.tryParse(model.analysisSubmissionDate!) : null,
      isRetest: model.isRetest == 1,
      isBlind: model.isBlind == 1,
      isReplicate: model.isReplicate == 1,
      originalTestId: model.originalTestId,
      rawWater: model.rawWater,
      filteredWater: model.filteredWater,
      treatedWater: model.treatedWater,
      location1: model.location1,
      location2: model.location2,
      location3: model.location3,
      location4: model.location4,
      location5: model.location5,
      location6: model.location6,
      approvalStatus: _mapApprovalStatus(model.approvalStatus),
      approvedById: model.approvedById,
      approvalDate: model.approvalDate != null 
          ? DateTime.tryParse(model.approvalDate!) : null,
      approvalRemarks: model.approvalRemarks,
      reassignedTo: UserMapper().map(model.reassignedTo),
      createdAt: model.createdAt != null 
          ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null 
          ? DateTime.tryParse(model.updatedAt!) : null,
    );
  }

  ParameterEntity _mapParameterModelToEntity(ParameterModel model) {
    return ParameterEntity(
      id: model.id,
      name: model.name ?? '',
      type: model.type,
      status: model.status,
      requirement: model.requirement,
      permissibleLimit: model.permissibleLimit,
      protocolUsed: model.protocolUsed,
      units: model.units,
      createdAt: model.createdAt != null ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null ? DateTime.tryParse(model.updatedAt!) : null,
    );
  }

  ApprovalStatus _mapApprovalStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return ApprovalStatus.approved;
      case 'rejected':
        return ApprovalStatus.rejected;
      default:
        return ApprovalStatus.pending;
    }
  }
}
