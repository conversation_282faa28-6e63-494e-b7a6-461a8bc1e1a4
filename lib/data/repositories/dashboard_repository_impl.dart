import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/dashboard/dashboard_stats_entity.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../datasources/dashboard_remote_data_source.dart';
import '../models/dashboard/dashboard_stats_model.dart';

class DashboardRepositoryImpl implements DashboardRepository {
  final DashboardRemoteDataSource _remoteDataSource;

  DashboardRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, DashboardStatsEntity>> getDashboardStats() async {
    try {
      final result = await _remoteDataSource.getDashboardStats();
      final entity = _mapDashboardStatsModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper method for mapping between model and entity
  DashboardStatsEntity _mapDashboardStatsModelToEntity(DashboardStatsModel model) {
    return DashboardStatsEntity(
      totalStats: TotalStatsEntity(
        totalTestRequests: model.totalStats.totalTestRequests,
        totalJobAllocations: model.totalStats.totalJobAllocations,
        totalUsers: model.totalStats.totalUsers,
      ),
      monthlyStats: MonthlyStatsEntity(
        monthlyTestRequests: model.monthlyStats.monthlyTestRequests,
        monthlyJobAllocations: model.monthlyStats.monthlyJobAllocations,
      ),
      taskStats: TaskStatsEntity(
        overdueTasks: model.taskStats.overdueTasks,
        todayTasks: model.taskStats.todayTasks,
        upcomingTasks: model.taskStats.upcomingTasks,
      ),
      userStats: UserStatsEntity(
        myJobs: model.userStats.myJobs,
        myOverdueTasks: model.userStats.myOverdueTasks,
      ),
    );
  }
}
