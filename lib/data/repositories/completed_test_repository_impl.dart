import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/completed_test/completed_test_entity.dart';
import '../../domain/entities/common/pagination_entity.dart';
import '../../domain/repositories/completed_test_repository.dart';
import '../datasources/completed_test_remote_data_source.dart';
import '../models/completed_test/completed_test_model.dart';

class CompletedTestRepositoryImpl implements CompletedTestRepository {
  final CompletedTestRemoteDataSource _remoteDataSource;

  CompletedTestRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, CompletedTestsResult>> getCompletedTests({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final result = await _remoteDataSource.getCompletedTests(
        page: page,
        perPage: perPage,
      );

      final entities = result.completedTests.map(_mapCompletedTestModelToEntity).toList();
      final paginationEntity = PaginationEntity(
        currentPage: result.pagination.currentPage,
        totalPages: result.pagination.totalPages,
        perPage: result.pagination.perPage,
        total: result.pagination.total,
        hasMore: result.pagination.hasMore,
      );

      final summary = result.summary != null
          ? CompletedTestsSummary(
              totalCompleted: result.summary!.totalCompleted,
              pendingApproval: result.summary!.pendingApproval,
              approved: result.summary!.approved,
              rejected: result.summary!.rejected,
            )
          : null;

      final completedTestsResult = CompletedTestsResult(
        completedTests: entities,
        pagination: paginationEntity,
        summary: summary,
      );

      return Right(completedTestsResult);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, CompletedTestEntity>> getCompletedTestById(int id) async {
    try {
      final result = await _remoteDataSource.getCompletedTestById(id);
      final entity = _mapCompletedTestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, String>> exportToExcel() async {
    try {
      final result = await _remoteDataSource.exportToExcel();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper method for mapping between model and entity
  CompletedTestEntity _mapCompletedTestModelToEntity(CompletedTestModel model) {
    return CompletedTestEntity(
      id: model.id,
      jobDetailId: model.jobDetailId,
      testRequestSampleId: model.testRequestSampleId,
      userId: model.userId,
      result: model.result,
      analysisStartDate: model.analysisStartDate != null 
          ? DateTime.tryParse(model.analysisStartDate!) : null,
      analysisCompletionDate: model.analysisCompletionDate != null 
          ? DateTime.tryParse(model.analysisCompletionDate!) : null,
      analysisSubmissionDate: model.analysisSubmissionDate != null 
          ? DateTime.tryParse(model.analysisSubmissionDate!) : null,
      isRetest: model.isRetest == 1,
      isBlind: model.isBlind == 1,
      isReplicate: model.isReplicate == 1,
      originalTestId: model.originalTestId,
      rawWater: model.rawWater,
      filteredWater: model.filteredWater,
      treatedWater: model.treatedWater,
      location1: model.location1,
      location2: model.location2,
      location3: model.location3,
      location4: model.location4,
      location5: model.location5,
      location6: model.location6,
      approvalStatus: _mapApprovalStatus(model.approvalStatus),
      approvedById: model.approvedById,
      approvalDate: model.approvalDate != null 
          ? DateTime.tryParse(model.approvalDate!) : null,
      approvalRemarks: model.approvalRemarks,
      reassignedTo: model.reassignedTo,
      createdAt: model.createdAt != null 
          ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null 
          ? DateTime.tryParse(model.updatedAt!) : null,
      // Note: Related entities (jobDetail, testRequestSample, admin, approvedBy) 
      // would need their own mappers if included in the response
    );
  }

  ApprovalStatus _mapApprovalStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return ApprovalStatus.approved;
      case 'rejected':
        return ApprovalStatus.rejected;
      default:
        return ApprovalStatus.pending;
    }
  }
}
