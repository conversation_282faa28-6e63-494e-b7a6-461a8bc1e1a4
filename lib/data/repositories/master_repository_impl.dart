import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/master/parameter_entity.dart';
import '../../domain/entities/master/customer_entity.dart';
import '../../domain/repositories/master_repository.dart';
import '../datasources/master_remote_data_source.dart';
import '../models/master/parameter_model.dart';
import '../models/master/customer_model.dart';

class MasterRepositoryImpl implements MasterRepository {
  final MasterRemoteDataSource _remoteDataSource;

  MasterRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, List<ParameterEntity>>> getParameters() async {
    try {
      final result = await _remoteDataSource.getParameters();
      final entities = result.map(_mapParameterModelToEntity).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, List<CustomerEntity>>> getCustomers({
    String? search,
  }) async {
    try {
      final result = await _remoteDataSource.getCustomers(search: search);
      final entities = result.map(_mapCustomerModelToEntity).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, CustomerEntity>> addCustomer(
    CustomerEntity customer,
  ) async {
    try {
      final model = _mapCustomerEntityToModel(customer);
      final result = await _remoteDataSource.addCustomer(model);
      final entity = _mapCustomerModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper methods for mapping between models and entities
  ParameterEntity _mapParameterModelToEntity(ParameterModel model) {
    return ParameterEntity(
      id: model.id,
      name: model.name ?? "NA",
      type: model.type,
      status: model.status,
      requirement: model.requirement,
      permissibleLimit: model.permissibleLimit,
      protocolUsed: model.protocolUsed,
      units: model.units,
      createdAt: model.createdAt != null ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null ? DateTime.tryParse(model.updatedAt!) : null,
    );
  }

  CustomerEntity _mapCustomerModelToEntity(CustomerModel model) {
    return CustomerEntity(
      id: model.id,
      name: model.name ?? "NA",
      address: model.address ?? "NA",
      contactPerson: model.contactPerson ?? "NA",
      mobileNo: model.mobileNo ?? "NA",
      email: model.email ?? "NA",
      createdAt: model.createdAt != null ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null ? DateTime.tryParse(model.updatedAt!) : null,
    );
  }

  CustomerModel _mapCustomerEntityToModel(CustomerEntity entity) {
    return CustomerModel(
      id: entity.id,
      name: entity.name,
      address: entity.address,
      contactPerson: entity.contactPerson,
      mobileNo: entity.mobileNo,
      email: entity.email,
      createdAt: entity.createdAt?.toIso8601String(),
      updatedAt: entity.updatedAt?.toIso8601String(),
    );
  }
}
