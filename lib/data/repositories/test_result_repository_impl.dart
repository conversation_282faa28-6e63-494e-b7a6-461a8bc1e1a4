import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/test_result/test_result_entity.dart';
import '../../domain/entities/common/pagination_entity.dart';
import '../../domain/repositories/test_result_repository.dart';
import '../datasources/test_result_remote_data_source.dart';
import '../models/test_result/test_result_model.dart';

class TestResultRepositoryImpl implements TestResultRepository {
  final TestResultRemoteDataSource _remoteDataSource;

  TestResultRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, PaginatedResult<TestResultEntity>>> getTestResults({
    int page = 1,
    int perPage = 15,
    int? parameterId,
    int? sampleId,
    int? analystId,
    TestResultStatus? status,
  }) async {
    try {
      final result = await _remoteDataSource.getTestResults(
        page: page,
        perPage: perPage,
        parameterId: parameterId,
        sampleId: sampleId,
        analystId: analystId,
        status: status != null ? _mapTestResultStatusToString(status) : null,
      );

      final entities = result.testResults.map(_mapTestResultModelToEntity).toList();
      final paginationEntity = PaginationEntity(
        currentPage: result.pagination.currentPage,
        totalPages: result.pagination.totalPages,
        perPage: result.pagination.perPage,
        total: result.pagination.total,
        hasMore: result.pagination.hasMore,
      );

      final paginatedResult = PaginatedResult<TestResultEntity>(
        items: entities,
        pagination: paginationEntity,
      );

      return Right(paginatedResult);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestResultEntity>> getTestResultById(int id) async {
    try {
      final result = await _remoteDataSource.getTestResultById(id);
      final entity = _mapTestResultModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestResultEntity>> addTestResult(
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.addTestResult(request);
      final entity = _mapTestResultModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestResultEntity>> updateTestResult(
    int id,
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.updateTestResult(id, request);
      final entity = _mapTestResultModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }



  @override
  Future<Either<Failure, TestResultEntity>> approveTestResult(
    int id,
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.approveTestResult(id, request);
      final entity = _mapTestResultModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestResultEntity>> rejectTestResult(
    int id,
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.rejectTestResult(id, request);
      final entity = _mapTestResultModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, List<TestResultEntity>>> getActiveTests() async {
    try {
      final result = await _remoteDataSource.getActiveTests();
      final entities = result.map(_mapTestResultModelToEntity).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, List<TestResultEntity>>> getMyTests() async {
    try {
      final result = await _remoteDataSource.getMyTests();
      final entities = result.map(_mapTestResultModelToEntity).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestResultEntity>> acceptTest(int id) async {
    try {
      final result = await _remoteDataSource.acceptTest(id);
      final entity = _mapTestResultModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestResultEntity>> rejectTest(
    int id,
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.rejectTest(id, request);
      final entity = _mapTestResultModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestResultEntity>> addResult(
    int id,
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.addResult(id, request);
      final entity = _mapTestResultModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestResultEntity>> reassignTest(
    int id,
    Map<String, dynamic> request,
  ) async {
    try {
      final result = await _remoteDataSource.reassignTest(id, request);
      final entity = _mapTestResultModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper methods for mapping between models and entities
  TestResultEntity _mapTestResultModelToEntity(TestResultModel model) {
    return TestResultEntity(
      id: model.id,
      parameterAllocationId: model.parameterAllocationId,
      parameterId: model.parameterId,
      sampleId: model.sampleId,
      analystId: model.analystId,
      result: model.result,
      unit: model.unit,
      testMethod: model.testMethod,
      instrumentUsed: model.instrumentUsed,
      analysisDate: model.analysisDate != null 
          ? DateTime.tryParse(model.analysisDate!) : null,
      resultStatus: _mapTestResultStatus(model.resultStatus),
      isWithinLimit: model.isWithinLimit,
      lowerLimit: model.lowerLimit,
      upperLimit: model.upperLimit,
      detectionLimit: model.detectionLimit,
      uncertainty: model.uncertainty,
      dilutionFactor: model.dilutionFactor,
      remarks: model.remarks,
      createdAt: model.createdAt != null 
          ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null 
          ? DateTime.tryParse(model.updatedAt!) : null,
      // Note: Related entities (parameter, sample, analyst) would need 
      // their own mappers if included in the response
    );
  }



  TestResultStatus _mapTestResultStatus(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return TestResultStatus.completed;
      case 'approved':
        return TestResultStatus.approved;
      case 'rejected':
        return TestResultStatus.rejected;
      case 'under_review':
        return TestResultStatus.underReview;
      default:
        return TestResultStatus.pending;
    }
  }

  String _mapTestResultStatusToString(TestResultStatus status) {
    switch (status) {
      case TestResultStatus.completed:
        return 'completed';
      case TestResultStatus.approved:
        return 'approved';
      case TestResultStatus.rejected:
        return 'rejected';
      case TestResultStatus.underReview:
        return 'under_review';
      case TestResultStatus.pending:
        return 'pending';
    }
  }
}
