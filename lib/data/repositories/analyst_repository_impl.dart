import 'package:dartz/dartz.dart';
import 'package:lims_app_flutter/data/mappers/user_mapper.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/analyst/analyst_test_entity.dart';
import '../../domain/entities/completed_test/completed_test_entity.dart';
import '../../domain/entities/parameter_allocation/parameter_allocation_entity.dart';
import '../../domain/repositories/analyst_repository.dart';
import '../datasources/analyst_remote_data_source.dart';
import '../models/analyst/analyst_test_model.dart';
import '../models/completed_test/completed_test_model.dart';

class AnalystRepositoryImpl implements AnalystRepository {
  final AnalystRemoteDataSource _remoteDataSource;

  AnalystRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, List<AnalystTestEntity>>> getMyTests() async {
    try {
      final result = await _remoteDataSource.getMyTests();
      final entities = result.map(_mapAnalystTestModelToEntity).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, CompletedTestEntity>> submitTestResult(
    int testId,
    SubmitTestResultRequestEntity request,
  ) async {
    try {
      final model = _mapSubmitTestResultRequestEntityToModel(request);
      final result = await _remoteDataSource.submitTestResult(testId, model);
      final entity = _mapCompletedTestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> rejectTest(
    int testId,
    RejectTestRequestEntity request,
  ) async {
    try {
      final model = RejectTestRequestModel(rejectionReason: request.rejectionReason);
      await _remoteDataSource.rejectTest(testId, model);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper methods for mapping between models and entities
  AnalystTestEntity _mapAnalystTestModelToEntity(AnalystTestModel model) {
    return AnalystTestEntity(
      id: model.id,
      serialNo: model.serialNo,
      creationDate: DateTime.parse(model.creationDate),
      codeNumber: model.codeNumber,
      nature: model.nature,
      quantity: model.quantity,
      collectionDate: DateTime.parse(model.collectionDate),
      submissionDate: DateTime.parse(model.submissionDate),
      dueDate: DateTime.parse(model.dueDate),
      userId: model.userId,
      testRequestId: model.testRequestId,
      reportType: model.reportType,
      designation: model.designation,
      remarks: model.remarks,
      nablStatus: model.nablStatus,
      createdAt: model.createdAt != null 
          ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null 
          ? DateTime.tryParse(model.updatedAt!) : null,
      parameterAllocations: model.parameterAllocations
          ?.map(_mapParameterAllocationModelToEntity)
          .toList(),
    );
  }

  ParameterAllocationEntity _mapParameterAllocationModelToEntity(
    AnalystParameterAllocationModel model,
  ) {
    return ParameterAllocationEntity(
      id: model.id,
      jobDetailId: model.jobDetailId,
      sampleId: model.sampleId,
      analystId: model.analystId,
      isRetest: model.isRetest == 1,
      isBlind: model.isBlind == 1,
      isReplicate: model.isReplicate == 1,
      status: _mapAllocationStatus(model.status),
      rejectionReason: model.rejectionReason,
      rejectedAt: model.rejectedAt != null 
          ? DateTime.tryParse(model.rejectedAt!) : null,
    );
  }

  CompletedTestEntity _mapCompletedTestModelToEntity(CompletedTestModel model) {
    return CompletedTestEntity(
      id: model.id,
      jobDetailId: model.jobDetailId,
      testRequestSampleId: model.testRequestSampleId,
      userId: model.userId,
      result: model.result,
      analysisStartDate: model.analysisStartDate != null 
          ? DateTime.tryParse(model.analysisStartDate!) : null,
      analysisCompletionDate: model.analysisCompletionDate != null 
          ? DateTime.tryParse(model.analysisCompletionDate!) : null,
      analysisSubmissionDate: model.analysisSubmissionDate != null 
          ? DateTime.tryParse(model.analysisSubmissionDate!) : null,
      isRetest: model.isRetest == 1,
      isBlind: model.isBlind == 1,
      isReplicate: model.isReplicate == 1,
      originalTestId: model.originalTestId,
      rawWater: model.rawWater,
      filteredWater: model.filteredWater,
      treatedWater: model.treatedWater,
      location1: model.location1,
      location2: model.location2,
      location3: model.location3,
      location4: model.location4,
      location5: model.location5,
      location6: model.location6,
      approvalStatus: _mapApprovalStatus(model.approvalStatus),
      approvedById: model.approvedById,
      approvalDate: model.approvalDate != null 
          ? DateTime.tryParse(model.approvalDate!) : null,
      approvalRemarks: model.approvalRemarks,
      reassignedTo: UserMapper().map(model.reassignedTo),
      createdAt: model.createdAt != null 
          ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null 
          ? DateTime.tryParse(model.updatedAt!) : null,
    );
  }

  SubmitTestResultRequestModel _mapSubmitTestResultRequestEntityToModel(
    SubmitTestResultRequestEntity entity,
  ) {
    return SubmitTestResultRequestModel(
      result: entity.result,
      analysisStartDate: entity.analysisStartDate.toIso8601String().split('T')[0],
      analysisCompletionDate: entity.analysisCompletionDate.toIso8601String().split('T')[0],
      analysisSubmissionDate: entity.analysisSubmissionDate.toIso8601String().split('T')[0],
      rawWater: entity.rawWater,
      filteredWater: entity.filteredWater,
      treatedWater: entity.treatedWater,
      location1: entity.location1,
      location2: entity.location2,
      location3: entity.location3,
      location4: entity.location4,
      location5: entity.location5,
      location6: entity.location6,
    );
  }

  AllocationStatus _mapAllocationStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'in_progress':
        return AllocationStatus.inProgress;
      case 'completed':
        return AllocationStatus.completed;
      case 'rejected':
        return AllocationStatus.rejected;
      default:
        return AllocationStatus.pending;
    }
  }

  ApprovalStatus _mapApprovalStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return ApprovalStatus.approved;
      case 'rejected':
        return ApprovalStatus.rejected;
      default:
        return ApprovalStatus.pending;
    }
  }
}
