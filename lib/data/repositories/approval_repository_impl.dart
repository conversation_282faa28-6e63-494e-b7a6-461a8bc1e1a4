import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/approval/approval_entity.dart';
import '../../domain/entities/common/pagination_entity.dart';
import '../../domain/repositories/approval_repository.dart';
import '../datasources/approval_remote_data_source.dart';
import '../models/approval/approval_model.dart';

class ApprovalRepositoryImpl implements ApprovalRepository {
  final ApprovalRemoteDataSource _remoteDataSource;

  ApprovalRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, PaginatedResult<ApprovalEntity>>> getPendingApprovals({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final result = await _remoteDataSource.getPendingApprovals(
        page: page,
        perPage: perPage,
      );

      final entities = result.approvals.map(_mapApprovalModelToEntity).toList();
      final paginationEntity = PaginationEntity(
        currentPage: result.pagination.currentPage,
        totalPages: result.pagination.totalPages,
        perPage: result.pagination.perPage,
        total: result.pagination.total,
        hasMore: result.pagination.hasMore,
      );

      final paginatedResult = PaginatedResult<ApprovalEntity>(
        items: entities,
        pagination: paginationEntity,
      );

      return Right(paginatedResult);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, PaginatedResult<ApprovalEntity>>> getApprovalHistory({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final result = await _remoteDataSource.getApprovalHistory(
        page: page,
        perPage: perPage,
      );

      final entities = result.approvals.map(_mapApprovalModelToEntity).toList();
      final paginationEntity = PaginationEntity(
        currentPage: result.pagination.currentPage,
        totalPages: result.pagination.totalPages,
        perPage: result.pagination.perPage,
        total: result.pagination.total,
        hasMore: result.pagination.hasMore,
      );

      final paginatedResult = PaginatedResult<ApprovalEntity>(
        items: entities,
        pagination: paginationEntity,
      );

      return Right(paginatedResult);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, ApprovalEntity>> getApprovalById(int id) async {
    try {
      final result = await _remoteDataSource.getApprovalById(id);
      final entity = _mapApprovalModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, ApprovalEntity>> processApproval(
    int testId,
    ApprovalRequestEntity request,
  ) async {
    try {
      final model = _mapApprovalRequestEntityToModel(request);
      final result = await _remoteDataSource.processApproval(testId, model);
      final entity = _mapApprovalModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, BulkApprovalResult>> processBulkApproval(
    BulkApprovalRequestEntity request,
  ) async {
    try {
      final model = _mapBulkApprovalRequestEntityToModel(request);
      final result = await _remoteDataSource.processBulkApproval(model);
      final bulkResult = BulkApprovalResult(
        approvedCount: result.approvedCount,
        failedCount: result.failedCount,
        failedTests: result.failedTests,
      );
      return Right(bulkResult);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, ApprovalEntity>> reassignTest(
    int testId,
    int newApproverId,
    String? remarks,
  ) async {
    try {
      final result = await _remoteDataSource.reassignTest(testId, newApproverId, remarks);
      final entity = _mapApprovalModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper methods for mapping between models and entities
  ApprovalEntity _mapApprovalModelToEntity(ApprovalModel model) {
    return ApprovalEntity(
      id: model.id,
      testId: model.testId,
      approverId: model.approverId,
      approvalStatus: _mapApprovalStatusType(model.approvalStatus),
      approvalDate: model.approvalDate != null 
          ? DateTime.tryParse(model.approvalDate!) : null,
      approvalRemarks: model.approvalRemarks,
      reassignedTo: model.reassignedTo,
      createdAt: model.createdAt != null 
          ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null 
          ? DateTime.tryParse(model.updatedAt!) : null,
      // Note: Related entities (approver, reassignedUser, test) would need 
      // their own mappers if included in the response
    );
  }

  ApprovalRequestModel _mapApprovalRequestEntityToModel(ApprovalRequestEntity entity) {
    return ApprovalRequestModel(
      approvalStatus: _mapApprovalStatusTypeToString(entity.approvalStatus),
      approvalRemarks: entity.approvalRemarks,
      reassignedTo: entity.reassignedTo,
    );
  }

  BulkApprovalRequestModel _mapBulkApprovalRequestEntityToModel(
    BulkApprovalRequestEntity entity,
  ) {
    return BulkApprovalRequestModel(
      testIds: entity.testIds,
      approvalStatus: _mapApprovalStatusTypeToString(entity.approvalStatus),
      approvalRemarks: entity.approvalRemarks,
    );
  }

  ApprovalStatusType _mapApprovalStatusType(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return ApprovalStatusType.approved;
      case 'rejected':
        return ApprovalStatusType.rejected;
      case 'reassigned':
        return ApprovalStatusType.reassigned;
      default:
        return ApprovalStatusType.pending;
    }
  }

  String _mapApprovalStatusTypeToString(ApprovalStatusType status) {
    switch (status) {
      case ApprovalStatusType.approved:
        return 'approved';
      case ApprovalStatusType.rejected:
        return 'rejected';
      case ApprovalStatusType.reassigned:
        return 'reassigned';
      case ApprovalStatusType.pending:
        return 'pending';
    }
  }
}
