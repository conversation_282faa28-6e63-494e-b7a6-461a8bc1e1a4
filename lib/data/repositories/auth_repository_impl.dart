import 'package:dartz/dartz.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/auth/login_entity.dart';
import '../../domain/entities/auth/user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_data_source.dart';
import '../models/auth/login_request_model.dart';
import '../models/auth/update_profile_request_model.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final SharedPreferences _sharedPreferences;

  AuthRepositoryImpl(this._remoteDataSource, this._sharedPreferences);

  @override
  Future<Either<Failure, LoginEntity>> login({
    required String username,
    required String password,
  }) async {
    try {
      final request = LoginRequestModel(
        username: username,
        password: password,
      );

      final result = await _remoteDataSource.login(request);

      // Save token to local storage
      await _sharedPreferences.setString(
        ApiConstants.authTokenKey,
        result.token,
      );

      // Convert to entity
      final loginEntity = LoginEntity(
        token: result.token,
        user: UserEntity(
          id: result.user.id,
          name: result.user.name ?? "NA",
          username: result.user.username ?? "NA",
          email: result.user.email,
          emailVerifiedAt: result.user.emailVerifiedAt != null
              ? DateTime.tryParse(result.user.emailVerifiedAt!)
              : null,
          createdAt: result.user.createdAt != null
              ? DateTime.tryParse(result.user.createdAt!)
              : null,
          updatedAt: result.user.updatedAt != null
              ? DateTime.tryParse(result.user.updatedAt!)
              : null,
        ),
      );

      return Right(loginEntity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> getProfile() async {
    try {
      final result = await _remoteDataSource.getProfile();

      final userEntity = UserEntity(
        id: result.id,
        name: result.name ?? "NA",
        username: result.username ?? "NA",
        email: result.email,
        emailVerifiedAt: result.emailVerifiedAt != null
            ? DateTime.tryParse(result.emailVerifiedAt!)
            : null,
        createdAt: result.createdAt != null
            ? DateTime.tryParse(result.createdAt!)
            : null,
        updatedAt: result.updatedAt != null
            ? DateTime.tryParse(result.updatedAt!)
            : null,
      );

      return Right(userEntity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> updateProfile({
    required String name,
  }) async {
    try {
      final request = UpdateProfileRequestModel(name: name);
      final result = await _remoteDataSource.updateProfile(request);

      final userEntity = UserEntity(
        id: result.id,
        name: result.name ?? "NA",
        username: result.username ?? "NA",
        email: result.email,
        emailVerifiedAt: result.emailVerifiedAt != null
            ? DateTime.tryParse(result.emailVerifiedAt!)
            : null,
        createdAt: result.createdAt != null
            ? DateTime.tryParse(result.createdAt!)
            : null,
        updatedAt: result.updatedAt != null
            ? DateTime.tryParse(result.updatedAt!)
            : null,
      );

      return Right(userEntity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        message: e.message,
        fieldErrors: e.fieldErrors,
      ));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      await _remoteDataSource.logout();
      
      // Clear local storage
      await _sharedPreferences.remove(ApiConstants.authTokenKey);
      await _sharedPreferences.remove(ApiConstants.userDataKey);

      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }
}
