import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../network/dio_client.dart';

// Data sources
import '../../data/datasources/auth_remote_data_source.dart';
import '../../data/datasources/test_request_remote_data_source.dart';
import '../../data/datasources/job_allocation_remote_data_source.dart';
import '../../data/datasources/master_remote_data_source.dart';
import '../../data/datasources/dashboard_remote_data_source.dart';
import '../../data/datasources/completed_test_remote_data_source.dart';
import '../../data/datasources/quality_control_remote_data_source.dart';
import '../../data/datasources/analyst_remote_data_source.dart';
import '../../data/datasources/approval_remote_data_source.dart';
import '../../data/datasources/test_result_remote_data_source.dart';

// Repositories
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/test_request_repository_impl.dart';
import '../../data/repositories/job_allocation_repository_impl.dart';
import '../../data/repositories/master_repository_impl.dart';
import '../../data/repositories/dashboard_repository_impl.dart';
import '../../data/repositories/completed_test_repository_impl.dart';
import '../../data/repositories/quality_control_repository_impl.dart';
import '../../data/repositories/analyst_repository_impl.dart';
import '../../data/repositories/approval_repository_impl.dart';
import '../../data/repositories/test_result_repository_impl.dart';

// Domain repositories
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/test_request_repository.dart';
import '../../domain/repositories/job_allocation_repository.dart';
import '../../domain/repositories/master_repository.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../../domain/repositories/completed_test_repository.dart';
import '../../domain/repositories/quality_control_repository.dart';
import '../../domain/repositories/analyst_repository.dart';
import '../../domain/repositories/approval_repository.dart';
import '../../domain/repositories/test_result_repository.dart';

// Use cases - Auth
import '../../domain/usecases/auth/login_usecase.dart';
import '../../domain/usecases/auth/logout_usecase.dart';
import '../../domain/usecases/auth/get_profile_usecase.dart';
import '../../domain/usecases/auth/update_profile_usecase.dart';
import '../../domain/usecases/auth/login_and_get_profile_usecase.dart';

// Use cases - Test Request
import '../../domain/usecases/test_request/get_test_requests_usecase.dart';
import '../../domain/usecases/test_request/get_test_request_by_id_usecase.dart';
import '../../domain/usecases/test_request/create_test_request_usecase.dart';
import '../../domain/usecases/test_request/update_test_request_usecase.dart';
import '../../domain/usecases/test_request/delete_test_request_usecase.dart';
import '../../domain/usecases/test_request/get_samples_usecase.dart';
import '../../domain/usecases/test_request/add_sample_usecase.dart';
import '../../domain/usecases/test_request/create_test_request_with_samples_usecase.dart';
import '../../domain/usecases/test_request/get_test_request_with_samples_usecase.dart';

// Use cases - Job Allocation
import '../../domain/usecases/job_allocation/get_job_allocations_usecase.dart';
import '../../domain/usecases/job_allocation/get_job_allocation_by_id_usecase.dart';
import '../../domain/usecases/job_allocation/create_job_allocation_usecase.dart';
import '../../domain/usecases/job_allocation/update_job_allocation_usecase.dart';
import '../../domain/usecases/job_allocation/delete_job_allocation_usecase.dart';

// Use cases - Master
import '../../domain/usecases/master/get_parameters_usecase.dart';
import '../../domain/usecases/master/get_customers_usecase.dart';
import '../../domain/usecases/master/add_customer_usecase.dart';

// Use cases - Dashboard
import '../../domain/usecases/dashboard/get_dashboard_stats_usecase.dart';

// Use cases - Completed Tests
import '../../domain/usecases/completed_test/get_completed_tests_usecase.dart';
import '../../domain/usecases/completed_test/get_completed_test_by_id_usecase.dart';

// Use cases - Quality Control
import '../../domain/usecases/quality_control/get_qc_tests_usecase.dart';
import '../../domain/usecases/quality_control/create_qc_test_usecase.dart';
import '../../domain/usecases/quality_control/approve_test_usecase.dart';

// Use cases - Analyst
import '../../domain/usecases/analyst/get_my_tests_usecase.dart';
import '../../domain/usecases/analyst/submit_test_result_usecase.dart';
import '../../domain/usecases/analyst/reject_test_usecase.dart';

// Use cases - Approval
import '../../domain/usecases/approval/get_pending_approvals_usecase.dart';
import '../../domain/usecases/approval/process_approval_usecase.dart';

// Use cases - Test Results
import '../../domain/usecases/test_result/get_test_results_usecase.dart';
import '../../domain/usecases/test_result/create_test_result_usecase.dart';



// BLoCs
import '../../presentation/features/auth/bloc/auth_bloc.dart';
import '../../presentation/features/tests/bloc/test_request_bloc.dart';
import '../../presentation/features/jobs/bloc/job_allocation_bloc.dart';
import '../../presentation/features/dashboard/bloc/dashboard_bloc.dart';
import '../../presentation/features/master/bloc/master_data_bloc.dart';
import '../../presentation/features/completed_tests/bloc/completed_tests_bloc.dart';
import '../../presentation/features/quality_control/bloc/qc_bloc.dart';
import '../../presentation/features/analyst/bloc/analyst_bloc.dart';
import '../../presentation/features/approvals/bloc/approval_bloc.dart';
import '../../presentation/features/test_results/bloc/test_results_bloc.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);

  // Core
  sl.registerLazySingleton(() => DioClient(sl()));

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(sl()),
  );
  
  sl.registerLazySingleton<TestRequestRemoteDataSource>(
    () => TestRequestRemoteDataSourceImpl(sl()),
  );
  
  sl.registerLazySingleton<JobAllocationRemoteDataSource>(
    () => JobAllocationRemoteDataSourceImpl(sl()),
  );
  
  sl.registerLazySingleton<MasterRemoteDataSource>(
    () => MasterRemoteDataSourceImpl(sl()),
  );
  
  sl.registerLazySingleton<DashboardRemoteDataSource>(
    () => DashboardRemoteDataSourceImpl(sl()),
  );

  // New LIMS 2 data sources
  sl.registerLazySingleton<CompletedTestRemoteDataSource>(
    () => CompletedTestRemoteDataSourceImpl(sl()),
  );

  sl.registerLazySingleton<QualityControlRemoteDataSource>(
    () => QualityControlRemoteDataSourceImpl(sl()),
  );

  sl.registerLazySingleton<AnalystRemoteDataSource>(
    () => AnalystRemoteDataSourceImpl(sl()),
  );

  sl.registerLazySingleton<ApprovalRemoteDataSource>(
    () => ApprovalRemoteDataSourceImpl(sl()),
  );

  sl.registerLazySingleton<TestResultRemoteDataSource>(
    () => TestResultRemoteDataSourceImpl(sl()),
  );

  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(sl(), sl()),
  );
  
  sl.registerLazySingleton<TestRequestRepository>(
    () => TestRequestRepositoryImpl(sl()),
  );
  
  sl.registerLazySingleton<JobAllocationRepository>(
    () => JobAllocationRepositoryImpl(sl()),
  );
  
  sl.registerLazySingleton<MasterRepository>(
    () => MasterRepositoryImpl(sl()),
  );
  
  sl.registerLazySingleton<DashboardRepository>(
    () => DashboardRepositoryImpl(sl()),
  );

  // New LIMS 2 repositories
  sl.registerLazySingleton<CompletedTestRepository>(
    () => CompletedTestRepositoryImpl(sl()),
  );

  sl.registerLazySingleton<QualityControlRepository>(
    () => QualityControlRepositoryImpl(sl()),
  );

  sl.registerLazySingleton<AnalystRepository>(
    () => AnalystRepositoryImpl(sl()),
  );

  sl.registerLazySingleton<ApprovalRepository>(
    () => ApprovalRepositoryImpl(sl()),
  );

  sl.registerLazySingleton<TestResultRepository>(
    () => TestResultRepositoryImpl(sl()),
  );

  // Use cases - Auth
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => GetProfileUseCase(sl()));
  sl.registerLazySingleton(() => UpdateProfileUseCase(sl()));
  sl.registerLazySingleton(() => LoginAndGetProfileUseCase(sl()));

  // Use cases - Test Request
  sl.registerLazySingleton(() => GetTestRequestsUseCase(sl()));
  sl.registerLazySingleton(() => GetTestRequestByIdUseCase(sl()));
  sl.registerLazySingleton(() => CreateTestRequestUseCase(sl()));
  sl.registerLazySingleton(() => UpdateTestRequestUseCase(sl()));
  sl.registerLazySingleton(() => DeleteTestRequestUseCase(sl()));
  sl.registerLazySingleton(() => GetSamplesUseCase(sl()));
  sl.registerLazySingleton(() => AddSampleUseCase(sl()));
  sl.registerLazySingleton(() => CreateTestRequestWithSamplesUseCase(sl()));
  sl.registerLazySingleton(() => GetTestRequestWithSamplesUseCase(sl()));

  // Use cases - Job Allocation
  sl.registerLazySingleton(() => GetJobAllocationsUseCase(sl()));
  sl.registerLazySingleton(() => GetJobAllocationByIdUseCase(sl()));
  sl.registerLazySingleton(() => CreateJobAllocationUseCase(sl()));
  sl.registerLazySingleton(() => UpdateJobAllocationUseCase(sl()));
  sl.registerLazySingleton(() => DeleteJobAllocationUseCase(sl()));

  // Use cases - Master
  sl.registerLazySingleton(() => GetParametersUseCase(sl()));
  sl.registerLazySingleton(() => GetCustomersUseCase(sl()));
  sl.registerLazySingleton(() => AddCustomerUseCase(sl()));

  // Use cases - Dashboard
  sl.registerLazySingleton(() => GetDashboardStatsUseCase(sl()));

  // Use cases - Completed Tests
  sl.registerLazySingleton(() => GetCompletedTestsUseCase(sl()));
  sl.registerLazySingleton(() => GetCompletedTestByIdUseCase(sl()));

  // Use cases - Quality Control
  sl.registerLazySingleton(() => GetQCTestsUseCase(sl()));
  sl.registerLazySingleton(() => CreateQCTestUseCase(sl()));
  sl.registerLazySingleton(() => ApproveTestUseCase(sl()));

  // Use cases - Analyst
  sl.registerLazySingleton(() => GetMyTestsUseCase(sl()));
  sl.registerLazySingleton(() => SubmitTestResultUseCase(sl()));
  sl.registerLazySingleton(() => RejectTestUseCase(sl()));

  // Use cases - Approval
  sl.registerLazySingleton(() => GetPendingApprovalsUseCase(sl()));
  sl.registerLazySingleton(() => ProcessApprovalUseCase(sl()));

  // Use cases - Test Results
  sl.registerLazySingleton(() => GetTestResultsUseCase(sl()));
  sl.registerLazySingleton(() => CreateTestResultUseCase(sl()));



  // BLoCs
  sl.registerFactory(() => AuthBloc(
    loginUseCase: sl(),
    logoutUseCase: sl(),
    getProfileUseCase: sl(),
    updateProfileUseCase: sl(),
    loginAndGetProfileUseCase: sl(),
    sharedPreferences: sl(),
  ));

  sl.registerFactory(() => TestRequestBloc(
    getTestRequestsUseCase: sl(),
    getTestRequestByIdUseCase: sl(),
    getTestRequestWithSamplesUseCase: sl(),
    createTestRequestUseCase: sl(),
    createTestRequestWithSamplesUseCase: sl(),
    updateTestRequestUseCase: sl(),
    deleteTestRequestUseCase: sl(),
    getSamplesUseCase: sl(),
    addSampleUseCase: sl(),
  ));

  sl.registerFactory(() => JobAllocationBloc(
    getJobAllocationsUseCase: sl(),
    getJobAllocationByIdUseCase: sl(),
    createJobAllocationUseCase: sl(),
    updateJobAllocationUseCase: sl(),
    deleteJobAllocationUseCase: sl(),
  ));

  sl.registerFactory(() => DashboardBloc(
    getDashboardStatsUseCase: sl(),
  ));

  sl.registerFactory(() => MasterDataBloc(
    getParametersUseCase: sl(),
    getCustomersUseCase: sl(),
    addCustomerUseCase: sl(),
  ));

  // New LIMS 2 BLoCs
  sl.registerFactory(() => CompletedTestsBloc(
    getCompletedTestsUseCase: sl(),
    getCompletedTestByIdUseCase: sl(),
  ));

  sl.registerFactory(() => QCBloc(
    getQCTestsUseCase: sl(),
    createQCTestUseCase: sl(),
    approveTestUseCase: sl(),
  ));

  sl.registerFactory(() => AnalystBloc(
    getMyTestsUseCase: sl(),
    submitTestResultUseCase: sl(),
    rejectTestUseCase: sl(),
  ));

  sl.registerFactory(() => ApprovalBloc(
    getPendingApprovalsUseCase: sl(),
    processApprovalUseCase: sl(),
  ));

  sl.registerFactory(() => TestResultsBloc(
    getTestResultsUseCase: sl(),
    createTestResultUseCase: sl(),
  ));
}
