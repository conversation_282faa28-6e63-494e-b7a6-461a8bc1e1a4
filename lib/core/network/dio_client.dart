import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/api_constants.dart';
import '../error/exceptions.dart';

class DioClient {
  late final Dio _dio;
  final SharedPreferences _sharedPreferences;

  DioClient(this._sharedPreferences) {
    _dio = Dio(
      BaseOptions(
        baseUrl: ApiConstants.baseUrl,
        connectTimeout: Duration(milliseconds: ApiConstants.connectTimeout),
        receiveTimeout: Duration(milliseconds: ApiConstants.receiveTimeout),
        sendTimeout: Duration(milliseconds: ApiConstants.sendTimeout),
        headers: {
          'Content-Type': ApiConstants.contentType,
        },
      ),
    );

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token if available
          final token = _sharedPreferences.getString(ApiConstants.authTokenKey);
          if (token != null && token.isNotEmpty) {
            options.headers[ApiConstants.authorization] = '${ApiConstants.bearer} $token';
          }
          handler.next(options);
        },
        onError: (error, handler) {
          final exception = _handleDioError(error);
          handler.reject(DioException(
            requestOptions: error.requestOptions,
            error: exception,
            type: error.type,
            response: error.response,
          ));
        },
      ),
    );

    // Add logging interceptor in debug mode
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: false,
    ));
  }

  Dio get dio => _dio;

  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkException(
          message: 'Connection timeout. Please check your internet connection.',
        );
      case DioExceptionType.badResponse:
        return _handleStatusCode(error.response?.statusCode, error.response?.data);
      case DioExceptionType.cancel:
        return const NetworkException(message: 'Request was cancelled');
      case DioExceptionType.connectionError:
        return const NetworkException(
          message: 'No internet connection. Please check your network.',
        );
      default:
        return NetworkException(
          message: error.message ?? 'An unexpected error occurred',
        );
    }
  }

  Exception _handleStatusCode(int? statusCode, dynamic responseData) {
    String message = 'An error occurred';
    Map<String, List<String>>? fieldErrors;
    String? generalError;

    if (responseData is Map<String, dynamic>) {
      message = responseData['message'] ?? 'An error occurred';

      // Check for field-specific validation errors (errors - plural)
      if (responseData['errors'] is Map<String, dynamic>) {
        fieldErrors = _parseValidationErrors(responseData['errors']);
      }

      // Check for general error (error - singular)
      if (responseData['error'] is String) {
        generalError = responseData['error'];
      }
    }

    switch (statusCode) {
      case 400:
        // If we have field errors, it's a validation error
        if (fieldErrors != null && fieldErrors.isNotEmpty) {
          return ValidationException(
            message: message,
            fieldErrors: fieldErrors,
          );
        }
        // Otherwise, it's a general error
        return GeneralException(
          message: message,
          details: generalError,
        );
      case 401:
        return UnauthorizedException(message: message);
      case 403:
        return ServerException(message: 'Access forbidden', statusCode: statusCode);
      case 404:
        // 404 errors often have specific details in the 'error' field
        return GeneralException(
          message: message,
          details: generalError ?? 'Resource not found',
        );
      case 422:
        // Laravel validation errors typically use 422 status code
        return ValidationException(
          message: message,
          fieldErrors: fieldErrors,
        );
      case 500:
        return ServerException(message: 'Internal server error', statusCode: statusCode);
      default:
        // For other status codes, check if we have specific error details
        if (generalError != null) {
          return GeneralException(
            message: message,
            details: generalError,
          );
        }
        return ServerException(message: message, statusCode: statusCode);
    }
  }

  /// Parse validation errors from API response
  Map<String, List<String>> _parseValidationErrors(Map<String, dynamic> errors) {
    final Map<String, List<String>> fieldErrors = {};

    errors.forEach((key, value) {
      if (value is List) {
        fieldErrors[key] = value.map((e) => e.toString()).toList();
      } else if (value is String) {
        fieldErrors[key] = [value];
      }
    });

    return fieldErrors;
  }
}
