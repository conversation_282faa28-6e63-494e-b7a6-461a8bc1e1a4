import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

// Generic API response for all endpoints
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final Map<String, dynamic>? errors; // For validation errors (field-specific)
  final String? error; // For general errors (single error message)

  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
    this.error,
  });

  /// Helper method to get field-specific validation errors
  Map<String, List<String>> get validationErrors {
    if (errors == null) return {};

    final Map<String, List<String>> fieldErrors = {};

    errors!.forEach((key, value) {
      if (value is List) {
        fieldErrors[key] = value.map((e) => e.toString()).toList();
      } else if (value is String) {
        fieldErrors[key] = [value];
      }
    });

    return fieldErrors;
  }

  /// Check if this response has validation errors (field-specific)
  bool get hasValidationErrors => errors != null && errors!.isNotEmpty;

  /// Check if this response has a general error
  bool get hasGeneralError => error != null && error!.isNotEmpty;

  /// Get the appropriate error message
  String get errorMessage {
    if (hasGeneralError) {
      return error!;
    } else if (message != null && message!.isNotEmpty) {
      return message!;
    } else {
      return 'An error occurred';
    }
  }

  /// Check if this is a validation error response
  bool get isValidationError => hasValidationErrors && !hasGeneralError;

  /// Check if this is a general error response
  bool get isGeneralError => hasGeneralError || (!hasValidationErrors && !success);

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);
}

// Pagination metadata for test-requests API
@JsonSerializable()
class PaginationMeta {
  @JsonKey(name: 'current_page')
  final int currentPage;

  @JsonKey(name: 'total_pages')
  final int totalPages;

  @JsonKey(name: 'per_page')
  final int perPage;

  final int total;

  @JsonKey(name: 'has_more')
  final bool hasMore;

  const PaginationMeta({
    required this.currentPage,
    required this.totalPages,
    required this.perPage,
    required this.total,
    required this.hasMore,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}

// Simple response for operations that don't return data
@JsonSerializable()
class SimpleApiResponse {
  final bool success;
  final String? message;

  const SimpleApiResponse({
    required this.success,
    this.message,
  });

  factory SimpleApiResponse.fromJson(Map<String, dynamic> json) =>
      _$SimpleApiResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SimpleApiResponseToJson(this);
}
