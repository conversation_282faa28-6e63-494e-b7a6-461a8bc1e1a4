// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApiResponse<T> _$ApiResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => $checkedCreate('ApiResponse', json, ($checkedConvert) {
  final val = ApiResponse<T>(
    success: $checkedConvert('success', (v) => v as bool),
    message: $checkedConvert('message', (v) => v as String?),
    data: $checkedConvert(
      'data',
      (v) => _$nullableGenericFromJson(v, fromJsonT),
    ),
    errors: $checkedConvert('errors', (v) => v as Map<String, dynamic>?),
    error: $checkedConvert('error', (v) => v as String?),
  );
  return val;
});

Map<String, dynamic> _$ApiResponseToJson<T>(
  ApiResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
  'data': _$nullableGenericToJson(instance.data, toJsonT),
  'errors': instance.errors,
  'error': instance.error,
};

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) => input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) => input == null ? null : toJson(input);

PaginationMeta _$PaginationMetaFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'PaginationMeta',
      json,
      ($checkedConvert) {
        final val = PaginationMeta(
          currentPage: $checkedConvert(
            'current_page',
            (v) => (v as num).toInt(),
          ),
          totalPages: $checkedConvert('total_pages', (v) => (v as num).toInt()),
          perPage: $checkedConvert('per_page', (v) => (v as num).toInt()),
          total: $checkedConvert('total', (v) => (v as num).toInt()),
          hasMore: $checkedConvert('has_more', (v) => v as bool),
        );
        return val;
      },
      fieldKeyMap: const {
        'currentPage': 'current_page',
        'totalPages': 'total_pages',
        'perPage': 'per_page',
        'hasMore': 'has_more',
      },
    );

Map<String, dynamic> _$PaginationMetaToJson(PaginationMeta instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'total_pages': instance.totalPages,
      'per_page': instance.perPage,
      'total': instance.total,
      'has_more': instance.hasMore,
    };

SimpleApiResponse _$SimpleApiResponseFromJson(Map<String, dynamic> json) =>
    $checkedCreate('SimpleApiResponse', json, ($checkedConvert) {
      final val = SimpleApiResponse(
        success: $checkedConvert('success', (v) => v as bool),
        message: $checkedConvert('message', (v) => v as String?),
      );
      return val;
    });

Map<String, dynamic> _$SimpleApiResponseToJson(SimpleApiResponse instance) =>
    <String, dynamic>{'success': instance.success, 'message': instance.message};
