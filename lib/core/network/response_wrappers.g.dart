// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'response_wrappers.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaginatedListResponse<T> _$PaginatedListResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => $checkedCreate('PaginatedListResponse', json, ($checkedConvert) {
  final val = PaginatedListResponse<T>(
    items: $checkedConvert(
      'items',
      (v) => (v as List<dynamic>).map(fromJsonT).toList(),
    ),
    pagination: $checkedConvert(
      'pagination',
      (v) => PaginationMeta.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$PaginatedListResponseToJson<T>(
  PaginatedListResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'items': instance.items.map(toJsonT).toList(),
  'pagination': instance.pagination.toJson(),
};

ListResponse<T> _$ListResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => $checkedCreate('ListResponse', json, ($checkedConvert) {
  final val = ListResponse<T>(
    items: $checkedConvert(
      'items',
      (v) => (v as List<dynamic>).map(fromJsonT).toList(),
    ),
  );
  return val;
});

Map<String, dynamic> _$ListResponseToJson<T>(
  ListResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{'items': instance.items.map(toJsonT).toList()};

SingleItemResponse<T> _$SingleItemResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => $checkedCreate('SingleItemResponse', json, ($checkedConvert) {
  final val = SingleItemResponse<T>(
    item: $checkedConvert('item', (v) => fromJsonT(v)),
  );
  return val;
});

Map<String, dynamic> _$SingleItemResponseToJson<T>(
  SingleItemResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{'item': toJsonT(instance.item)};
