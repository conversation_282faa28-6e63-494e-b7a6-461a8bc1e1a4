import 'package:json_annotation/json_annotation.dart';
import 'api_response.dart';

part 'response_wrappers.g.dart';

// Generic wrapper for paginated list responses
@JsonSerializable(genericArgumentFactories: true)
class PaginatedListResponse<T> {
  final List<T> items;
  final PaginationMeta pagination;

  const PaginatedListResponse({
    required this.items,
    required this.pagination,
  });

  factory PaginatedListResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedListResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginatedListResponseToJson(this, toJsonT);
}

// Generic wrapper for simple list responses
@JsonSerializable(genericArgumentFactories: true)
class ListResponse<T> {
  final List<T> items;

  const ListResponse({
    required this.items,
  });

  factory ListResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ListResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ListResponseToJson(this, toJsonT);
}

// Type definitions for common response patterns
typedef SimpleResponse<T> = ApiResponse<T>;
typedef SimpleListResponse<T> = ApiResponse<ListResponse<T>>;
typedef PaginatedResponse<T> = ApiResponse<PaginatedListResponse<T>>;

// Single item wrapper responses
@JsonSerializable(genericArgumentFactories: true)
class SingleItemResponse<T> {
  final T item;

  const SingleItemResponse({
    required this.item,
  });

  factory SingleItemResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$SingleItemResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$SingleItemResponseToJson(this, toJsonT);
}
