import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../error/failures.dart';

/// Base class for all use cases
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

/// Use this class for use cases that don't need any parameters
class NoParams extends Equatable {
  const NoParams();

  @override
  List<Object> get props => [];
}
