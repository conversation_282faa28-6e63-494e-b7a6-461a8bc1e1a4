class ApiConstants {
  // Base URL
  static const String baseUrl = 'https://managemylab.in/public';
  static const String apiVersion = '/api/v1';

  // ========================================
  // ENDPOINTS FROM LIMS 2 POSTMAN COLLECTION
  // ========================================

  // Auth endpoints
  static const String login = '$apiVersion/auth/login';
  static const String logout = '$apiVersion/auth/logout';
  static const String profile = '$apiVersion/auth/profile';

  // Test Request endpoints
  static const String testRequests = '$apiVersion/test-requests';
  static String testRequestById(int id) => '$testRequests/$id';
  static String testRequestSamples(int id) => '$testRequests/$id/samples';

  // Job Allocation endpoints
  static const String jobAllocations = '$apiVersion/job-allocations';
  static String jobAllocationById(int id) => '$jobAllocations/$id';
  static const String jobAllocationExportExcel = '$jobAllocations/export/excel';
  static String jobAllocationPdf(int id) => '$jobAllocations/$id/pdf';
  static const String jobAllocationAnalysts = '$apiVersion/job-allocations/analysts';

  // Completed Tests endpoints
  static const String completedTests = '$apiVersion/completed-tests';
  static String completedTestById(int id) => '$completedTests/$id';
  static const String completedTestsExportExcel = '$completedTests/export/excel';

  // Quality Control endpoints
  static const String qualityControlTests = '$apiVersion/quality-control/tests';
  static const String qualityControlRetest = '$apiVersion/quality-control/retest';
  static const String qualityControlBlind = '$apiVersion/quality-control/blind';
  static const String qualityControlReplicate = '$apiVersion/quality-control/replicate';
  static String qualityControlParameter(int id) => '$apiVersion/quality-control/parameter/$id';

  // Dashboard endpoints
  static const String dashboardStats = '$apiVersion/dashboard/stats';

  // Masters endpoints
  static const String parameters = '$apiVersion/masters/parameters';
  static const String customers = '$apiVersion/masters/customers';
  static String searchCustomers(String query) => '$customers?search=$query';

  // Test Results endpoints (From Postman Collection)
  static const String testResults = '$apiVersion/test-results';
  static String testResultById(int id) => '$testResults/$id';
  static String addTestResult = '$testResults';
  static String updateTestResult(int id) => '$testResults/$id';
  static String approveTestResult(int id) => '$testResults/$id/approve';
  static String rejectTestResult(int id) => '$testResults/$id/reject';
  static const String activeTests = '$testResults/active-tests';
  static String acceptTest(int id) => '$testResults/$id/accept';
  static String rejectTest(int id) => '$testResults/$id/reject';
  static const String listMyTests = '$testResults/my-tests';
  static String addResult(int id) => '$testResults/$id/result';
  static String reassignTest(int id) => '$testResults/$id/reassign';

  // Approvals endpoints (From Postman Collection)
  static const String approvals = '$apiVersion/approvals';
  static String approvalById(int id) => '$approvals/$id';
  static String processApproval(int id) => '$approvals/$id/approve';
  static const String pendingApprovals = '$approvals/pending';
  static const String approvedApprovals = '$approvals/approved';
  static const String rejectedApprovals = '$approvals/rejected';
  static String reassignApproval(int id) => '$approvals/$id/reassign';
  static String rejectApproval(int id) => '$approvals/$id/reject';

  // Reminders endpoints (From Postman Collection)
  static const String reminders = '$apiVersion/reminders';
  static String reminderById(int id) => '$reminders/$id';
  static const String activeReminders = '$reminders/active';

  // ========================================
  // ENDPOINT HELPER METHODS
  // ========================================

  /// Get full URL for any endpoint
  static String getFullUrl(String endpoint) => '$baseUrl$endpoint';

  /// Get all available endpoints for documentation
  static Map<String, List<String>> getAllEndpoints() {
    return {
      'Auth': [login, logout, profile],
      'Test Requests': [testRequests, '$testRequests/{id}', '$testRequests/{id}/samples'],
      'Job Allocations': [jobAllocations, '$jobAllocations/{id}', jobAllocationExportExcel, '$jobAllocations/{id}/pdf', jobAllocationAnalysts],
      'Completed Tests': [completedTests, '$completedTests/{id}', completedTestsExportExcel],
      'Quality Control': [qualityControlTests, qualityControlRetest, qualityControlBlind, qualityControlReplicate],
      'Dashboard': [dashboardStats],
      'Masters': [parameters, customers],
      'Test Results': [testResults, '$testResults/{id}', activeTests, listMyTests],
      'Approvals': [approvals, '$approvals/{id}', pendingApprovals, approvedApprovals, rejectedApprovals],
      'Reminders': [reminders, '$reminders/{id}', activeReminders],
    };
  }

  /// Check if an endpoint requires authentication
  static bool requiresAuth(String endpoint) {
    final publicEndpoints = [login];
    return !publicEndpoints.contains(endpoint);
  }
  
  // Headers
  static const String contentType = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearer = 'Bearer';
  
  // Storage keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  
  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds
}
