class ApiConstants {
  // Base URL
  static const String baseUrl = 'https://managemylab.in/public';
  static const String apiVersion = '/api/v1';

  // ========================================
  // ENDPOINTS FROM OPENAPI SPECIFICATION
  // ========================================

  // Auth endpoints
  static const String login = '$apiVersion/auth/login';
  static const String logout = '$apiVersion/auth/logout';
  static const String profile = '$apiVersion/auth/profile';

  // Test Request endpoints
  static const String testRequests = '$apiVersion/test-requests';
  static String testRequestById(int id) => '$testRequests/$id';
  static String testRequestSamples(int id) => '$testRequests/$id/samples';

  // Job Allocation endpoints
  static const String jobAllocations = '$apiVersion/job-allocations';
  static String jobAllocationById(int id) => '$jobAllocations/$id';
  static const String jobAllocationExportExcel = '$jobAllocations/export/excel';
  static String jobAllocationPdf(int id) => '$jobAllocations/$id/pdf';

  // Completed Tests endpoints
  static const String completedTests = '$apiVersion/completed-tests';
  static String completedTestById(int id) => '$completedTests/$id';

  // Quality Control endpoints
  static const String qualityControlTests = '$apiVersion/quality-control/tests';
  static String approveQCTest(int id) => '$qualityControlTests/$id/approve';

  // Analyst endpoints
  static const String analystMyTests = '$apiVersion/analyst/my-tests';
  static String submitTestResult(int id) => '$apiVersion/analyst/tests/$id/submit';
  static String rejectTest(int id) => '$apiVersion/analyst/tests/$id/reject';

  // Dashboard endpoints
  static const String dashboardStats = '$apiVersion/dashboard/stats';

  // Masters endpoints
  static const String parameters = '$apiVersion/masters/parameters';
  static const String customers = '$apiVersion/masters/customers';

  // ========================================
  // CUSTOM ENDPOINTS (NOT IN OPENAPI SPEC)
  // ========================================
  // Note: These endpoints may be custom implementations
  // Verify with backend team before using

  // Approval endpoints (CUSTOM - not in OpenAPI spec)
  static const String approvals = '$apiVersion/approvals';
  static const String approvalHistory = '$apiVersion/approvals/history';
  static String approvalById(int id) => '$approvals/$id';
  static String processApproval(int testId) => '$apiVersion/tests/$testId/approve';
  static const String bulkApproval = '$apiVersion/approvals/bulk';
  static String reassignTest(int testId) => '$apiVersion/tests/$testId/reassign';

  // Test Results endpoints (CUSTOM - not in OpenAPI spec)
  static const String testResults = '$apiVersion/test-results';
  static String testResultById(int id) => '$testResults/$id';
  static String myTestResults(int analystId) => '$apiVersion/analysts/$analystId/test-results';
  static const String testResultsRequiringAttention = '$testResults/requiring-attention';

  // ========================================
  // ENDPOINT HELPER METHODS
  // ========================================

  /// Get full URL for any endpoint
  static String getFullUrl(String endpoint) => '$baseUrl$endpoint';

  /// Check if endpoint is from OpenAPI specification
  static bool isOpenApiEndpoint(String endpoint) {
    final openApiEndpoints = [
      login, logout, profile,
      testRequests, completedTests, qualityControlTests,
      analystMyTests, dashboardStats, parameters, customers,
      jobAllocations, jobAllocationExportExcel,
    ];

    return openApiEndpoints.any((openApiEndpoint) =>
      endpoint.startsWith(openApiEndpoint.replaceAll(apiVersion, '')));
  }
  
  // Headers
  static const String contentType = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearer = 'Bearer';
  
  // Storage keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  
  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds
}
