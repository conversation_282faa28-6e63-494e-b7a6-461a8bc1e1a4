class ApiConstants {
  // Base URL
  static const String baseUrl = 'https://managemylab.in/public';
  static const String apiVersion = '/api/v1';
  
  // Auth endpoints
  static const String login = '$apiVersion/auth/login';
  static const String logout = '$apiVersion/auth/logout';
  static const String profile = '$apiVersion/auth/profile';
  
  // Test Request endpoints
  static const String testRequests = '$apiVersion/test-requests';
  static String testRequestById(int id) => '$testRequests/$id';
  static String testRequestSamples(int id) => '$testRequests/$id/samples';
  
  // Job Allocation endpoints
  static const String jobAllocations = '$apiVersion/job-allocations';
  static String jobAllocationById(int id) => '$jobAllocations/$id';
  
  // Masters endpoints
  static const String parameters = '$apiVersion/masters/parameters';
  static const String customers = '$apiVersion/masters/customers';
  
  // Dashboard endpoints
  static const String dashboardStats = '$apiVersion/dashboard/stats';
  
  // Headers
  static const String contentType = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearer = 'Bearer';
  
  // Storage keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  
  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds
}
