class ApiConstants {
  // Base URL
  static const String baseUrl = 'https://managemylab.in/public';
  static const String apiVersion = '/api/v1';
  
  // Auth endpoints
  static const String login = '$apiVersion/auth/login';
  static const String logout = '$apiVersion/auth/logout';
  static const String profile = '$apiVersion/auth/profile';
  
  // Test Request endpoints
  static const String testRequests = '$apiVersion/test-requests';
  static String testRequestById(int id) => '$testRequests/$id';
  static String testRequestSamples(int id) => '$testRequests/$id/samples';
  
  // Job Allocation endpoints
  static const String jobAllocations = '$apiVersion/job-allocations';
  static String jobAllocationById(int id) => '$jobAllocations/$id';
  
  // Masters endpoints
  static const String parameters = '$apiVersion/masters/parameters';
  static const String customers = '$apiVersion/masters/customers';
  
  // Dashboard endpoints
  static const String dashboardStats = '$apiVersion/dashboard/stats';

  // Completed Tests endpoints
  static const String completedTests = '$apiVersion/completed-tests';
  static String completedTestById(int id) => '$completedTests/$id';

  // Quality Control endpoints
  static const String qcTests = '$apiVersion/quality-control/tests';
  static String approveQCTest(int id) => '$qcTests/$id/approve';

  // Analyst endpoints
  static const String analystMyTests = '$apiVersion/analyst/my-tests';
  static String analystSubmitTestResult(int id) => '$apiVersion/analyst/tests/$id/submit';
  static String analystRejectTest(int id) => '$apiVersion/analyst/tests/$id/reject';

  // Approval endpoints
  static const String approvals = '$apiVersion/approvals';
  static const String approvalHistory = '$apiVersion/approvals/history';
  static String approvalById(int id) => '$approvals/$id';
  static String processApproval(int testId) => '$apiVersion/tests/$testId/approve';
  static const String bulkApproval = '$apiVersion/approvals/bulk';
  static String reassignTest(int testId) => '$apiVersion/tests/$testId/reassign';

  // Test Results endpoints
  static const String testResults = '$apiVersion/test-results';
  static String testResultById(int id) => '$testResults/$id';
  static String myTestResults(int analystId) => '$apiVersion/analysts/$analystId/test-results';
  static const String testResultsRequiringAttention = '$testResults/requiring-attention';
  
  // Headers
  static const String contentType = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearer = 'Bearer';
  
  // Storage keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  
  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds
}
