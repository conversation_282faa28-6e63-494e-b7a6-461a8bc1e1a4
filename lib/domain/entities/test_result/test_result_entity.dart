import 'package:equatable/equatable.dart';
import '../master/parameter_entity.dart';
import '../test_request/sample_entity.dart';
import '../auth/user_entity.dart';

enum TestResultStatus { pending, completed, approved, rejected, underReview }

class TestResultEntity extends Equatable {
  final int id;
  final int parameterAllocationId;
  final int parameterId;
  final int sampleId;
  final int analystId;
  final String result;
  final String? unit;
  final String? testMethod;
  final String? instrumentUsed;
  final DateTime? analysisDate;
  final TestResultStatus resultStatus;
  final bool isWithinLimit;
  final String? lowerLimit;
  final String? upperLimit;
  final String? detectionLimit;
  final String? uncertainty;
  final String? dilutionFactor;
  final String? remarks;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final ParameterEntity? parameter;
  final SampleEntity? sample;
  final UserEntity? analyst;

  const TestResultEntity({
    required this.id,
    required this.parameterAllocationId,
    required this.parameterId,
    required this.sampleId,
    required this.analystId,
    required this.result,
    this.unit,
    this.testMethod,
    this.instrumentUsed,
    this.analysisDate,
    this.resultStatus = TestResultStatus.pending,
    this.isWithinLimit = true,
    this.lowerLimit,
    this.upperLimit,
    this.detectionLimit,
    this.uncertainty,
    this.dilutionFactor,
    this.remarks,
    this.createdAt,
    this.updatedAt,
    this.parameter,
    this.sample,
    this.analyst,
  });

  @override
  List<Object?> get props => [
        id,
        parameterAllocationId,
        parameterId,
        sampleId,
        analystId,
        result,
        unit,
        testMethod,
        instrumentUsed,
        analysisDate,
        resultStatus,
        isWithinLimit,
        lowerLimit,
        upperLimit,
        detectionLimit,
        uncertainty,
        dilutionFactor,
        remarks,
        createdAt,
        updatedAt,
        parameter,
        sample,
        analyst,
      ];

  bool get isPending => resultStatus == TestResultStatus.pending;
  bool get isCompleted => resultStatus == TestResultStatus.completed;
  bool get isApproved => resultStatus == TestResultStatus.approved;
  bool get isRejected => resultStatus == TestResultStatus.rejected;
  bool get isUnderReview => resultStatus == TestResultStatus.underReview;
  
  bool get hasLimits => lowerLimit != null || upperLimit != null;
  bool get hasUncertainty => uncertainty != null && uncertainty!.isNotEmpty;
  bool get hasDilution => dilutionFactor != null && dilutionFactor!.isNotEmpty;
  bool get hasDetectionLimit => detectionLimit != null && detectionLimit!.isNotEmpty;
  
  bool get requiresAttention => !isWithinLimit || isRejected;
  
  String get displayResult {
    if (unit != null && unit!.isNotEmpty) {
      return '$result $unit';
    }
    return result;
  }
}

class CreateTestResultRequestEntity extends Equatable {
  final int parameterAllocationId;
  final String result;
  final String? unit;
  final String? testMethod;
  final String? instrumentUsed;
  final DateTime analysisDate;
  final String? lowerLimit;
  final String? upperLimit;
  final String? detectionLimit;
  final String? uncertainty;
  final String? dilutionFactor;
  final String? remarks;

  const CreateTestResultRequestEntity({
    required this.parameterAllocationId,
    required this.result,
    this.unit,
    this.testMethod,
    this.instrumentUsed,
    required this.analysisDate,
    this.lowerLimit,
    this.upperLimit,
    this.detectionLimit,
    this.uncertainty,
    this.dilutionFactor,
    this.remarks,
  });

  @override
  List<Object?> get props => [
        parameterAllocationId,
        result,
        unit,
        testMethod,
        instrumentUsed,
        analysisDate,
        lowerLimit,
        upperLimit,
        detectionLimit,
        uncertainty,
        dilutionFactor,
        remarks,
      ];
}

class UpdateTestResultRequestEntity extends Equatable {
  final String result;
  final String? unit;
  final String? testMethod;
  final String? instrumentUsed;
  final DateTime analysisDate;
  final String? lowerLimit;
  final String? upperLimit;
  final String? detectionLimit;
  final String? uncertainty;
  final String? dilutionFactor;
  final String? remarks;

  const UpdateTestResultRequestEntity({
    required this.result,
    this.unit,
    this.testMethod,
    this.instrumentUsed,
    required this.analysisDate,
    this.lowerLimit,
    this.upperLimit,
    this.detectionLimit,
    this.uncertainty,
    this.dilutionFactor,
    this.remarks,
  });

  @override
  List<Object?> get props => [
        result,
        unit,
        testMethod,
        instrumentUsed,
        analysisDate,
        lowerLimit,
        upperLimit,
        detectionLimit,
        uncertainty,
        dilutionFactor,
        remarks,
      ];
}
