import 'package:equatable/equatable.dart';
import '../job_allocation/job_allocation_entity.dart';
import '../parameter_allocation/parameter_allocation_entity.dart';

class AnalystTestEntity extends JobAllocationEntity {
  final List<ParameterAllocationEntity>? parameterAllocations;

  const AnalystTestEntity({
    required super.id,
    required super.serialNo,
    required super.creationDate,
    required super.codeNumber,
    required super.nature,
    required super.quantity,
    required super.collectionDate,
    required super.submissionDate,
    required super.dueDate,
    required super.userId,
    required super.testRequestId,
    super.reportType,
    required super.designation,
    super.remarks,
    required super.nablStatus,
    super.createdAt,
    super.updatedAt,
    this.parameterAllocations,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        parameterAllocations,
      ];

  int get totalParameters => parameterAllocations?.length ?? 0;
  
  int get completedParameters => parameterAllocations
      ?.where((allocation) => allocation.isCompleted)
      .length ?? 0;
      
  int get pendingParameters => parameterAllocations
      ?.where((allocation) => allocation.isPending)
      .length ?? 0;
      
  int get rejectedParameters => parameterAllocations
      ?.where((allocation) => allocation.isRejected)
      .length ?? 0;

  double get completionPercentage {
    if (totalParameters == 0) return 0.0;
    return (completedParameters / totalParameters) * 100;
  }

  bool get isFullyCompleted => completedParameters == totalParameters && totalParameters > 0;
  bool get hasRejectedParameters => rejectedParameters > 0;
  
  List<ParameterAllocationEntity> get qualityControlAllocations =>
      parameterAllocations?.where((allocation) => allocation.isQualityControlTest).toList() ?? [];
      
  bool get hasQualityControlTests => qualityControlAllocations.isNotEmpty;
}

class SubmitTestResultRequestEntity extends Equatable {
  final String result;
  final DateTime analysisStartDate;
  final DateTime analysisCompletionDate;
  final DateTime analysisSubmissionDate;
  final String? rawWater;
  final String? filteredWater;
  final String? treatedWater;
  final String? location1;
  final String? location2;
  final String? location3;
  final String? location4;
  final String? location5;
  final String? location6;

  const SubmitTestResultRequestEntity({
    required this.result,
    required this.analysisStartDate,
    required this.analysisCompletionDate,
    required this.analysisSubmissionDate,
    this.rawWater,
    this.filteredWater,
    this.treatedWater,
    this.location1,
    this.location2,
    this.location3,
    this.location4,
    this.location5,
    this.location6,
  });

  @override
  List<Object?> get props => [
        result,
        analysisStartDate,
        analysisCompletionDate,
        analysisSubmissionDate,
        rawWater,
        filteredWater,
        treatedWater,
        location1,
        location2,
        location3,
        location4,
        location5,
        location6,
      ];

  bool get hasWaterTypeResults => [rawWater, filteredWater, treatedWater]
      .any((water) => water != null && water.isNotEmpty);
      
  bool get hasLocationResults => [location1, location2, location3, location4, location5, location6]
      .any((location) => location != null && location.isNotEmpty);
      
  Duration get analysisDuration => analysisCompletionDate.difference(analysisStartDate);
}

class RejectTestRequestEntity extends Equatable {
  final String rejectionReason;

  const RejectTestRequestEntity({
    required this.rejectionReason,
  });

  @override
  List<Object?> get props => [rejectionReason];
}
