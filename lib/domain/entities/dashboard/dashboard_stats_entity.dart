import 'package:equatable/equatable.dart';

abstract class BaseStatEntity extends Equatable {
  const BaseStatEntity();

  @override
  String toString() {
    return props.join(', ');
  }
}

class TotalStatsEntity extends BaseStatEntity {
  final int totalTestRequests;
  final int totalJobAllocations;
  final int totalUsers;

  const TotalStatsEntity({
    required this.totalTestRequests,
    required this.totalJobAllocations,
    required this.totalUsers,
  });

  @override
  List<Object> get props => [
        totalTestRequests,
        totalJobAllocations,
        totalUsers,
      ];

}

class MonthlyStatsEntity extends BaseStatEntity {
  final int monthlyTestRequests;
  final int monthlyJobAllocations;

  const MonthlyStatsEntity({
    required this.monthlyTestRequests,
    required this.monthlyJobAllocations,
  });

  @override
  List<Object> get props => [
        monthlyTestRequests,
        monthlyJobAllocations,
      ];
}

class TaskStatsEntity extends BaseStatEntity {
  final int overdueTasks;
  final int todayTasks;
  final int upcomingTasks;

  const TaskStatsEntity({
    required this.overdueTasks,
    required this.todayTasks,
    required this.upcomingTasks,
  });

  @override
  List<Object> get props => [
        overdueTasks,
        todayTasks,
        upcomingTasks,
      ];
}

class UserStatsEntity extends BaseStatEntity {
  final int myJobs;
  final int myOverdueTasks;

  const UserStatsEntity({
    required this.myJobs,
    required this.myOverdueTasks,
  });

  @override
  List<Object> get props => [
        myJobs,
        myOverdueTasks,
      ];
}

class DashboardStatsEntity extends Equatable {
  final TotalStatsEntity totalStats;
  final MonthlyStatsEntity monthlyStats;
  final TaskStatsEntity taskStats;
  final UserStatsEntity userStats;

  const DashboardStatsEntity({
    required this.totalStats,
    required this.monthlyStats,
    required this.taskStats,
    required this.userStats,
  });

  @override
  List<Object> get props => [
        totalStats,
        monthlyStats,
        taskStats,
        userStats,
      ];
}
