import 'package:equatable/equatable.dart';

class JobAllocationEntity extends Equatable {
  final int id;
  final String serialNo;
  final DateTime creationDate;
  final String codeNumber;
  final String nature;
  final String quantity;
  final DateTime collectionDate;
  final DateTime submissionDate;
  final DateTime dueDate;
  final int userId;
  final int testRequestId;
  final String? reportType;
  final String designation;
  final String? remarks;
  final String nablStatus;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const JobAllocationEntity({
    required this.id,
    required this.serialNo,
    required this.creationDate,
    required this.codeNumber,
    required this.nature,
    required this.quantity,
    required this.collectionDate,
    required this.submissionDate,
    required this.dueDate,
    required this.userId,
    required this.testRequestId,
    this.reportType,
    required this.designation,
    this.remarks,
    required this.nablStatus,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        serialNo,
        creationDate,
        codeNumber,
        nature,
        quantity,
        collectionDate,
        submissionDate,
        dueDate,
        userId,
        testRequestId,
        reportType,
        designation,
        remarks,
        nablStatus,
        createdAt,
        updatedAt,
      ];

  /// TODO : pending feature
  get priority => null;

  /// TODO : pending feature
  get status => null;
}
