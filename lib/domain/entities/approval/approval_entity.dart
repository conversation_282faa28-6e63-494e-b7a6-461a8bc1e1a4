import 'package:equatable/equatable.dart';
import '../auth/user_entity.dart';
import '../completed_test/completed_test_entity.dart';

enum ApprovalStatusType { pending, approved, rejected, reassigned }

class ApprovalEntity extends Equatable {
  final int id;
  final int testId;
  final int approverId;
  final ApprovalStatusType approvalStatus;
  final DateTime? approvalDate;
  final String? approvalRemarks;
  final UserEntity? reassignedTo;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final UserEntity? approver;
  final UserEntity? reassignedUser;
  final CompletedTestEntity? test;

  const ApprovalEntity({
    required this.id,
    required this.testId,
    required this.approverId,
    required this.approvalStatus,
    this.approvalDate,
    this.approvalRemarks,
    this.reassignedTo,
    this.createdAt,
    this.updatedAt,
    this.approver,
    this.reassignedUser,
    this.test,
  });

  @override
  List<Object?> get props => [
        id,
        testId,
        approverId,
        approvalStatus,
        approvalDate,
        approvalRemarks,
        reassignedTo,
        createdAt,
        updatedAt,
        approver,
        reassignedUser,
        test,
      ];

  bool get isPending => approvalStatus == ApprovalStatusType.pending;
  bool get isApproved => approvalStatus == ApprovalStatusType.approved;
  bool get isRejected => approvalStatus == ApprovalStatusType.rejected;
  bool get isReassigned => approvalStatus == ApprovalStatusType.reassigned;
  
  bool get hasRemarks => approvalRemarks != null && approvalRemarks!.isNotEmpty;
  bool get isReassignedToSomeone => reassignedTo != null;
  
  Duration? get approvalDuration {
    if (approvalDate != null && createdAt != null) {
      return approvalDate!.difference(createdAt!);
    }
    return null;
  }
}

class ApprovalRequestEntity extends Equatable {
  final ApprovalStatusType approvalStatus;
  final String? approvalRemarks;
  final UserEntity? reassignedTo;

  const ApprovalRequestEntity({
    required this.approvalStatus,
    this.approvalRemarks,
    this.reassignedTo,
  });

  @override
  List<Object?> get props => [approvalStatus, approvalRemarks, reassignedTo];
}

class BulkApprovalRequestEntity extends Equatable {
  final List<int> testIds;
  final ApprovalStatusType approvalStatus;
  final String? approvalRemarks;

  const BulkApprovalRequestEntity({
    required this.testIds,
    required this.approvalStatus,
    this.approvalRemarks,
  });

  @override
  List<Object?> get props => [testIds, approvalStatus, approvalRemarks];
  
  bool get isApproving => approvalStatus == ApprovalStatusType.approved;
  bool get isRejecting => approvalStatus == ApprovalStatusType.rejected;
  int get testCount => testIds.length;
}
