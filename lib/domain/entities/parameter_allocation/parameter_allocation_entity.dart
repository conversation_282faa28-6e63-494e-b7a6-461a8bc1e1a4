import 'package:equatable/equatable.dart';
import '../test_request/sample_entity.dart';
import '../auth/user_entity.dart';

enum AllocationStatus { pending, inProgress, completed, rejected }

class ParameterAllocationEntity extends Equatable {
  final int id;
  final int jobDetailId;
  final int sampleId;
  final int analystId;
  final bool isRetest;
  final bool isBlind;
  final bool isReplicate;
  final bool isSpiked;
  final String? spikedResult;
  final int? originalAllocationId;
  final AllocationStatus status;
  final String? rejectionReason;
  final DateTime? rejectedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final SampleEntity? sample;
  final UserEntity? analyst;
  final UserEntity? approvedBy;

  const ParameterAllocationEntity({
    required this.id,
    required this.jobDetailId,
    required this.sampleId,
    required this.analystId,
    this.isRetest = false,
    this.isBlind = false,
    this.isReplicate = false,
    this.isSpiked = false,
    this.spikedResult,
    this.originalAllocationId,
    this.status = AllocationStatus.pending,
    this.rejectionReason,
    this.rejectedAt,
    this.createdAt,
    this.updatedAt,
    this.sample,
    this.analyst,
    this.approvedBy,
  });

  @override
  List<Object?> get props => [
        id,
        jobDetailId,
        sampleId,
        analystId,
        isRetest,
        isBlind,
        isReplicate,
        isSpiked,
        spikedResult,
        originalAllocationId,
        status,
        rejectionReason,
        rejectedAt,
        createdAt,
        updatedAt,
        sample,
        analyst,
        approvedBy,
      ];

  bool get isPending => status == AllocationStatus.pending;
  bool get isInProgress => status == AllocationStatus.inProgress;
  bool get isCompleted => status == AllocationStatus.completed;
  bool get isRejected => status == AllocationStatus.rejected;
  
  bool get isQualityControlTest => isRetest || isBlind || isReplicate;
  bool get hasSpecialRequirements => isSpiked || isQualityControlTest;
}
