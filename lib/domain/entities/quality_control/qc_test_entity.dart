import 'package:equatable/equatable.dart';

enum QCTestType { retest, blind, replicate, standard }

class QCTestEntity extends Equatable {
  final int id;
  final int jobDetailId;
  final int testRequestSampleId;
  final String result;
  final DateTime analysisStartDate;
  final DateTime analysisCompletionDate;
  final DateTime analysisSubmissionDate;
  final bool isRetest;
  final bool isBlind;
  final bool isReplicate;
  final int? originalTestId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const QCTestEntity({
    required this.id,
    required this.jobDetailId,
    required this.testRequestSampleId,
    required this.result,
    required this.analysisStartDate,
    required this.analysisCompletionDate,
    required this.analysisSubmissionDate,
    this.isRetest = false,
    this.isBlind = false,
    this.isReplicate = false,
    this.originalTestId,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        jobDetailId,
        testRequestSampleId,
        result,
        analysisStartDate,
        analysisCompletionDate,
        analysisSubmissionDate,
        isRetest,
        isBlind,
        isReplicate,
        originalTestId,
        createdAt,
        updatedAt,
      ];

  QCTestType get testType {
    if (isRetest) return QCTestType.retest;
    if (isBlind) return QCTestType.blind;
    if (isReplicate) return QCTestType.replicate;
    return QCTestType.standard;
  }

  bool get isQualityControlTest => isRetest || isBlind || isReplicate;
  
  Duration get analysisDuration => analysisCompletionDate.difference(analysisStartDate);
  
  bool get isCompletedOnTime => analysisSubmissionDate.isBefore(
    analysisCompletionDate.add(const Duration(hours: 24))
  );
}

class QCTestRequestEntity extends Equatable {
  final int jobDetailId;
  final int testRequestSampleId;
  final String result;
  final DateTime analysisStartDate;
  final DateTime analysisCompletionDate;
  final DateTime analysisSubmissionDate;
  final bool isRetest;
  final bool isBlind;
  final bool isReplicate;
  final int? originalTestId;

  const QCTestRequestEntity({
    required this.jobDetailId,
    required this.testRequestSampleId,
    required this.result,
    required this.analysisStartDate,
    required this.analysisCompletionDate,
    required this.analysisSubmissionDate,
    this.isRetest = false,
    this.isBlind = false,
    this.isReplicate = false,
    this.originalTestId,
  });

  @override
  List<Object?> get props => [
        jobDetailId,
        testRequestSampleId,
        result,
        analysisStartDate,
        analysisCompletionDate,
        analysisSubmissionDate,
        isRetest,
        isBlind,
        isReplicate,
        originalTestId,
      ];
}

class ApproveTestRequestEntity extends Equatable {
  final String approvalStatus;
  final String? approvalRemarks;
  final int? reassignedTo;

  const ApproveTestRequestEntity({
    required this.approvalStatus,
    this.approvalRemarks,
    this.reassignedTo,
  });

  @override
  List<Object?> get props => [approvalStatus, approvalRemarks, reassignedTo];
  
  bool get isApproved => approvalStatus.toLowerCase() == 'approved';
  bool get isRejected => approvalStatus.toLowerCase() == 'rejected';
}
