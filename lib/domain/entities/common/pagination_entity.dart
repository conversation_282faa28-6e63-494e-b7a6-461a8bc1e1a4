import 'package:equatable/equatable.dart';

class PaginationEntity extends Equatable {
  final int currentPage;
  final int totalPages;
  final int perPage;
  final int total;
  final bool hasMore;

  const PaginationEntity({
    required this.currentPage,
    required this.totalPages,
    required this.perPage,
    required this.total,
    required this.hasMore,
  });

  @override
  List<Object> get props => [
        currentPage,
        totalPages,
        perPage,
        total,
        hasMore,
      ];
}

class PaginatedResult<T> extends Equatable {
  final List<T> items;
  final PaginationEntity pagination;

  const PaginatedResult({
    required this.items,
    required this.pagination,
  });

  @override
  List<Object> get props => [items, pagination];

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
  int get length => items.length;
  bool get hasNextPage => pagination.hasMore;
  bool get hasPreviousPage => pagination.currentPage > 1;
}
