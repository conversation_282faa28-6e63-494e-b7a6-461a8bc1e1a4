import 'package:equatable/equatable.dart';

class ParameterEntity extends Equatable {
  final int id;
  final String name;
  final String? type;
  final int? status;
  final String? requirement;
  final String? permissibleLimit;
  final String? protocolUsed;
  final String? units;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const ParameterEntity({
    required this.id,
    required this.name,
    this.type,
    this.status,
    this.requirement,
    this.permissibleLimit,
    this.protocolUsed,
    this.units,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        status,
        requirement,
        permissibleLimit,
        protocolUsed,
        units,
        createdAt,
        updatedAt,
      ];
}
