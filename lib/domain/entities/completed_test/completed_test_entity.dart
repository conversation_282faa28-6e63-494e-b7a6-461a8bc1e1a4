import 'package:equatable/equatable.dart';
import '../job_allocation/job_allocation_entity.dart';
import '../test_request/sample_entity.dart';
import '../auth/user_entity.dart';

enum ApprovalStatus { pending, approved, rejected }

class CompletedTestEntity extends Equatable {
  final int id;
  final int jobDetailId;
  final int testRequestSampleId;
  final int userId;
  final String result;
  final DateTime? analysisStartDate;
  final DateTime? analysisCompletionDate;
  final DateTime? analysisSubmissionDate;
  final bool isRetest;
  final bool isBlind;
  final bool isReplicate;
  final int? originalTestId;
  final String? rawWater;
  final String? filteredWater;
  final String? treatedWater;
  final String? location1;
  final String? location2;
  final String? location3;
  final String? location4;
  final String? location5;
  final String? location6;
  final ApprovalStatus approvalStatus;
  final int? approvedById;
  final DateTime? approvalDate;
  final String? approvalRemarks;
  final int? reassignedTo;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final JobAllocationEntity? jobDetail;
  final SampleEntity? testRequestSample;
  final UserEntity? admin;
  final UserEntity? approvedBy;

  const CompletedTestEntity({
    required this.id,
    required this.jobDetailId,
    required this.testRequestSampleId,
    required this.userId,
    required this.result,
    this.analysisStartDate,
    this.analysisCompletionDate,
    this.analysisSubmissionDate,
    this.isRetest = false,
    this.isBlind = false,
    this.isReplicate = false,
    this.originalTestId,
    this.rawWater,
    this.filteredWater,
    this.treatedWater,
    this.location1,
    this.location2,
    this.location3,
    this.location4,
    this.location5,
    this.location6,
    this.approvalStatus = ApprovalStatus.pending,
    this.approvedById,
    this.approvalDate,
    this.approvalRemarks,
    this.reassignedTo,
    this.createdAt,
    this.updatedAt,
    this.jobDetail,
    this.testRequestSample,
    this.admin,
    this.approvedBy,
  });

  @override
  List<Object?> get props => [
        id,
        jobDetailId,
        testRequestSampleId,
        userId,
        result,
        analysisStartDate,
        analysisCompletionDate,
        analysisSubmissionDate,
        isRetest,
        isBlind,
        isReplicate,
        originalTestId,
        rawWater,
        filteredWater,
        treatedWater,
        location1,
        location2,
        location3,
        location4,
        location5,
        location6,
        approvalStatus,
        approvedById,
        approvalDate,
        approvalRemarks,
        reassignedTo,
        createdAt,
        updatedAt,
        jobDetail,
        testRequestSample,
        admin,
        approvedBy,
      ];

  bool get isPending => approvalStatus == ApprovalStatus.pending;
  bool get isApproved => approvalStatus == ApprovalStatus.approved;
  bool get isRejected => approvalStatus == ApprovalStatus.rejected;
  
  bool get hasLocationData => [location1, location2, location3, location4, location5, location6]
      .any((location) => location != null && location.isNotEmpty);
      
  bool get hasWaterTypeData => [rawWater, filteredWater, treatedWater]
      .any((water) => water != null && water.isNotEmpty);
}
