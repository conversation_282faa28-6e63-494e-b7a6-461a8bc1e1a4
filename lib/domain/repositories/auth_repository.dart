import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/auth/login_entity.dart';
import '../entities/auth/user_entity.dart';

abstract class AuthRepository {
  Future<Either<Failure, LoginEntity>> login({
    required String username,
    required String password,
  });
  
  Future<Either<Failure, UserEntity>> getProfile();
  
  Future<Either<Failure, UserEntity>> updateProfile({
    required String name,
  });
  
  Future<Either<Failure, void>> logout();
}
