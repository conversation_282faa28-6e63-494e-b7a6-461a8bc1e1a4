import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/completed_test/completed_test_entity.dart';
import '../entities/common/pagination_entity.dart';

abstract class CompletedTestRepository {
  /// Get completed tests with pagination
  Future<Either<Failure, CompletedTestsResult>> getCompletedTests({
    int page = 1,
    int perPage = 15,
  });

  /// Get completed test details by ID
  Future<Either<Failure, CompletedTestEntity>> getCompletedTestById(int id);

  /// Export completed tests to Excel (from Postman collection)
  Future<Either<Failure, String>> exportToExcel();
}

class CompletedTestsResult {
  final List<CompletedTestEntity> completedTests;
  final PaginationEntity pagination;
  final CompletedTestsSummary? summary;

  const CompletedTestsResult({
    required this.completedTests,
    required this.pagination,
    this.summary,
  });
}

class CompletedTestsSummary {
  final int totalCompleted;
  final int pendingApproval;
  final int approved;
  final int rejected;

  const CompletedTestsSummary({
    required this.totalCompleted,
    required this.pendingApproval,
    required this.approved,
    required this.rejected,
  });
}
