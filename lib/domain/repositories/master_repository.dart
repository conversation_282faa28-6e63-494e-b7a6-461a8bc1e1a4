import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/master/parameter_entity.dart';
import '../entities/master/customer_entity.dart';

abstract class MasterRepository {
  Future<Either<Failure, List<ParameterEntity>>> getParameters();
  
  Future<Either<Failure, List<CustomerEntity>>> getCustomers({
    String? search,
  });
  
  Future<Either<Failure, CustomerEntity>> addCustomer(
    CustomerEntity customer,
  );
}
