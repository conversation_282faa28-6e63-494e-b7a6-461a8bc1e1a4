import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/test_request/test_request_entity.dart';
import '../entities/test_request/sample_entity.dart';
import '../entities/common/pagination_entity.dart';

abstract class TestRequestRepository {
  Future<Either<Failure, PaginatedResult<TestRequestEntity>>> getTestRequests({
    int page = 1,
    int perPage = 15,
    String? search,
  });
  
  Future<Either<Failure, TestRequestEntity>> getTestRequestById(int id);
  
  Future<Either<Failure, TestRequestEntity>> createTestRequest(
    TestRequestEntity testRequest,
  );
  
  Future<Either<Failure, TestRequestEntity>> updateTestRequest(
    int id,
    TestRequestEntity testRequest,
  );
  
  Future<Either<Failure, void>> deleteTestRequest(int id);
  
  Future<Either<Failure, List<SampleEntity>>> getSamples(int testRequestId);
  
  Future<Either<Failure, SampleEntity>> addSample(
    int testRequestId,
    SampleEntity sample,
  );
}
