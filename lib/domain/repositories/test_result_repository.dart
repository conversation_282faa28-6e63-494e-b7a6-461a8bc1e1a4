import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/test_result/test_result_entity.dart';
import '../entities/common/pagination_entity.dart';

abstract class TestResultRepository {
  /// Get test results with pagination and filters
  Future<Either<Failure, PaginatedResult<TestResultEntity>>> getTestResults({
    int page = 1,
    int perPage = 15,
    int? parameterId,
    int? sampleId,
    int? analystId,
    TestResultStatus? status,
  });

  /// Get test result details by ID
  Future<Either<Failure, TestResultEntity>> getTestResultById(int id);

  /// Get test results for a specific parameter allocation
  Future<Either<Failure, List<TestResultEntity>>> getTestResultsByAllocation(
    int parameterAllocationId,
  );

  /// Create a new test result
  Future<Either<Failure, TestResultEntity>> createTestResult(
    CreateTestResultRequestEntity request,
  );

  /// Update an existing test result
  Future<Either<Failure, TestResultEntity>> updateTestResult(
    int id,
    UpdateTestResultRequestEntity request,
  );

  /// Delete a test result
  Future<Either<Failure, void>> deleteTestResult(int id);

  /// Get test results for analyst dashboard
  Future<Either<Failure, List<TestResultEntity>>> getMyTestResults(
    int analystId,
  );

  /// Get test results requiring attention (out of limits, rejected)
  Future<Either<Failure, List<TestResultEntity>>> getTestResultsRequiringAttention();
}
