import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/test_result/test_result_entity.dart';
import '../entities/common/pagination_entity.dart';

abstract class TestResultRepository {
  /// Get test results with pagination and filters
  Future<Either<Failure, PaginatedResult<TestResultEntity>>> getTestResults({
    int page = 1,
    int perPage = 15,
    int? parameterId,
    int? sampleId,
    int? analystId,
    TestResultStatus? status,
  });

  /// Get test result details by ID
  Future<Either<Failure, TestResultEntity>> getTestResultById(int id);

  /// Add a new test result (from Postman collection)
  Future<Either<Failure, TestResultEntity>> addTestResult(
    Map<String, dynamic> request,
  );

  /// Update an existing test result
  Future<Either<Failure, TestResultEntity>> updateTestResult(
    int id,
    Map<String, dynamic> request,
  );

  /// Approve test result (from Postman collection)
  Future<Either<Failure, TestResultEntity>> approveTestResult(
    int id,
    Map<String, dynamic> request,
  );

  /// Reject test result (from Postman collection)
  Future<Either<Failure, TestResultEntity>> rejectTestResult(
    int id,
    Map<String, dynamic> request,
  );

  /// Get active tests (from Postman collection)
  Future<Either<Failure, List<TestResultEntity>>> getActiveTests();

  /// Get my tests for analyst (from Postman collection)
  Future<Either<Failure, List<TestResultEntity>>> getMyTests();

  /// Accept test assignment (from Postman collection)
  Future<Either<Failure, TestResultEntity>> acceptTest(int id);

  /// Reject test assignment (from Postman collection)
  Future<Either<Failure, TestResultEntity>> rejectTest(
    int id,
    Map<String, dynamic> request,
  );

  /// Add result to test (from Postman collection)
  Future<Either<Failure, TestResultEntity>> addResult(
    int id,
    Map<String, dynamic> request,
  );

  /// Reassign test to another analyst (from Postman collection)
  Future<Either<Failure, TestResultEntity>> reassignTest(
    int id,
    Map<String, dynamic> request,
  );
}
