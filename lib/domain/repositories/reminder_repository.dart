import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';

abstract class ReminderRepository {
  /// Get reminders with pagination (from Postman collection)
  Future<Either<Failure, Map<String, dynamic>>> getReminders({
    int page = 1,
    int perPage = 15,
  });

  /// Get reminder details by ID
  Future<Either<Failure, Map<String, dynamic>>> getReminderById(int id);

  /// Create a new reminder (from Postman collection)
  Future<Either<Failure, Map<String, dynamic>>> createReminder(
    Map<String, dynamic> request,
  );

  /// Get active reminders (from Postman collection)
  Future<Either<Failure, List<Map<String, dynamic>>>> getActiveReminders();
}
