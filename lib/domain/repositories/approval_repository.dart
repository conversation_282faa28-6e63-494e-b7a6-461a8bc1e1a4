import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/approval/approval_entity.dart';
import '../entities/common/pagination_entity.dart';

abstract class ApprovalRepository {
  /// Get all approvals with pagination (from Postman collection)
  Future<Either<Failure, PaginatedResult<ApprovalEntity>>> getApprovals({
    int page = 1,
    int perPage = 15,
  });

  /// Get pending approvals with pagination (from Postman collection)
  Future<Either<Failure, PaginatedResult<ApprovalEntity>>> getPendingApprovals({
    int page = 1,
    int perPage = 15,
  });

  /// Get approved approvals with pagination (from Postman collection)
  Future<Either<Failure, PaginatedResult<ApprovalEntity>>> getApprovedApprovals({
    int page = 1,
    int perPage = 15,
  });

  /// Get rejected approvals with pagination (from Postman collection)
  Future<Either<Failure, PaginatedResult<ApprovalEntity>>> getRejectedApprovals({
    int page = 1,
    int perPage = 15,
  });

  /// Get approval details by ID
  Future<Either<Failure, ApprovalEntity>> getApprovalById(int id);

  /// Process approval (from Postman collection)
  Future<Either<Failure, ApprovalEntity>> processApproval(
    int id,
      ApprovalRequestEntity request,
  );

  /// Reassign approval to another approver (from Postman collection)
  Future<Either<Failure, ApprovalEntity>> reassignApproval(
    int id,
    Map<String, dynamic> request,
  );

  /// Reject approval (from Postman collection)
  Future<Either<Failure, ApprovalEntity>> rejectApproval(
    int id,
    Map<String, dynamic> request,
  );
}
