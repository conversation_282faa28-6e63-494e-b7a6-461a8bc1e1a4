import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/approval/approval_entity.dart';
import '../entities/common/pagination_entity.dart';

abstract class ApprovalRepository {
  /// Get pending approvals with pagination
  Future<Either<Failure, PaginatedResult<ApprovalEntity>>> getPendingApprovals({
    int page = 1,
    int perPage = 15,
  });

  /// Get approval history with pagination
  Future<Either<Failure, PaginatedResult<ApprovalEntity>>> getApprovalHistory({
    int page = 1,
    int perPage = 15,
  });

  /// Get approval details by ID
  Future<Either<Failure, ApprovalEntity>> getApprovalById(int id);

  /// Approve or reject a single test
  Future<Either<Failure, ApprovalEntity>> processApproval(
    int testId,
    ApprovalRequestEntity request,
  );

  /// Bulk approve or reject multiple tests
  Future<Either<Failure, BulkApprovalResult>> processBulkApproval(
    BulkApprovalRequestEntity request,
  );

  /// Reassign test to another approver
  Future<Either<Failure, ApprovalEntity>> reassignTest(
    int testId,
    int newApproverId,
    String? remarks,
  );
}

class BulkApprovalResult {
  final int approvedCount;
  final int failedCount;
  final List<int>? failedTests;

  const BulkApprovalResult({
    required this.approvedCount,
    required this.failedCount,
    this.failedTests,
  });
}
