import 'package:dartz/dartz.dart';
import 'package:lims_app_flutter/domain/entities/common/pagination_entity.dart';
import '../../core/error/failures.dart';
import '../entities/job_allocation/job_allocation_entity.dart';

abstract class JobAllocationRepository {
  Future<Either<Failure, PaginatedResult<JobAllocationEntity>>> getJobAllocations({
    int page = 1,
    int perPage = 15,
    String? search,
  });
  
  Future<Either<Failure, JobAllocationEntity>> getJobAllocationById(int id);
  
  Future<Either<Failure, JobAllocationEntity>> createJobAllocation(
    JobAllocationEntity jobAllocation,
  );
  
  Future<Either<Failure, JobAllocationEntity>> updateJobAllocation(
    int id,
    JobAllocationEntity jobAllocation,
  );
  
  Future<Either<Failure, void>> deleteJobAllocation(int id);
}
