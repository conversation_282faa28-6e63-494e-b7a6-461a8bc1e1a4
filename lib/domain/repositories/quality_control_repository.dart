import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/completed_test/completed_test_entity.dart';
import '../entities/quality_control/qc_test_entity.dart';

abstract class QualityControlRepository {
  /// Get quality control tests
  Future<Either<Failure, QCTestsResult>> getQCTests({
    int page = 1,
    int perPage = 15,
  });

  /// Add retest (from Postman collection)
  Future<Either<Failure, CompletedTestEntity>> addRetest(
    Map<String, dynamic> request,
  );

  /// Add blind test (from Postman collection)
  Future<Either<Failure, CompletedTestEntity>> addBlind(
    Map<String, dynamic> request,
  );

  /// Add replicate test (from Postman collection)
  Future<Either<Failure, CompletedTestEntity>> addReplicate(
    Map<String, dynamic> request,
  );

  /// View parameter details (from Postman collection)
  Future<Either<Failure, ParameterEntity>> viewParameter(int id);
}

class QCTestsResult {
  final int currentPage;
  final List<CompletedTestEntity> tests;

  const QCTestsResult({
    required this.currentPage,
    required this.tests,
  });
}
