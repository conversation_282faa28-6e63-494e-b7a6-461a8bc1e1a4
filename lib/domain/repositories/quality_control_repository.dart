import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/completed_test/completed_test_entity.dart';
import '../entities/quality_control/qc_test_entity.dart';

abstract class QualityControlRepository {
  /// Get quality control tests
  Future<Either<Failure, QCTestsResult>> getQCTests({
    int page = 1,
    int perPage = 15,
  });

  /// Create a new quality control test
  Future<Either<Failure, CompletedTestEntity>> createQCTest(
    QCTestRequestEntity request,
  );

  /// Approve or reject a test
  Future<Either<Failure, CompletedTestEntity>> approveTest(
    int testId,
    ApproveTestRequestEntity request,
  );
}

class QCTestsResult {
  final int currentPage;
  final List<CompletedTestEntity> tests;

  const QCTestsResult({
    required this.currentPage,
    required this.tests,
  });
}
