import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/analyst/analyst_test_entity.dart';
import '../entities/completed_test/completed_test_entity.dart';

abstract class AnalystRepository {
  /// Get tests assigned to the current analyst
  Future<Either<Failure, List<AnalystTestEntity>>> getMyTests();

  /// Submit test result
  Future<Either<Failure, CompletedTestEntity>> submitTestResult(
    int testId,
    SubmitTestResultRequestEntity request,
  );

  /// Reject a test
  Future<Either<Failure, void>> rejectTest(
    int testId,
    RejectTestRequestEntity request,
  );
}
