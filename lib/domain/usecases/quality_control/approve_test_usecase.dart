import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/completed_test/completed_test_entity.dart';
import '../../entities/quality_control/qc_test_entity.dart';
import '../../repositories/quality_control_repository.dart';

class ApproveTestUseCase implements UseCase<CompletedTestEntity, ApproveTestParams> {
  final QualityControlRepository repository;

  ApproveTestUseCase(this.repository);

  @override
  Future<Either<Failure, CompletedTestEntity>> call(ApproveTestParams params) async {
    return await repository.approveTest(params.testId, params.request);
  }
}

class ApproveTestParams extends Equatable {
  final int testId;
  final ApproveTestRequestEntity request;

  const ApproveTestParams({
    required this.testId,
    required this.request,
  });

  @override
  List<Object> get props => [testId, request];
}
