import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/quality_control_repository.dart';

class GetQCTestsUseCase implements UseCase<QCTestsResult, GetQCTestsParams> {
  final QualityControlRepository repository;

  GetQCTestsUseCase(this.repository);

  @override
  Future<Either<Failure, QCTestsResult>> call(GetQCTestsParams params) async {
    return await repository.getQCTests(
      page: params.page,
      perPage: params.perPage,
    );
  }
}

class GetQCTestsParams extends Equatable {
  final int page;
  final int perPage;

  const GetQCTestsParams({
    this.page = 1,
    this.perPage = 15,
  });

  @override
  List<Object> get props => [page, perPage];
}
