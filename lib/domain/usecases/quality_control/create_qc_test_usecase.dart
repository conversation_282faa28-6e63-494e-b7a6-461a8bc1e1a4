import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/completed_test/completed_test_entity.dart';
import '../../entities/quality_control/qc_test_entity.dart';
import '../../repositories/quality_control_repository.dart';

class CreateQCTestUseCase implements UseCase<CompletedTestEntity, QCTestRequestEntity> {
  final QualityControlRepository repository;

  CreateQCTestUseCase(this.repository);

  @override
  Future<Either<Failure, CompletedTestEntity>> call(QCTestRequestEntity params) async {
    return await repository.createQCTest(params);
  }
}
