import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/analyst/analyst_test_entity.dart';
import '../../repositories/analyst_repository.dart';

class RejectTestUseCase implements UseCase<void, RejectTestParams> {
  final AnalystRepository repository;

  RejectTestUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(RejectTestParams params) async {
    return await repository.rejectTest(params.testId, params.request);
  }
}

class RejectTestParams extends Equatable {
  final int testId;
  final RejectTestRequestEntity request;

  const RejectTestParams({
    required this.testId,
    required this.request,
  });

  @override
  List<Object> get props => [testId, request];
}
