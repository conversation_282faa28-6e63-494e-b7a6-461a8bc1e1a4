import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/analyst/analyst_test_entity.dart';
import '../../entities/completed_test/completed_test_entity.dart';
import '../../repositories/analyst_repository.dart';

class SubmitTestResultUseCase implements UseCase<CompletedTestEntity, SubmitTestResultParams> {
  final AnalystRepository repository;

  SubmitTestResultUseCase(this.repository);

  @override
  Future<Either<Failure, CompletedTestEntity>> call(SubmitTestResultParams params) async {
    return await repository.submitTestResult(params.testId, params.request);
  }
}

class SubmitTestResultParams extends Equatable {
  final int testId;
  final SubmitTestResultRequestEntity request;

  const SubmitTestResultParams({
    required this.testId,
    required this.request,
  });

  @override
  List<Object> get props => [testId, request];
}
