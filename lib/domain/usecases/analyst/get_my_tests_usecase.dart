import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/analyst/analyst_test_entity.dart';
import '../../repositories/analyst_repository.dart';

class GetMyTestsUseCase implements UseCase<List<AnalystTestEntity>, NoParams> {
  final AnalystRepository repository;

  GetMyTestsUseCase(this.repository);

  @override
  Future<Either<Failure, List<AnalystTestEntity>>> call(NoParams params) async {
    return await repository.getMyTests();
  }
}
