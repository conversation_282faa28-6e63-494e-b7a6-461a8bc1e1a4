import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/auth/user_entity.dart';
import '../../repositories/auth_repository.dart';

/// Composite use case that performs login and immediately fetches user profile
/// This is useful for complete authentication flow
class LoginAndGetProfileUseCase {
  final AuthRepository repository;

  LoginAndGetProfileUseCase(this.repository);

  Future<Either<Failure, LoginAndGetProfileResult>> call(LoginAndGetProfileParams params) async {
    // First, perform login
    final loginResult = await repository.login(
      username: params.username,
      password: params.password,
    );

    return loginResult.fold(
      (failure) => Left(failure),
      (loginEntity) async {
        // If login successful, get profile
        final profileResult = await repository.getProfile();
        
        return profileResult.fold(
          (failure) => Left(failure),
          (userEntity) => Right(LoginAndGetProfileResult(
            token: loginEntity.token,
            user: userEntity,
          )),
        );
      },
    );
  }
}

class LoginAndGetProfileParams extends Equatable {
  final String username;
  final String password;

  const LoginAndGetProfileParams({
    required this.username,
    required this.password,
  });

  @override
  List<Object> get props => [username, password];
}

class LoginAndGetProfileResult extends Equatable {
  final String token;
  final UserEntity user;

  const LoginAndGetProfileResult({
    required this.token,
    required this.user,
  });

  @override
  List<Object> get props => [token, user];
}
