import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/job_allocation/job_allocation_entity.dart';
import '../../repositories/job_allocation_repository.dart';

class GetJobAllocationByIdUseCase {
  final JobAllocationRepository repository;

  GetJobAllocationByIdUseCase(this.repository);

  Future<Either<Failure, JobAllocationEntity>> call(GetJobAllocationByIdParams params) async {
    return await repository.getJobAllocationById(params.id);
  }
}

class GetJobAllocationByIdParams extends Equatable {
  final int id;

  const GetJobAllocationByIdParams({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}
