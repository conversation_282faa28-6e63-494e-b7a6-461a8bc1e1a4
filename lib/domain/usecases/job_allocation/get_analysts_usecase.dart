import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../core/usecases/usecase.dart';
import '../../repositories/job_allocation_repository.dart';

class GetAnalystsUseCase implements UseCase<List<Map<String, dynamic>>, NoParams> {
  final JobAllocationRepository repository;

  GetAnalystsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> call(NoParams params) async {
    return await repository.getAnalysts();
  }
}
