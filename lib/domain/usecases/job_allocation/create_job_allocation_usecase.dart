import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/job_allocation/job_allocation_entity.dart';
import '../../repositories/job_allocation_repository.dart';

class CreateJobAllocationUseCase {
  final JobAllocationRepository repository;

  CreateJobAllocationUseCase(this.repository);

  Future<Either<Failure, JobAllocationEntity>> call(CreateJobAllocationParams params) async {
    return await repository.createJobAllocation(params.jobAllocation);
  }
}

class CreateJobAllocationParams extends Equatable {
  final JobAllocationEntity jobAllocation;

  const CreateJobAllocationParams({
    required this.jobAllocation,
  });

  @override
  List<Object> get props => [jobAllocation];
}
