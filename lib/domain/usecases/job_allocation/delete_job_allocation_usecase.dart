import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../repositories/job_allocation_repository.dart';

class DeleteJobAllocationUseCase {
  final JobAllocationRepository repository;

  DeleteJobAllocationUseCase(this.repository);

  Future<Either<Failure, void>> call(DeleteJobAllocationParams params) async {
    return await repository.deleteJobAllocation(params.id);
  }
}

class DeleteJobAllocationParams extends Equatable {
  final int id;

  const DeleteJobAllocationParams({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}
