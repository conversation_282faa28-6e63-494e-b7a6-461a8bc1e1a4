import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/job_allocation/job_allocation_entity.dart';
import '../../repositories/job_allocation_repository.dart';

class UpdateJobAllocationUseCase {
  final JobAllocationRepository repository;

  UpdateJobAllocationUseCase(this.repository);

  Future<Either<Failure, JobAllocationEntity>> call(UpdateJobAllocationParams params) async {
    return await repository.updateJobAllocation(params.id, params.jobAllocation);
  }
}

class UpdateJobAllocationParams extends Equatable {
  final int id;
  final JobAllocationEntity jobAllocation;

  const UpdateJobAllocationParams({
    required this.id,
    required this.jobAllocation,
  });

  @override
  List<Object> get props => [id, jobAllocation];
}
