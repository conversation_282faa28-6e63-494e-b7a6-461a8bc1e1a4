import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/test_request/test_request_entity.dart';
import '../../repositories/test_request_repository.dart';

class CreateTestRequestUseCase {
  final TestRequestRepository repository;

  CreateTestRequestUseCase(this.repository);

  Future<Either<Failure, TestRequestEntity>> call(CreateTestRequestParams params) async {
    return await repository.createTestRequest(params.testRequest);
  }
}

class CreateTestRequestParams extends Equatable {
  final TestRequestEntity testRequest;

  const CreateTestRequestParams({
    required this.testRequest,
  });

  @override
  List<Object> get props => [testRequest];
}
