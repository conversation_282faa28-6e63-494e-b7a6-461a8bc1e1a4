import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/test_request/sample_entity.dart';
import '../../repositories/test_request_repository.dart';

class AddSampleUseCase {
  final TestRequestRepository repository;

  AddSampleUseCase(this.repository);

  Future<Either<Failure, SampleEntity>> call(AddSampleParams params) async {
    return await repository.addSample(params.testRequestId, params.sample);
  }
}

class AddSampleParams extends Equatable {
  final int testRequestId;
  final SampleEntity sample;

  const AddSampleParams({
    required this.testRequestId,
    required this.sample,
  });

  @override
  List<Object> get props => [testRequestId, sample];
}
