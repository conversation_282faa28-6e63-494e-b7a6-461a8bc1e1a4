import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/test_request/test_request_entity.dart';
import '../../entities/test_request/sample_entity.dart';
import '../../repositories/test_request_repository.dart';

/// Composite use case that gets a test request and its samples
/// This provides complete test request details in one operation
class GetTestRequestWithSamplesUseCase {
  final TestRequestRepository repository;

  GetTestRequestWithSamplesUseCase(this.repository);

  Future<Either<Failure, GetTestRequestWithSamplesResult>> call(
    GetTestRequestWithSamplesParams params,
  ) async {
    // First, get the test request
    final testRequestResult = await repository.getTestRequestById(params.id);

    return testRequestResult.fold(
      (failure) => Left(failure),
      (testRequest) async {
        // Then get the samples for this test request
        final samplesResult = await repository.getSamples(params.id);

        return samplesResult.fold(
          (failure) => Left(failure),
          (samples) => Right(GetTestRequestWithSamplesResult(
            testRequest: testRequest,
            samples: samples,
          )),
        );
      },
    );
  }
}

class GetTestRequestWithSamplesParams extends Equatable {
  final int id;

  const GetTestRequestWithSamplesParams({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}

class GetTestRequestWithSamplesResult extends Equatable {
  final TestRequestEntity testRequest;
  final List<SampleEntity> samples;

  const GetTestRequestWithSamplesResult({
    required this.testRequest,
    required this.samples,
  });

  @override
  List<Object> get props => [testRequest, samples];
}
