import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../repositories/test_request_repository.dart';

class DeleteTestRequestUseCase {
  final TestRequestRepository repository;

  DeleteTestRequestUseCase(this.repository);

  Future<Either<Failure, void>> call(DeleteTestRequestParams params) async {
    return await repository.deleteTestRequest(params.id);
  }
}

class DeleteTestRequestParams extends Equatable {
  final int id;

  const DeleteTestRequestParams({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}
