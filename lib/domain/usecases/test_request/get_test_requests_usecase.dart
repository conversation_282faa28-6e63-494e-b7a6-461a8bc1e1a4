import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/test_request/test_request_entity.dart';
import '../../entities/common/pagination_entity.dart';
import '../../repositories/test_request_repository.dart';

class GetTestRequestsUseCase {
  final TestRequestRepository repository;

  GetTestRequestsUseCase(this.repository);

  Future<Either<Failure, PaginatedResult<TestRequestEntity>>> call(GetTestRequestsParams params) async {
    return await repository.getTestRequests(
      page: params.page,
      perPage: params.perPage,
      search: params.search,
    );
  }
}

class GetTestRequestsParams extends Equatable {
  final int page;
  final int perPage;
  final String? search;

  const GetTestRequestsParams({
    this.page = 1,
    this.perPage = 15,
    this.search,
  });

  @override
  List<Object?> get props => [page, perPage, search];
}
