import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/test_request/test_request_entity.dart';
import '../../repositories/test_request_repository.dart';

class GetTestRequestByIdUseCase {
  final TestRequestRepository repository;

  GetTestRequestByIdUseCase(this.repository);

  Future<Either<Failure, TestRequestEntity>> call(GetTestRequestByIdParams params) async {
    return await repository.getTestRequestById(params.id);
  }
}

class GetTestRequestByIdParams extends Equatable {
  final int id;

  const GetTestRequestByIdParams({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}
