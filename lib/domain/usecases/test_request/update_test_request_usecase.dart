import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/test_request/test_request_entity.dart';
import '../../repositories/test_request_repository.dart';

class UpdateTestRequestUseCase {
  final TestRequestRepository repository;

  UpdateTestRequestUseCase(this.repository);

  Future<Either<Failure, TestRequestEntity>> call(UpdateTestRequestParams params) async {
    return await repository.updateTestRequest(params.id, params.testRequest);
  }
}

class UpdateTestRequestParams extends Equatable {
  final int id;
  final TestRequestEntity testRequest;

  const UpdateTestRequestParams({
    required this.id,
    required this.testRequest,
  });

  @override
  List<Object> get props => [id, testRequest];
}
