import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/test_request/test_request_entity.dart';
import '../../entities/test_request/sample_entity.dart';
import '../../repositories/test_request_repository.dart';

/// Composite use case that creates a test request and then adds samples to it
/// This handles the complete test request creation workflow
class CreateTestRequestWithSamplesUseCase {
  final TestRequestRepository repository;

  CreateTestRequestWithSamplesUseCase(this.repository);

  Future<Either<Failure, CreateTestRequestWithSamplesResult>> call(
    CreateTestRequestWithSamplesParams params,
  ) async {
    // First, create the test request
    final createResult = await repository.createTestRequest(params.testRequest);

    return createResult.fold(
      (failure) => Left(failure),
      (createdTestRequest) async {
        final List<SampleEntity> addedSamples = [];
        final List<Failure> sampleFailures = [];

        // Add each sample to the created test request
        for (final sample in params.samples) {
          final sampleResult = await repository.addSample(
            createdTestRequest.id!,
            sample,
          );

          sampleResult.fold(
            (failure) => sampleFailures.add(failure),
            (addedSample) => addedSamples.add(addedSample),
          );
        }

        // If any sample failed to add, return the first failure
        if (sampleFailures.isNotEmpty) {
          return Left(sampleFailures.first);
        }

        return Right(CreateTestRequestWithSamplesResult(
          testRequest: createdTestRequest,
          samples: addedSamples,
        ));
      },
    );
  }
}

class CreateTestRequestWithSamplesParams extends Equatable {
  final TestRequestEntity testRequest;
  final List<SampleEntity> samples;

  const CreateTestRequestWithSamplesParams({
    required this.testRequest,
    required this.samples,
  });

  @override
  List<Object> get props => [testRequest, samples];
}

class CreateTestRequestWithSamplesResult extends Equatable {
  final TestRequestEntity testRequest;
  final List<SampleEntity> samples;

  const CreateTestRequestWithSamplesResult({
    required this.testRequest,
    required this.samples,
  });

  @override
  List<Object> get props => [testRequest, samples];
}
