import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/test_request/sample_entity.dart';
import '../../repositories/test_request_repository.dart';

class GetSamplesUseCase {
  final TestRequestRepository repository;

  GetSamplesUseCase(this.repository);

  Future<Either<Failure, List<SampleEntity>>> call(GetSamplesParams params) async {
    return await repository.getSamples(params.testRequestId);
  }
}

class GetSamplesParams extends Equatable {
  final int testRequestId;

  const GetSamplesParams({
    required this.testRequestId,
  });

  @override
  List<Object> get props => [testRequestId];
}
