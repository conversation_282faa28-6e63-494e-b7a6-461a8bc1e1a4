import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/approval/approval_entity.dart';
import '../../entities/common/pagination_entity.dart';
import '../../repositories/approval_repository.dart';

class GetPendingApprovalsUseCase implements UseCase<PaginatedResult<ApprovalEntity>, GetPendingApprovalsParams> {
  final ApprovalRepository repository;

  GetPendingApprovalsUseCase(this.repository);

  @override
  Future<Either<Failure, PaginatedResult<ApprovalEntity>>> call(GetPendingApprovalsParams params) async {
    return await repository.getPendingApprovals(
      page: params.page,
      perPage: params.perPage,
    );
  }
}

class GetPendingApprovalsParams extends Equatable {
  final int page;
  final int perPage;

  const GetPendingApprovalsParams({
    this.page = 1,
    this.perPage = 15,
  });

  @override
  List<Object> get props => [page, perPage];
}
