import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/approval/approval_entity.dart';
import '../../repositories/approval_repository.dart';

class ProcessApprovalUseCase implements UseCase<ApprovalEntity, ProcessApprovalParams> {
  final ApprovalRepository repository;

  ProcessApprovalUseCase(this.repository);

  @override
  Future<Either<Failure, ApprovalEntity>> call(ProcessApprovalParams params) async {
    return await repository.processApproval(params.testId, params.request);
  }
}

class ProcessApprovalParams extends Equatable {
  final int testId;
  final ApprovalRequestEntity request;

  const ProcessApprovalParams({
    required this.testId,
    required this.request,
  });

  @override
  List<Object> get props => [testId, request];
}
