import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/approval/approval_entity.dart';
import '../../repositories/approval_repository.dart';

class ProcessBulkApprovalUseCase implements UseCase<BulkApprovalResult, BulkApprovalRequestEntity> {
  final ApprovalRepository repository;

  ProcessBulkApprovalUseCase(this.repository);

  @override
  Future<Either<Failure, BulkApprovalResult>> call(BulkApprovalRequestEntity params) async {
    return await repository.processBulkApproval(params);
  }
}
