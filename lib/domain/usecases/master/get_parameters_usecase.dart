import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/master/parameter_entity.dart';
import '../../repositories/master_repository.dart';

class GetParametersUseCase {
  final MasterRepository repository;

  GetParametersUseCase(this.repository);

  Future<Either<Failure, List<ParameterEntity>>> call(NoParams params) async {
    return await repository.getParameters();
  }
}
