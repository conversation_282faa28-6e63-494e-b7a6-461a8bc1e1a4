import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/master/customer_entity.dart';
import '../../repositories/master_repository.dart';

class GetCustomersUseCase {
  final MasterRepository repository;

  GetCustomersUseCase(this.repository);

  Future<Either<Failure, List<CustomerEntity>>> call(GetCustomersParams params) async {
    return await repository.getCustomers(search: params.search);
  }
}

class GetCustomersParams extends Equatable {
  final String? search;

  const GetCustomersParams({
    this.search,
  });

  @override
  List<Object?> get props => [search];
}
