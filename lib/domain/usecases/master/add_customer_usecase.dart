import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/master/customer_entity.dart';
import '../../repositories/master_repository.dart';

class AddCustomerUseCase {
  final MasterRepository repository;

  AddCustomerUseCase(this.repository);

  Future<Either<Failure, CustomerEntity>> call(AddCustomerParams params) async {
    return await repository.addCustomer(params.customer);
  }
}

class AddCustomerParams extends Equatable {
  final CustomerEntity customer;

  const AddCustomerParams({
    required this.customer,
  });

  @override
  List<Object> get props => [customer];
}
