import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/test_result/test_result_entity.dart';
import '../../repositories/test_result_repository.dart';

class CreateTestResultUseCase implements UseCase<TestResultEntity, Map<String, dynamic>> {
  final TestResultRepository repository;

  CreateTestResultUseCase(this.repository);

  @override
  Future<Either<Failure, TestResultEntity>> call(Map<String, dynamic> params) async {
    return await repository.addTestResult(params);
  }
}
