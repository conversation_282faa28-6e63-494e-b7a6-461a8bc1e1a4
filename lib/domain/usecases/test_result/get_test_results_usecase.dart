import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/test_result/test_result_entity.dart';
import '../../entities/common/pagination_entity.dart';
import '../../repositories/test_result_repository.dart';

class GetTestResultsUseCase implements UseCase<PaginatedResult<TestResultEntity>, GetTestResultsParams> {
  final TestResultRepository repository;

  GetTestResultsUseCase(this.repository);

  @override
  Future<Either<Failure, PaginatedResult<TestResultEntity>>> call(GetTestResultsParams params) async {
    return await repository.getTestResults(
      page: params.page,
      perPage: params.perPage,
      parameterId: params.parameterId,
      sampleId: params.sampleId,
      analystId: params.analystId,
      status: params.status,
    );
  }
}

class GetTestResultsParams extends Equatable {
  final int page;
  final int perPage;
  final int? parameterId;
  final int? sampleId;
  final int? analystId;
  final TestResultStatus? status;

  const GetTestResultsParams({
    this.page = 1,
    this.perPage = 15,
    this.parameterId,
    this.sampleId,
    this.analystId,
    this.status,
  });

  @override
  List<Object?> get props => [page, perPage, parameterId, sampleId, analystId, status];
}
