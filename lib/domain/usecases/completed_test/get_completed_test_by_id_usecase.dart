import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/completed_test/completed_test_entity.dart';
import '../../repositories/completed_test_repository.dart';

class GetCompletedTestByIdUseCase implements UseCase<CompletedTestEntity, GetCompletedTestByIdParams> {
  final CompletedTestRepository repository;

  GetCompletedTestByIdUseCase(this.repository);

  @override
  Future<Either<Failure, CompletedTestEntity>> call(GetCompletedTestByIdParams params) async {
    return await repository.getCompletedTestById(params.id);
  }
}

class GetCompletedTestByIdParams extends Equatable {
  final int id;

  const GetCompletedTestByIdParams({required this.id});

  @override
  List<Object> get props => [id];
}
