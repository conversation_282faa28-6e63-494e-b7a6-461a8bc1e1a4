import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/completed_test_repository.dart';

class GetCompletedTestsUseCase implements UseCase<CompletedTestsResult, GetCompletedTestsParams> {
  final CompletedTestRepository repository;

  GetCompletedTestsUseCase(this.repository);

  @override
  Future<Either<Failure, CompletedTestsResult>> call(GetCompletedTestsParams params) async {
    return await repository.getCompletedTests(
      page: params.page,
      perPage: params.perPage,
    );
  }
}

class GetCompletedTestsParams extends Equatable {
  final int page;
  final int perPage;

  const GetCompletedTestsParams({
    this.page = 1,
    this.perPage = 15,
  });

  @override
  List<Object> get props => [page, perPage];
}
