# LIMS Use Cases Documentation

This document provides comprehensive documentation for all use cases implemented in the LIMS Flutter application.

## 🏗️ Use Case Architecture

All use cases follow the Clean Architecture pattern and implement the following structure:
- **Input**: Parameters class (extends Equatable)
- **Output**: Either<Failure, Result> using dartz functional programming
- **Dependency**: Repository interface injection
- **Single Responsibility**: Each use case handles one specific business operation

## 📋 Use Cases by Module

### 🔐 Authentication Module

#### 1. LoginUseCase
**Purpose**: Authenticate user with username and password
```dart
final result = await loginUseCase(LoginParams(
  username: 'superadmin',
  password: '12345678',
));
```

#### 2. LogoutUseCase
**Purpose**: Terminate user session and clear local storage
```dart
final result = await logoutUseCase();
```

#### 3. GetProfileUseCase
**Purpose**: Retrieve current user profile information
```dart
final result = await getProfileUseCase();
```

#### 4. UpdateProfileUseCase
**Purpose**: Update user profile information
```dart
final result = await updateProfileUseCase(UpdateProfileParams(
  name: 'Updated Name',
));
```

#### 5. LoginAndGetProfileUseCase (Composite)
**Purpose**: Complete authentication flow - login and fetch profile
```dart
final result = await loginAndGetProfileUseCase(LoginAndGetProfileParams(
  username: 'superadmin',
  password: '12345678',
));
```

### 🧪 Test Request Module

#### 1. GetTestRequestsUseCase
**Purpose**: Retrieve paginated list of test requests with search
```dart
final result = await getTestRequestsUseCase(GetTestRequestsParams(
  page: 1,
  perPage: 15,
  search: 'CS396',
));
```

#### 2. GetTestRequestByIdUseCase
**Purpose**: Retrieve specific test request details
```dart
final result = await getTestRequestByIdUseCase(GetTestRequestByIdParams(
  id: 18,
));
```

#### 3. CreateTestRequestUseCase
**Purpose**: Create new test request
```dart
final result = await createTestRequestUseCase(CreateTestRequestParams(
  testRequest: testRequestEntity,
));
```

#### 4. UpdateTestRequestUseCase
**Purpose**: Update existing test request
```dart
final result = await updateTestRequestUseCase(UpdateTestRequestParams(
  id: 18,
  testRequest: updatedTestRequestEntity,
));
```

#### 5. DeleteTestRequestUseCase
**Purpose**: Delete test request
```dart
final result = await deleteTestRequestUseCase(DeleteTestRequestParams(
  id: 18,
));
```

#### 6. GetSamplesUseCase
**Purpose**: Retrieve samples for a test request
```dart
final result = await getSamplesUseCase(GetSamplesParams(
  testRequestId: 18,
));
```

#### 7. AddSampleUseCase
**Purpose**: Add new sample to test request
```dart
final result = await addSampleUseCase(AddSampleParams(
  testRequestId: 18,
  sample: sampleEntity,
));
```

#### 8. CreateTestRequestWithSamplesUseCase (Composite)
**Purpose**: Create test request and add multiple samples in one operation
```dart
final result = await createTestRequestWithSamplesUseCase(
  CreateTestRequestWithSamplesParams(
    testRequest: testRequestEntity,
    samples: [sample1, sample2, sample3],
  ),
);
```

#### 9. GetTestRequestWithSamplesUseCase (Composite)
**Purpose**: Get test request details along with all its samples
```dart
final result = await getTestRequestWithSamplesUseCase(
  GetTestRequestWithSamplesParams(id: 18),
);
```

### 👷 Job Allocation Module

#### 1. GetJobAllocationsUseCase
**Purpose**: Retrieve all job allocations
```dart
final result = await getJobAllocationsUseCase();
```

#### 2. GetJobAllocationByIdUseCase
**Purpose**: Retrieve specific job allocation details
```dart
final result = await getJobAllocationByIdUseCase(GetJobAllocationByIdParams(
  id: 13,
));
```

#### 3. CreateJobAllocationUseCase
**Purpose**: Create new job allocation
```dart
final result = await createJobAllocationUseCase(CreateJobAllocationParams(
  jobAllocation: jobAllocationEntity,
));
```

#### 4. UpdateJobAllocationUseCase
**Purpose**: Update existing job allocation
```dart
final result = await updateJobAllocationUseCase(UpdateJobAllocationParams(
  id: 13,
  jobAllocation: updatedJobAllocationEntity,
));
```

#### 5. DeleteJobAllocationUseCase
**Purpose**: Delete job allocation
```dart
final result = await deleteJobAllocationUseCase(DeleteJobAllocationParams(
  id: 13,
));
```

### 📊 Master Data Module

#### 1. GetParametersUseCase
**Purpose**: Retrieve all test parameters
```dart
final result = await getParametersUseCase(const NoParams());
```

#### 2. GetCustomersUseCase
**Purpose**: Retrieve customers with optional search
```dart
final result = await getCustomersUseCase(GetCustomersParams(
  search: 'abc',
));
```

#### 3. AddCustomerUseCase
**Purpose**: Add new customer
```dart
final result = await addCustomerUseCase(AddCustomerParams(
  customer: customerEntity,
));
```

### 📈 Dashboard Module

#### 1. GetDashboardStatsUseCase
**Purpose**: Retrieve dashboard statistics
```dart
final result = await getDashboardStatsUseCase(const NoParams());
```

### 🔍 Search Module

#### 1. GlobalSearchUseCase (Composite)
**Purpose**: Perform global search across test requests and customers
```dart
final result = await globalSearchUseCase(GlobalSearchParams(
  query: 'search term',
  maxResults: 10,
));
```

## 🔄 Error Handling Pattern

All use cases return `Either<Failure, Result>` for consistent error handling:

```dart
result.fold(
  (failure) {
    // Handle different types of failures
    if (failure is NetworkFailure) {
      // Handle network errors
    } else if (failure is ServerFailure) {
      // Handle server errors
    } else if (failure is ValidationFailure) {
      // Handle validation errors
    }
  },
  (success) {
    // Handle successful result
  },
);
```

## 🧪 Testing Use Cases

Use cases are easily testable due to their dependency injection:

```dart
// Mock the repository
final mockRepository = MockAuthRepository();

// Create use case with mock
final useCase = LoginUseCase(mockRepository);

// Setup mock behavior
when(mockRepository.login(any, any))
    .thenAnswer((_) async => Right(loginEntity));

// Test the use case
final result = await useCase(LoginParams(
  username: 'test',
  password: 'test',
));

// Verify result
expect(result.isRight(), true);
```

## 🚀 Usage in Presentation Layer

Use cases are injected into BLoCs/Cubits for state management:

```dart
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;

  AuthBloc({
    required this.loginUseCase,
    required this.logoutUseCase,
  }) : super(AuthInitial());

  @override
  Stream<AuthState> mapEventToState(AuthEvent event) async* {
    if (event is LoginRequested) {
      yield AuthLoading();
      
      final result = await loginUseCase(LoginParams(
        username: event.username,
        password: event.password,
      ));

      yield result.fold(
        (failure) => AuthError(failure.message),
        (loginEntity) => AuthSuccess(loginEntity.user),
      );
    }
  }
}
```

## 📝 Best Practices

1. **Single Responsibility**: Each use case handles one business operation
2. **Immutable Parameters**: All parameter classes are immutable and extend Equatable
3. **Error Handling**: Consistent error handling using Either<Failure, Result>
4. **Dependency Injection**: Use cases depend on repository interfaces, not implementations
5. **Composite Use Cases**: Combine multiple operations for complex business workflows
6. **Testing**: Easy to test with mocked dependencies
