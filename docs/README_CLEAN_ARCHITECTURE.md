# LIMS Flutter App - Clean Architecture Implementation

This Flutter application implements a clean architecture pattern for a Lab Management Information System (LIMS) with comprehensive REST API integration.

## 🏗️ Architecture Overview

The project follows Clean Architecture principles with clear separation of concerns across three main layers:

### 📁 Project Structure

```
lib/
├── core/                           # Core utilities and shared components
│   ├── constants/
│   │   └── api_constants.dart      # API endpoints and configuration
│   ├── error/
│   │   ├── exceptions.dart         # Custom exceptions
│   │   └── failures.dart          # Failure classes for error handling
│   ├── network/
│   │   ├── dio_client.dart         # HTTP client configuration
│   │   └── api_response.dart       # Generic API response wrapper
│   └── di/
│       └── injection_container.dart # Dependency injection setup
├── data/                           # Data layer
│   ├── datasources/               # Remote data sources
│   │   ├── auth_remote_data_source.dart
│   │   ├── test_request_remote_data_source.dart
│   │   ├── job_allocation_remote_data_source.dart
│   │   ├── master_remote_data_source.dart
│   │   └── dashboard_remote_data_source.dart
│   ├── models/                    # Data transfer objects
│   │   ├── auth/
│   │   ├── test_request/
│   │   ├── job_allocation/
│   │   ├── master/
│   │   └── dashboard/
│   └── repositories/              # Repository implementations
│       ├── auth_repository_impl.dart
│       ├── test_request_repository_impl.dart
│       ├── job_allocation_repository_impl.dart
│       ├── master_repository_impl.dart
│       └── dashboard_repository_impl.dart
├── domain/                        # Domain layer (Business logic)
│   ├── entities/                  # Business entities
│   │   ├── auth/
│   │   ├── test_request/
│   │   ├── job_allocation/
│   │   ├── master/
│   │   └── dashboard/
│   ├── repositories/              # Repository interfaces
│   │   ├── auth_repository.dart
│   │   ├── test_request_repository.dart
│   │   ├── job_allocation_repository.dart
│   │   ├── master_repository.dart
│   │   └── dashboard_repository.dart
│   └── usecases/                  # Business use cases
│       ├── auth/
│       ├── test_request/
│       └── ...
└── presentation/                  # Presentation layer (UI)
    └── (To be implemented)
```

## 🔧 API Modules Implemented

Based on the provided Postman collection, the following API modules have been implemented:

### 1. **Authentication Module**
- **Login** - User authentication with token generation
- **Logout** - User session termination
- **Get Profile** - Retrieve user profile information
- **Update Profile** - Update user profile data

### 2. **Test Requests Module**
- **Get Test Requests** - Retrieve paginated list with search functionality
- **Get Test Request Details** - Retrieve specific test request by ID
- **Create Test Request** - Create new test request with samples
- **Update Test Request** - Update existing test request
- **Delete Test Request** - Remove test request
- **Get Samples** - Retrieve samples for a test request
- **Add Sample** - Add new sample to a test request

### 3. **Job Allocation Module**
- **Get Job Allocations** - Retrieve all job allocations
- **Get Job Allocation Details** - Retrieve specific job allocation
- **Create Job Allocation** - Create new job allocation
- **Update Job Allocation** - Update existing job allocation
- **Delete Job Allocation** - Remove job allocation

### 4. **Master Data Module**
- **Get Parameters** - Retrieve test parameters
- **Get Customers** - Retrieve customers with search functionality
- **Add Customer** - Create new customer record

### 5. **Dashboard Module**
- **Get Dashboard Stats** - Retrieve dashboard statistics

## 🛠️ Key Features

### ✅ Clean Architecture Benefits
- **Separation of Concerns**: Clear boundaries between layers
- **Testability**: Easy to unit test business logic
- **Maintainability**: Easy to modify and extend
- **Independence**: UI, database, and external services are independent

### ✅ Error Handling
- Comprehensive error handling with custom exceptions
- Network error management
- Server error handling with status codes
- Validation error handling

### ✅ Dependency Injection
- GetIt for dependency injection
- Singleton pattern for shared instances
- Easy to mock for testing

### ✅ HTTP Client Configuration
- Dio HTTP client with interceptors
- Automatic token injection
- Request/response logging
- Timeout configuration

## 📦 Dependencies

```yaml
dependencies:
  # HTTP client
  dio: ^5.4.0
  
  # Functional programming
  dartz: ^0.10.1
  
  # Dependency injection
  get_it: ^7.6.4
  
  # JSON serialization
  json_annotation: ^4.8.1
  
  # Local storage
  shared_preferences: ^2.2.2
  
  # Value equality
  equatable: ^2.0.5

dev_dependencies:
  # JSON code generation
  json_serializable: ^6.7.1
  build_runner: ^2.4.7
```

## 🚀 Getting Started

### 1. Install Dependencies
```bash
flutter pub get
```

### 2. Generate JSON Serialization Code
```bash
flutter packages pub run build_runner build
```

### 3. Run the Application
```bash
flutter run
```

## 🔄 Data Flow

1. **Presentation Layer** calls use cases
2. **Use Cases** interact with repository interfaces
3. **Repository Implementations** use remote data sources
4. **Remote Data Sources** make HTTP calls via Dio client
5. **Models** are converted to **Entities** in repositories
6. **Entities** are returned to presentation layer

## 🧪 Testing Strategy

The clean architecture makes testing straightforward:

- **Unit Tests**: Test use cases and entities
- **Integration Tests**: Test repository implementations
- **Widget Tests**: Test UI components
- **Mock Testing**: Easy to mock dependencies

## 🔐 Authentication Flow

1. User provides credentials
2. `LoginUseCase` calls `AuthRepository`
3. Repository uses `AuthRemoteDataSource`
4. Token is stored in `SharedPreferences`
5. Token is automatically added to subsequent requests

## 📱 Next Steps

1. **Implement Presentation Layer**:
   - Create screens for each module
   - Implement state management (Bloc/Cubit)
   - Add form validation

2. **Add More Use Cases**:
   - Implement remaining business logic
   - Add pagination handling
   - Implement offline capabilities

3. **Testing**:
   - Write comprehensive unit tests
   - Add integration tests
   - Implement widget tests

4. **Performance Optimization**:
   - Add caching mechanisms
   - Implement lazy loading
   - Optimize network calls

## 🤝 Contributing

This clean architecture implementation provides a solid foundation for the LIMS application. The modular structure makes it easy to add new features and maintain existing code.
