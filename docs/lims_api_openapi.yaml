openapi: 3.0.3
info:
  title: LIMS API
  description: Laboratory Information Management System API
  version: 1.0.0
  contact:
    name: LIMS API Support
    url: https://managemylab.in
servers:
  - url: https://managemylab.in/public/api/v1
    description: Production server

security:
  - bearerAuth: []

paths:
  # Authentication endpoints
  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'

  /auth/profile:
    get:
      tags:
        - Authentication
      summary: Get user profile
      responses:
        '200':
          description: Profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileResponse'
    put:
      tags:
        - Authentication
      summary: Update user profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProfileRequest'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileResponse'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: User logout
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  # Test Requests endpoints
  /test-requests:
    get:
      tags:
        - Test Requests
      summary: Get test requests with pagination
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 15
        - name: is_allocated
          in: query
          schema:
            type: boolean
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Test requests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestRequestsResponse'
    post:
      tags:
        - Test Requests
      summary: Create new test request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTestRequestRequest'
      responses:
        '201':
          description: Test request created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestRequestResponse'

  /test-requests/{id}:
    get:
      tags:
        - Test Requests
      summary: Get test request details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Test request details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestRequestResponse'
    put:
      tags:
        - Test Requests
      summary: Update test request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTestRequestRequest'
      responses:
        '200':
          description: Test request updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestRequestResponse'
    delete:
      tags:
        - Test Requests
      summary: Delete test request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Test request deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /test-requests/{id}/samples:
    get:
      tags:
        - Test Requests
      summary: Get samples for test request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Samples retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SamplesResponse'
    post:
      tags:
        - Test Requests
      summary: Add sample to test request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSampleRequest'
      responses:
        '201':
          description: Sample added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SampleResponse'

  # Job Allocation endpoints
  /job-allocations:
    get:
      tags:
        - Job Allocation
      summary: Get job allocations
      responses:
        '200':
          description: Job allocations retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobAllocationsResponse'
    post:
      tags:
        - Job Allocation
      summary: Create job allocation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateJobAllocationRequest'
      responses:
        '201':
          description: Job allocation created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobAllocationResponse'

  /job-allocations/{id}:
    delete:
      tags:
        - Job Allocation
      summary: Delete job allocation
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Job allocation deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /job-allocations/export/excel:
    get:
      tags:
        - Job Allocation
      summary: Export job allocations to Excel
      responses:
        '200':
          description: Export data prepared
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExportResponse'

  /job-allocations/{id}/pdf:
    get:
      tags:
        - Job Allocation
      summary: Generate PDF for job allocation
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: PDF generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PDFResponse'

  # Completed Tests endpoints
  /completed-tests:
    get:
      tags:
        - Completed Tests
      summary: Get completed tests
      responses:
        '200':
          description: Completed tests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompletedTestsResponse'

  /completed-tests/{id}:
    get:
      tags:
        - Completed Tests
      summary: Get completed test details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Completed test details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompletedTestResponse'

  # Quality Control endpoints
  /quality-control/tests:
    get:
      tags:
        - Quality Control
      summary: Get quality control tests
      responses:
        '200':
          description: QC tests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QCTestsResponse'
    post:
      tags:
        - Quality Control
      summary: Create quality control test
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQCTestRequest'
      responses:
        '201':
          description: QC test created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QCTestResponse'

  /quality-control/tests/{id}/approve:
    put:
      tags:
        - Quality Control
      summary: Approve or reject QC test
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApproveTestRequest'
      responses:
        '200':
          description: Test approval status updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QCTestResponse'

  # Analyst endpoints
  /analyst/my-tests:
    get:
      tags:
        - Analyst
      summary: Get analyst's assigned tests
      responses:
        '200':
          description: Analyst tests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalystTestsResponse'

  /analyst/tests/{id}/submit:
    post:
      tags:
        - Analyst
      summary: Submit test result
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitTestResultRequest'
      responses:
        '200':
          description: Test result submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestResultResponse'

  /analyst/tests/{id}/reject:
    post:
      tags:
        - Analyst
      summary: Reject test
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RejectTestRequest'
      responses:
        '200':
          description: Test rejected successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  # Dashboard endpoints
  /dashboard/stats:
    get:
      tags:
        - Dashboard
      summary: Get dashboard statistics
      responses:
        '200':
          description: Statistics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardStatsResponse'

  # Masters endpoints
  /masters/customers:
    get:
      tags:
        - Masters
      summary: Get customers
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomersResponse'
    post:
      tags:
        - Masters
      summary: Add customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'

  /masters/parameters:
    get:
      tags:
        - Masters
      summary: Get parameters
      responses:
        '200':
          description: Parameters retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParametersResponse'
    post:
      tags:
        - Masters
      summary: Add parameter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateParameterRequest'
      responses:
        '201':
          description: Parameter created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParameterResponse'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # Common schemas
    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string

    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
        total_pages:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        has_more:
          type: boolean

    # Authentication schemas
    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          example: "superadmin"
        password:
          type: string
          example: "12345678"

    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            token:
              type: string
              example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            profile:
              $ref: '#/components/schemas/User'

    ProfileResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            profile:
              $ref: '#/components/schemas/User'

    UpdateProfileRequest:
      type: object
      properties:
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          example: "<EMAIL>"

    User:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "Super admin"
        email:
          type: string
          example: "<EMAIL>"
        username:
          type: string
          example: "superadmin"
        email_verified_at:
          type: string
          format: date-time
          nullable: true
          example: null
        created_at:
          type: string
          format: date-time
          example: "2020-09-12T04:03:43.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-06-11T06:57:07.000000Z"

    # Test Request schemas
    TestRequest:
      type: object
      properties:
        id:
          type: integer
          example: 18
        request_number:
          type: string
          example: "23123"
        request_date:
          type: string
          format: date-time
          example: "2025-03-06T00:00:00.000000Z"
        customer_name:
          type: string
          example: "ABC Industries"
        customer_address:
          type: string
          example: "123 Industrial Area, Mumbai"
        contact_person:
          type: string
          example: "Rajesh Kumar"
        mobile_no:
          type: string
          example: "9999999999"
        email:
          type: string
          example: "<EMAIL>"
        special_request:
          type: string
          example: "Handle with care"
        submitted_by_name:
          type: string
          example: "Officer A"
        submitted_by_designation:
          type: string
          example: "Field Agent"
        submitted_by_date:
          type: string
          format: date-time
          example: "2025-04-03T00:00:00.000000Z"
        submitted_by_id_proof:
          type: string
          example: "**********"
        received_by_name:
          type: string
          example: "Officer B"
        received_by_designation:
          type: string
          example: "Lab Assistant"
        received_by_date:
          type: string
          format: date-time
          example: "2025-05-03T00:00:00.000000Z"
        received_by_id_proof:
          type: string
          example: "**********"
        sample_received_time:
          type: string
          format: date-time
          nullable: true
          example: "2025-06-04T14:30:00.000000Z"
        sample_collection_time:
          type: string
          format: date-time
          example: "2025-03-05T13:16:00.000000Z"
        quantity_of_sample:
          type: string
          example: "Sufficient"
        type_of_sample:
          type: string
          enum: [Solid, Liquid, Gas]
          example: "Liquid"
        sample_details:
          type: string
          example: "Water sample from river"
        sample_code:
          type: string
          example: "SC-20250604-001"
        deleted:
          type: integer
          example: 0
        is_allocated:
          type: boolean
          example: true
        samples:
          type: array
          items:
            $ref: '#/components/schemas/Sample'
        created_at:
          type: string
          format: date-time
          example: "2025-03-06T10:17:46.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-03-06T10:17:46.000000Z"

    CreateTestRequestRequest:
      type: object
      required:
        - request_number
        - request_date
        - customer_name
        - customer_address
        - contact_person
        - mobile_no
        - email
      properties:
        request_number:
          type: string
          example: "TR-2025-001"
        request_date:
          type: string
          format: date
          example: "2025-06-14"
        customer_name:
          type: string
          example: "XYZ Corporation"
        customer_address:
          type: string
          example: "456 Business Park, Delhi"
        contact_person:
          type: string
          example: "Priya Sharma"
        mobile_no:
          type: string
          example: "9876543210"
        email:
          type: string
          example: "<EMAIL>"
        special_request:
          type: string
          example: "Urgent analysis required"
        submitted_by_name:
          type: string
          example: "Field Officer"
        submitted_by_designation:
          type: string
          example: "Sample Collector"
        submitted_by_date:
          type: string
          format: date
          example: "2025-06-14"
        submitted_by_id_proof:
          type: string
          example: "EMP001"
        received_by_name:
          type: string
          example: "Lab Technician"
        received_by_designation:
          type: string
          example: "Sample Receiver"
        received_by_date:
          type: string
          format: date
          example: "2025-06-14"
        received_by_id_proof:
          type: string
          example: "LAB001"
        sample_received_time:
          type: string
          format: date-time
          example: "2025-06-14T10:30:00Z"
        sample_collection_time:
          type: string
          format: date-time
          example: "2025-06-14T09:00:00Z"
        quantity_of_sample:
          type: string
          example: "500ml"
        type_of_sample:
          type: string
          enum: [Solid, Liquid, Gas]
          example: "Liquid"
        sample_details:
          type: string
          example: "Drinking water sample"
        sample_code:
          type: string
          example: "WS-001"
        samples:
          type: array
          items:
            $ref: '#/components/schemas/CreateSampleRequest'

    UpdateTestRequestRequest:
      type: object
      properties:
        customer_name:
          type: string
        customer_address:
          type: string
        contact_person:
          type: string
        mobile_no:
          type: string
        email:
          type: string
        special_request:
          type: string
        sample_details:
          type: string

    TestRequestsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            test_requests:
              type: array
              items:
                $ref: '#/components/schemas/TestRequest'
            pagination:
              $ref: '#/components/schemas/PaginationMeta'

    TestRequestResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            test_request:
              $ref: '#/components/schemas/TestRequest'

    # Sample schemas
    Sample:
      type: object
      properties:
        id:
          type: integer
          example: 25
        test_request_id:
          type: integer
          example: 18
        parameter_id:
          type: integer
          example: 1
        particulars:
          type: string
          example: "pH Level"
        type:
          type: string
          example: "Chemical"
        quantity:
          type: string
          example: "100ml"
        remarks:
          type: string
          example: "Standard testing procedure"
        created_at:
          type: string
          format: date-time
          example: "2025-03-06T10:17:46.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-03-06T10:17:46.000000Z"
        parameter:
          $ref: '#/components/schemas/Parameter'

    CreateSampleRequest:
      type: object
      required:
        - parameter_id
        - particulars
        - type
        - quantity
      properties:
        parameter_id:
          type: integer
          example: 1
        particulars:
          type: string
          example: "pH Level"
        type:
          type: string
          example: "Chemical"
        quantity:
          type: string
          example: "100ml"
        remarks:
          type: string
          example: "Standard testing procedure"

    SamplesResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            samples:
              type: array
              items:
                $ref: '#/components/schemas/Sample'

    SampleResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            sample:
              $ref: '#/components/schemas/Sample'

    # Parameter schemas
    Parameter:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "pH"
        type:
          type: string
          nullable: true
          example: "Chemical"
        status:
          type: integer
          example: 1
        requirement:
          type: string
          example: "6.5 - 8.5"
        permissible_limit:
          type: string
          example: "6.0 - 9.0"
        protocol_used:
          type: string
          example: "IS 3025 (Part 11)"
        units:
          type: string
          nullable: true
          example: "pH units"
        created_at:
          type: string
          format: date-time
          example: "2025-01-01T00:00:00.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-01-01T00:00:00.000000Z"

    CreateParameterRequest:
      type: object
      required:
        - name
        - requirement
        - permissible_limit
        - protocol_used
      properties:
        name:
          type: string
          example: "Dissolved Oxygen"
        type:
          type: string
          example: "Chemical"
        requirement:
          type: string
          example: "> 5.0 mg/L"
        permissible_limit:
          type: string
          example: "> 4.0 mg/L"
        protocol_used:
          type: string
          example: "IS 3025 (Part 38)"
        units:
          type: string
          example: "mg/L"
        status:
          type: integer
          default: 1
          example: 1

    ParametersResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            parameters:
              type: array
              items:
                $ref: '#/components/schemas/Parameter'

    ParameterResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            parameter:
              $ref: '#/components/schemas/Parameter'

    # Job Allocation schemas
    JobAllocation:
      type: object
      properties:
        id:
          type: integer
          example: 14
        serial_no:
          type: string
          example: "JAF/ 25-26/0003"
        creation_date:
          type: string
          format: date-time
          example: "2025-06-11 00:00:00"
        code_number:
          type: string
          example: "LAB-0617-25"
        nature:
          type: string
          example: "Lake water analysis"
        quantity:
          type: string
          example: "500ml"
        collection_date:
          type: string
          format: date
          example: "2025-06-10"
        submission_date:
          type: string
          format: date
          example: "2025-06-11"
        due_date:
          type: string
          format: date
          example: "2025-06-18"
        user_id:
          type: integer
          example: 5
        test_request_id:
          type: integer
          example: 17
        report_type:
          type: string
          enum: [water, standard, location]
          example: "water"
        designation:
          type: string
          example: "Lab Analyst"
        remarks:
          type: string
          example: "Priority analysis required"
        nabl_status:
          type: string
          enum: [yes, no]
          example: "yes"
        deleted:
          type: integer
          example: 0
        created_at:
          type: string
          format: date-time
          example: "2025-06-11T06:57:07.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-06-11T06:57:07.000000Z"
        test_request:
          $ref: '#/components/schemas/TestRequest'
        user:
          $ref: '#/components/schemas/User'
        parameter_allocations:
          type: array
          items:
            $ref: '#/components/schemas/ParameterAllocation'

    ParameterAllocation:
      type: object
      properties:
        id:
          type: integer
          example: 1
        job_detail_id:
          type: integer
          example: 14
        sample_id:
          type: integer
          example: 25
        analyst_id:
          type: integer
          example: 3
        is_retest:
          type: integer
          example: 0
        is_blind:
          type: integer
          example: 0
        is_replicate:
          type: integer
          example: 0
        is_spiked:
          type: integer
          example: 0
        spiked_result:
          type: string
          nullable: true
          example: null
        original_allocation_id:
          type: integer
          nullable: true
          example: null
        status:
          type: string
          nullable: true
          example: "pending"
        rejection_reason:
          type: string
          nullable: true
          example: null
        rejected_at:
          type: string
          format: date-time
          nullable: true
          example: null
        created_at:
          type: string
          format: date-time
          example: "2025-06-11T06:57:07.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-06-11T06:57:07.000000Z"
        sample:
          $ref: '#/components/schemas/Sample'
        analyst:
          $ref: '#/components/schemas/User'
        approved_by:
          $ref: '#/components/schemas/User'
          nullable: true

    CreateJobAllocationRequest:
      type: object
      required:
        - test_request_id
        - creation_date
        - code_number
        - nature
        - quantity
        - collection_date
        - submission_date
        - due_date
        - user_id
        - designation
        - nabl_status
        - report_type
        - parameter_allocations
      properties:
        test_request_id:
          type: integer
          example: 18
        creation_date:
          type: string
          format: date
          example: "2025-06-14"
        code_number:
          type: string
          example: "LAB-0614-25"
        nature:
          type: string
          example: "Water quality analysis"
        quantity:
          type: string
          example: "1 liter"
        collection_date:
          type: string
          format: date
          example: "2025-06-13"
        submission_date:
          type: string
          format: date
          example: "2025-06-14"
        due_date:
          type: string
          format: date
          example: "2025-06-21"
        user_id:
          type: integer
          example: 3
        designation:
          type: string
          example: "Senior Analyst"
        remarks:
          type: string
          example: "High priority sample"
        nabl_status:
          type: string
          enum: [yes, no]
          example: "yes"
        report_type:
          type: string
          enum: [water, standard, location]
          example: "water"
        password:
          type: string
          example: "admin123"
        parameter_allocations:
          type: array
          items:
            type: object
            properties:
              sample_id:
                type: integer
                example: 25
              analyst_id:
                type: integer
                example: 3

    JobAllocationsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            job_allocations:
              type: array
              items:
                $ref: '#/components/schemas/JobAllocation'
            pagination:
              $ref: '#/components/schemas/PaginationMeta'

    JobAllocationResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            job_allocation:
              $ref: '#/components/schemas/JobAllocation'

    ExportResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            job_allocations:
              type: array
              items:
                $ref: '#/components/schemas/JobAllocation'
        export_url:
          type: string

    PDFResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            export_data:
              type: array
              items:
                type: object
            total_records:
              type: integer
            filename:
              type: string

    # Completed Tests schemas
    CompletedTest:
      type: object
      properties:
        id:
          type: integer
          example: 1
        job_detail_id:
          type: integer
          example: 14
        test_request_sample_id:
          type: integer
          example: 25
        user_id:
          type: integer
          example: 3
        result:
          type: string
          example: "7.2"
        analysis_start_date:
          type: string
          format: date-time
          example: "2025-06-14T09:00:00.000000Z"
        analysis_completion_date:
          type: string
          format: date-time
          example: "2025-06-14T15:30:00.000000Z"
        analysis_submission_date:
          type: string
          format: date-time
          example: "2025-06-14T16:00:00.000000Z"
        is_retest:
          type: integer
          example: 0
        is_blind:
          type: integer
          example: 0
        is_replicate:
          type: integer
          example: 0
        original_test_id:
          type: integer
          nullable: true
          example: null
        raw_water:
          type: string
          nullable: true
          example: "7.1"
        filtered_water:
          type: string
          nullable: true
          example: "7.2"
        treated_water:
          type: string
          nullable: true
          example: "7.3"
        location_1:
          type: string
          nullable: true
          example: "7.0"
        location_2:
          type: string
          nullable: true
          example: "7.1"
        location_3:
          type: string
          nullable: true
          example: "7.2"
        location_4:
          type: string
          nullable: true
          example: null
        location_5:
          type: string
          nullable: true
          example: null
        location_6:
          type: string
          nullable: true
          example: null
        approval_status:
          type: string
          enum: [pending, approved, rejected]
          example: "pending"
        approved_by_id:
          type: integer
          nullable: true
          example: null
        approval_date:
          type: string
          format: date-time
          nullable: true
          example: null
        approval_remarks:
          type: string
          nullable: true
          example: null
        reassigned_to:
          type: integer
          nullable: true
          example: null
        created_at:
          type: string
          format: date-time
          example: "2025-06-14T16:00:00.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-06-14T16:00:00.000000Z"
        job_detail:
          $ref: '#/components/schemas/JobAllocation'
        test_request_sample:
          $ref: '#/components/schemas/Sample'
        admin:
          $ref: '#/components/schemas/User'
        approved_by:
          $ref: '#/components/schemas/User'
          nullable: true

    CompletedTestsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            completed_tests:
              type: array
              items:
                $ref: '#/components/schemas/CompletedTest'
            pagination:
              $ref: '#/components/schemas/PaginationMeta'
            summary:
              type: object
              properties:
                total_completed:
                  type: integer
                pending_approval:
                  type: integer
                approved:
                  type: integer
                rejected:
                  type: integer

    CompletedTestResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            completed_test:
              $ref: '#/components/schemas/CompletedTest'

    # Quality Control schemas
    QCTestsResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            current_page:
              type: integer
            data:
              type: array
              items:
                $ref: '#/components/schemas/CompletedTest'

    CreateQCTestRequest:
      type: object
      required:
        - job_detail_id
        - test_request_sample_id
        - result
        - analysis_start_date
        - analysis_completion_date
        - analysis_submission_date
      properties:
        job_detail_id:
          type: integer
          example: 14
        test_request_sample_id:
          type: integer
          example: 25
        result:
          type: string
          example: "7.2"
        analysis_start_date:
          type: string
          format: date
          example: "2025-06-14"
        analysis_completion_date:
          type: string
          format: date
          example: "2025-06-14"
        analysis_submission_date:
          type: string
          format: date
          example: "2025-06-14"
        is_retest:
          type: boolean
          default: false
          example: false
        is_blind:
          type: boolean
          default: false
          example: false
        is_replicate:
          type: boolean
          default: false
          example: false
        original_test_id:
          type: integer
          example: 1

    QCTestResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            test:
              $ref: '#/components/schemas/CompletedTest'

    ApproveTestRequest:
      type: object
      required:
        - approval_status
      properties:
        approval_status:
          type: string
          enum: [approved, rejected]
          example: "approved"
        approval_remarks:
          type: string
          example: "Test results are within acceptable limits"
        reassigned_to:
          type: integer
          example: 5

    # Analyst schemas
    AnalystTestsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            my_tests:
              type: array
              items:
                $ref: '#/components/schemas/JobAllocation'

    SubmitTestResultRequest:
      type: object
      required:
        - result
        - analysis_start_date
        - analysis_completion_date
        - analysis_submission_date
      properties:
        result:
          type: string
          example: "7.2"
        analysis_start_date:
          type: string
          format: date
          example: "2025-06-14"
        analysis_completion_date:
          type: string
          format: date
          example: "2025-06-14"
        analysis_submission_date:
          type: string
          format: date
          example: "2025-06-14"
        raw_water:
          type: string
          example: "7.1"
        filtered_water:
          type: string
          example: "7.2"
        treated_water:
          type: string
          example: "7.3"
        location_1:
          type: string
          example: "7.0"
        location_2:
          type: string
          example: "7.1"
        location_3:
          type: string
          example: "7.2"
        location_4:
          type: string
          example: "7.0"
        location_5:
          type: string
          example: "7.1"
        location_6:
          type: string
          example: "7.2"

    TestResultResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            test_result:
              $ref: '#/components/schemas/CompletedTest'

    RejectTestRequest:
      type: object
      required:
        - rejection_reason
      properties:
        rejection_reason:
          type: string

    # Dashboard schemas
    DashboardStatsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            total_stats:
              type: object
              properties:
                total_test_requests:
                  type: integer
                total_job_allocations:
                  type: integer
                total_users:
                  type: integer
            monthly_stats:
              type: object
              properties:
                monthly_test_requests:
                  type: integer
                monthly_job_allocations:
                  type: integer
            task_stats:
              type: object
              properties:
                overdue_tasks:
                  type: integer
                today_tasks:
                  type: integer
                upcoming_tasks:
                  type: integer
            user_stats:
              type: object
              properties:
                my_jobs:
                  type: integer
                my_overdue_tasks:
                  type: integer

    # Customer schemas
    Customer:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "Green Tech Solutions"
        address:
          type: string
          example: "789 Tech Park, Bangalore"
        contact_person:
          type: string
          example: "Amit Patel"
        mobile_no:
          type: string
          example: "9123456789"
        email:
          type: string
          example: "<EMAIL>"
        created_at:
          type: string
          format: date-time
          example: "2025-01-15T08:30:00.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-01-15T08:30:00.000000Z"

    CreateCustomerRequest:
      type: object
      required:
        - name
        - address
        - contact_person
        - mobile_no
        - email
      properties:
        name:
          type: string
          example: "Blue Ocean Industries"
        address:
          type: string
          example: "321 Industrial Zone, Chennai"
        contact_person:
          type: string
          example: "Sunita Reddy"
        mobile_no:
          type: string
          example: "9876543210"
        email:
          type: string
          format: email
          example: "<EMAIL>"

    CustomersResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            customers:
              type: array
              items:
                $ref: '#/components/schemas/Customer'

    CustomerResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            customer:
              $ref: '#/components/schemas/Customer'
