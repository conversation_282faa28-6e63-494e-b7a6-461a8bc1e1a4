openapi: 3.0.3
info:
  title: LIMS API
  description: Laboratory Information Management System API
  version: 1.0.0
  contact:
    name: LIMS API Support
    url: https://managemylab.in
servers:
  - url: https://managemylab.in/public/api/v1
    description: Production server

security:
  - bearerAuth: []

paths:
  # Authentication endpoints
  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'

  /auth/profile:
    get:
      tags:
        - Authentication
      summary: Get user profile
      responses:
        '200':
          description: Profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileResponse'
    put:
      tags:
        - Authentication
      summary: Update user profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProfileRequest'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileResponse'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: User logout
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  # Test Requests endpoints
  /test-requests:
    get:
      tags:
        - Test Requests
      summary: Get test requests with pagination
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 15
        - name: is_allocated
          in: query
          schema:
            type: boolean
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Test requests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestRequestsResponse'
    post:
      tags:
        - Test Requests
      summary: Create new test request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTestRequestRequest'
      responses:
        '201':
          description: Test request created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestRequestResponse'

  /test-requests/{id}:
    get:
      tags:
        - Test Requests
      summary: Get test request details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Test request details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestRequestResponse'
    put:
      tags:
        - Test Requests
      summary: Update test request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTestRequestRequest'
      responses:
        '200':
          description: Test request updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestRequestResponse'
    delete:
      tags:
        - Test Requests
      summary: Delete test request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Test request deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /test-requests/{id}/samples:
    get:
      tags:
        - Test Requests
      summary: Get samples for test request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Samples retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SamplesResponse'
    post:
      tags:
        - Test Requests
      summary: Add sample to test request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSampleRequest'
      responses:
        '201':
          description: Sample added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SampleResponse'

  # Job Allocation endpoints
  /job-allocations:
    get:
      tags:
        - Job Allocation
      summary: Get job allocations
      responses:
        '200':
          description: Job allocations retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobAllocationsResponse'
    post:
      tags:
        - Job Allocation
      summary: Create job allocation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateJobAllocationRequest'
      responses:
        '201':
          description: Job allocation created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobAllocationResponse'

  /job-allocations/{id}:
    delete:
      tags:
        - Job Allocation
      summary: Delete job allocation
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Job allocation deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /job-allocations/export/excel:
    get:
      tags:
        - Job Allocation
      summary: Export job allocations to Excel
      responses:
        '200':
          description: Export data prepared
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExportResponse'

  /job-allocations/{id}/pdf:
    get:
      tags:
        - Job Allocation
      summary: Generate PDF for job allocation
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: PDF generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PDFResponse'

  # Completed Tests endpoints
  /completed-tests:
    get:
      tags:
        - Completed Tests
      summary: Get completed tests
      responses:
        '200':
          description: Completed tests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompletedTestsResponse'

  /completed-tests/{id}:
    get:
      tags:
        - Completed Tests
      summary: Get completed test details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Completed test details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompletedTestResponse'

  # Quality Control endpoints
  /quality-control/tests:
    get:
      tags:
        - Quality Control
      summary: Get quality control tests
      responses:
        '200':
          description: QC tests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QCTestsResponse'
    post:
      tags:
        - Quality Control
      summary: Create quality control test
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQCTestRequest'
      responses:
        '201':
          description: QC test created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QCTestResponse'

  /quality-control/tests/{id}/approve:
    put:
      tags:
        - Quality Control
      summary: Approve or reject QC test
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApproveTestRequest'
      responses:
        '200':
          description: Test approval status updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QCTestResponse'

  # Analyst endpoints
  /analyst/my-tests:
    get:
      tags:
        - Analyst
      summary: Get analyst's assigned tests
      responses:
        '200':
          description: Analyst tests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalystTestsResponse'

  /analyst/tests/{id}/submit:
    post:
      tags:
        - Analyst
      summary: Submit test result
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitTestResultRequest'
      responses:
        '200':
          description: Test result submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestResultResponse'

  /analyst/tests/{id}/reject:
    post:
      tags:
        - Analyst
      summary: Reject test
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RejectTestRequest'
      responses:
        '200':
          description: Test rejected successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  # Dashboard endpoints
  /dashboard/stats:
    get:
      tags:
        - Dashboard
      summary: Get dashboard statistics
      responses:
        '200':
          description: Statistics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardStatsResponse'

  # Masters endpoints
  /masters/customers:
    get:
      tags:
        - Masters
      summary: Get customers
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomersResponse'
    post:
      tags:
        - Masters
      summary: Add customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'

  /masters/parameters:
    get:
      tags:
        - Masters
      summary: Get parameters
      responses:
        '200':
          description: Parameters retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParametersResponse'
    post:
      tags:
        - Masters
      summary: Add parameter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateParameterRequest'
      responses:
        '201':
          description: Parameter created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParameterResponse'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # Common schemas
    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string

    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
        total_pages:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        has_more:
          type: boolean

    # Authentication schemas
    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
        password:
          type: string

    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            token:
              type: string
            profile:
              $ref: '#/components/schemas/User'

    ProfileResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            profile:
              $ref: '#/components/schemas/User'

    UpdateProfileRequest:
      type: object
      properties:
        name:
          type: string
        email:
          type: string

    User:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        email:
          type: string
        username:
          type: string
        email_verified_at:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    # Test Request schemas
    TestRequest:
      type: object
      properties:
        id:
          type: integer
        request_number:
          type: string
        request_date:
          type: string
          format: date-time
        customer_name:
          type: string
        customer_address:
          type: string
        contact_person:
          type: string
        mobile_no:
          type: string
        email:
          type: string
        special_request:
          type: string
        submitted_by_name:
          type: string
        submitted_by_designation:
          type: string
        submitted_by_date:
          type: string
          format: date-time
        submitted_by_id_proof:
          type: string
        received_by_name:
          type: string
        received_by_designation:
          type: string
        received_by_date:
          type: string
          format: date-time
        received_by_id_proof:
          type: string
        sample_received_time:
          type: string
          format: date-time
          nullable: true
        sample_collection_time:
          type: string
          format: date-time
        quantity_of_sample:
          type: string
        type_of_sample:
          type: string
          enum: [Solid, Liquid, Gas]
        sample_details:
          type: string
        sample_code:
          type: string
        deleted:
          type: integer
        is_allocated:
          type: boolean
        samples:
          type: array
          items:
            $ref: '#/components/schemas/Sample'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateTestRequestRequest:
      type: object
      required:
        - request_number
        - request_date
        - customer_name
        - customer_address
        - contact_person
        - mobile_no
        - email
      properties:
        request_number:
          type: string
        request_date:
          type: string
          format: date
        customer_name:
          type: string
        customer_address:
          type: string
        contact_person:
          type: string
        mobile_no:
          type: string
        email:
          type: string
        special_request:
          type: string
        submitted_by_name:
          type: string
        submitted_by_designation:
          type: string
        submitted_by_date:
          type: string
          format: date
        submitted_by_id_proof:
          type: string
        received_by_name:
          type: string
        received_by_designation:
          type: string
        received_by_date:
          type: string
          format: date
        received_by_id_proof:
          type: string
        sample_received_time:
          type: string
          format: date-time
        sample_collection_time:
          type: string
          format: date-time
        quantity_of_sample:
          type: string
        type_of_sample:
          type: string
          enum: [Solid, Liquid, Gas]
        sample_details:
          type: string
        sample_code:
          type: string
        samples:
          type: array
          items:
            $ref: '#/components/schemas/CreateSampleRequest'

    UpdateTestRequestRequest:
      type: object
      properties:
        customer_name:
          type: string
        customer_address:
          type: string
        contact_person:
          type: string
        mobile_no:
          type: string
        email:
          type: string
        special_request:
          type: string
        sample_details:
          type: string

    TestRequestsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            test_requests:
              type: array
              items:
                $ref: '#/components/schemas/TestRequest'
            pagination:
              $ref: '#/components/schemas/PaginationMeta'

    TestRequestResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            test_request:
              $ref: '#/components/schemas/TestRequest'

    # Sample schemas
    Sample:
      type: object
      properties:
        id:
          type: integer
        test_request_id:
          type: integer
        parameter_id:
          type: integer
        particulars:
          type: string
        type:
          type: string
        quantity:
          type: string
        remarks:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        parameter:
          $ref: '#/components/schemas/Parameter'

    CreateSampleRequest:
      type: object
      required:
        - parameter_id
        - particulars
        - type
        - quantity
      properties:
        parameter_id:
          type: integer
        particulars:
          type: string
        type:
          type: string
        quantity:
          type: string
        remarks:
          type: string

    SamplesResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            samples:
              type: array
              items:
                $ref: '#/components/schemas/Sample'

    SampleResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            sample:
              $ref: '#/components/schemas/Sample'

    # Parameter schemas
    Parameter:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        type:
          type: string
          nullable: true
        status:
          type: integer
        requirement:
          type: string
        permissible_limit:
          type: string
        protocol_used:
          type: string
        units:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateParameterRequest:
      type: object
      required:
        - name
        - requirement
        - permissible_limit
        - protocol_used
      properties:
        name:
          type: string
        type:
          type: string
        requirement:
          type: string
        permissible_limit:
          type: string
        protocol_used:
          type: string
        units:
          type: string
        status:
          type: integer
          default: 1

    ParametersResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            parameters:
              type: array
              items:
                $ref: '#/components/schemas/Parameter'

    ParameterResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            parameter:
              $ref: '#/components/schemas/Parameter'

    # Job Allocation schemas
    JobAllocation:
      type: object
      properties:
        id:
          type: integer
        serial_no:
          type: string
        creation_date:
          type: string
          format: date-time
        code_number:
          type: string
        nature:
          type: string
        quantity:
          type: string
        collection_date:
          type: string
          format: date
        submission_date:
          type: string
          format: date
        due_date:
          type: string
          format: date
        user_id:
          type: integer
        test_request_id:
          type: integer
        report_type:
          type: string
          enum: [water, standard, location]
        designation:
          type: string
        remarks:
          type: string
        nabl_status:
          type: string
          enum: [yes, no]
        deleted:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        test_request:
          $ref: '#/components/schemas/TestRequest'
        user:
          $ref: '#/components/schemas/User'
        parameter_allocations:
          type: array
          items:
            $ref: '#/components/schemas/ParameterAllocation'

    ParameterAllocation:
      type: object
      properties:
        id:
          type: integer
        job_detail_id:
          type: integer
        sample_id:
          type: integer
        analyst_id:
          type: integer
        is_retest:
          type: integer
        is_blind:
          type: integer
        is_replicate:
          type: integer
        is_spiked:
          type: integer
        spiked_result:
          type: string
          nullable: true
        original_allocation_id:
          type: integer
          nullable: true
        status:
          type: string
          nullable: true
        rejection_reason:
          type: string
          nullable: true
        rejected_at:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        sample:
          $ref: '#/components/schemas/Sample'
        analyst:
          $ref: '#/components/schemas/User'
        approved_by:
          $ref: '#/components/schemas/User'
          nullable: true

    CreateJobAllocationRequest:
      type: object
      required:
        - test_request_id
        - creation_date
        - code_number
        - nature
        - quantity
        - collection_date
        - submission_date
        - due_date
        - user_id
        - designation
        - nabl_status
        - report_type
        - parameter_allocations
      properties:
        test_request_id:
          type: integer
        creation_date:
          type: string
          format: date
        code_number:
          type: string
        nature:
          type: string
        quantity:
          type: string
        collection_date:
          type: string
          format: date
        submission_date:
          type: string
          format: date
        due_date:
          type: string
          format: date
        user_id:
          type: integer
        designation:
          type: string
        remarks:
          type: string
        nabl_status:
          type: string
          enum: [yes, no]
        report_type:
          type: string
          enum: [water, standard, location]
        password:
          type: string
        parameter_allocations:
          type: array
          items:
            type: object
            properties:
              sample_id:
                type: integer
              analyst_id:
                type: integer

    JobAllocationsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            job_allocations:
              type: array
              items:
                $ref: '#/components/schemas/JobAllocation'
            pagination:
              $ref: '#/components/schemas/PaginationMeta'

    JobAllocationResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            job_allocation:
              $ref: '#/components/schemas/JobAllocation'

    ExportResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            job_allocations:
              type: array
              items:
                $ref: '#/components/schemas/JobAllocation'
        export_url:
          type: string

    PDFResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            export_data:
              type: array
              items:
                type: object
            total_records:
              type: integer
            filename:
              type: string

    # Completed Tests schemas
    CompletedTest:
      type: object
      properties:
        id:
          type: integer
        job_detail_id:
          type: integer
        test_request_sample_id:
          type: integer
        user_id:
          type: integer
        result:
          type: string
        analysis_start_date:
          type: string
          format: date-time
        analysis_completion_date:
          type: string
          format: date-time
        analysis_submission_date:
          type: string
          format: date-time
        is_retest:
          type: integer
        is_blind:
          type: integer
        is_replicate:
          type: integer
        original_test_id:
          type: integer
          nullable: true
        raw_water:
          type: string
          nullable: true
        filtered_water:
          type: string
          nullable: true
        treated_water:
          type: string
          nullable: true
        location_1:
          type: string
          nullable: true
        location_2:
          type: string
          nullable: true
        location_3:
          type: string
          nullable: true
        location_4:
          type: string
          nullable: true
        location_5:
          type: string
          nullable: true
        location_6:
          type: string
          nullable: true
        approval_status:
          type: string
          enum: [pending, approved, rejected]
        approved_by_id:
          type: integer
          nullable: true
        approval_date:
          type: string
          format: date-time
          nullable: true
        approval_remarks:
          type: string
          nullable: true
        reassigned_to:
          type: integer
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        job_detail:
          $ref: '#/components/schemas/JobAllocation'
        test_request_sample:
          $ref: '#/components/schemas/Sample'
        admin:
          $ref: '#/components/schemas/User'
        approved_by:
          $ref: '#/components/schemas/User'
          nullable: true

    CompletedTestsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            completed_tests:
              type: array
              items:
                $ref: '#/components/schemas/CompletedTest'
            pagination:
              $ref: '#/components/schemas/PaginationMeta'
            summary:
              type: object
              properties:
                total_completed:
                  type: integer
                pending_approval:
                  type: integer
                approved:
                  type: integer
                rejected:
                  type: integer

    CompletedTestResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            completed_test:
              $ref: '#/components/schemas/CompletedTest'

    # Quality Control schemas
    QCTestsResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            current_page:
              type: integer
            data:
              type: array
              items:
                $ref: '#/components/schemas/CompletedTest'

    CreateQCTestRequest:
      type: object
      required:
        - job_detail_id
        - test_request_sample_id
        - result
        - analysis_start_date
        - analysis_completion_date
        - analysis_submission_date
      properties:
        job_detail_id:
          type: integer
        test_request_sample_id:
          type: integer
        result:
          type: string
        analysis_start_date:
          type: string
          format: date
        analysis_completion_date:
          type: string
          format: date
        analysis_submission_date:
          type: string
          format: date
        is_retest:
          type: boolean
          default: false
        is_blind:
          type: boolean
          default: false
        is_replicate:
          type: boolean
          default: false
        original_test_id:
          type: integer

    QCTestResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            test:
              $ref: '#/components/schemas/CompletedTest'

    ApproveTestRequest:
      type: object
      required:
        - approval_status
      properties:
        approval_status:
          type: string
          enum: [approved, rejected]
        approval_remarks:
          type: string
        reassigned_to:
          type: integer

    # Analyst schemas
    AnalystTestsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            my_tests:
              type: array
              items:
                $ref: '#/components/schemas/JobAllocation'

    SubmitTestResultRequest:
      type: object
      required:
        - result
        - analysis_start_date
        - analysis_completion_date
        - analysis_submission_date
      properties:
        result:
          type: string
        analysis_start_date:
          type: string
          format: date
        analysis_completion_date:
          type: string
          format: date
        analysis_submission_date:
          type: string
          format: date
        raw_water:
          type: string
        filtered_water:
          type: string
        treated_water:
          type: string
        location_1:
          type: string
        location_2:
          type: string
        location_3:
          type: string
        location_4:
          type: string
        location_5:
          type: string
        location_6:
          type: string

    TestResultResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            test_result:
              $ref: '#/components/schemas/CompletedTest'

    RejectTestRequest:
      type: object
      required:
        - rejection_reason
      properties:
        rejection_reason:
          type: string

    # Dashboard schemas
    DashboardStatsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            total_stats:
              type: object
              properties:
                total_test_requests:
                  type: integer
                total_job_allocations:
                  type: integer
                total_users:
                  type: integer
            monthly_stats:
              type: object
              properties:
                monthly_test_requests:
                  type: integer
                monthly_job_allocations:
                  type: integer
            task_stats:
              type: object
              properties:
                overdue_tasks:
                  type: integer
                today_tasks:
                  type: integer
                upcoming_tasks:
                  type: integer
            user_stats:
              type: object
              properties:
                my_jobs:
                  type: integer
                my_overdue_tasks:
                  type: integer

    # Customer schemas
    Customer:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        address:
          type: string
        contact_person:
          type: string
        mobile_no:
          type: string
        email:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateCustomerRequest:
      type: object
      required:
        - name
        - address
        - contact_person
        - mobile_no
        - email
      properties:
        name:
          type: string
        address:
          type: string
        contact_person:
          type: string
        mobile_no:
          type: string
        email:
          type: string
          format: email

    CustomersResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            customers:
              type: array
              items:
                $ref: '#/components/schemas/Customer'

    CustomerResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            customer:
              $ref: '#/components/schemas/Customer'
