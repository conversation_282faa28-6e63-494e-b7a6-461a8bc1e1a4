# Job Assignment with Analysts - Usage Guide

This guide shows how to use the `getAnalysts()` method from `JobAllocationRepositoryImpl` to assign jobs to analysts.

## ✅ **Implementation Complete**

### **🔧 What's Been Set Up:**

1. **✅ Use Case Created**: `GetAnalystsUseCase`
2. **✅ BLoC Integration**: Added to `JobAllocationBloc`
3. **✅ Events & States**: `AnalystsLoadRequested`, `AnalystsLoaded`, etc.
4. **✅ UI Component**: `AnalystAssignmentDialog`
5. **✅ Dependency Injection**: Registered in DI container

---

## **🚀 How to Use in Your Job Allocation Screen**

### **1. Import the Dialog**
```dart
import '../widgets/analyst_assignment_dialog.dart';
```

### **2. Add Assignment Button to Job Card**
```dart
// In your job card widget
ElevatedButton.icon(
  onPressed: () async {
    final selectedAnalyst = await context.showAnalystAssignmentDialog(
      jobId: job.id,
      jobTitle: job.title,
    );
    
    if (selectedAnalyst != null) {
      // Assignment was successful
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Job assigned to ${selectedAnalyst['name']}'),
          backgroundColor: Colors.green,
        ),
      );
    }
  },
  icon: const Icon(Icons.person_add),
  label: const Text('Assign Analyst'),
)
```

### **3. Listen for Assignment Results**
```dart
// In your job allocation screen
BlocListener<JobAllocationBloc, JobAllocationState>(
  listener: (context, state) {
    if (state is JobAllocationAssigned) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Job assigned successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      // Refresh the job list
      context.read<JobAllocationBloc>().add(
        const JobAllocationsRefreshRequested(),
      );
    }
    
    if (state is AnalystsError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load analysts: ${state.failure.message}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  },
  child: YourJobListWidget(),
)
```

---

## **📋 API Response Format**

The `getAnalysts()` method returns a list of analyst objects with this structure:

```json
{
  "success": true,
  "data": {
    "analysts": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "department": "Chemistry",
        "specialization": "Organic Analysis",
        "status": "active",
        "workload": 5
      },
      {
        "id": 2,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "department": "Microbiology",
        "specialization": "Water Testing",
        "status": "active",
        "workload": 3
      }
    ]
  }
}
```

---

## **🎯 Complete Workflow**

### **Step 1: User Clicks "Assign Analyst"**
- Dialog opens
- `AnalystsLoadRequested` event is triggered
- BLoC calls `GetAnalystsUseCase`
- Use case calls `JobAllocationRepository.getAnalysts()`

### **Step 2: Analysts Are Loaded**
- Repository calls `JobAllocationRemoteDataSource.getAnalysts()`
- Data source makes API call to `/api/v1/job-allocations/analysts`
- Response is parsed and returned as `List<Map<String, dynamic>>`

### **Step 3: User Selects Analyst**
- Dialog shows list of available analysts
- User selects one and clicks "Assign"
- `JobAllocationAssignRequested` event is triggered

### **Step 4: Assignment Complete**
- Job is assigned to selected analyst
- UI is updated to reflect the assignment
- Success message is shown

---

## **🔧 Customization Options**

### **Filter Analysts by Specialization**
```dart
// You can extend the use case to accept filters
class GetAnalystsUseCase implements UseCase<List<Map<String, dynamic>>, GetAnalystsParams> {
  // Add filtering logic
}

class GetAnalystsParams {
  final String? specialization;
  final String? department;
  final bool? availableOnly;
  
  const GetAnalystsParams({
    this.specialization,
    this.department,
    this.availableOnly,
  });
}
```

### **Show Analyst Workload**
```dart
// In the dialog, show current workload
subtitle: Column(
  crossAxisAlignment: CrossAxisAlignment.start,
  children: [
    Text(analyst['email'] ?? ''),
    Text(
      'Current workload: ${analyst['workload'] ?? 0} jobs',
      style: Theme.of(context).textTheme.bodySmall,
    ),
  ],
),
```

### **Bulk Assignment**
```dart
// For assigning multiple jobs to one analyst
class BulkJobAssignmentDialog extends StatefulWidget {
  final List<int> jobIds;
  // Implementation similar to single assignment
}
```

---

## **✅ Ready to Use!**

The job assignment functionality is now fully implemented and ready to use. Simply:

1. **Import the dialog** in your job allocation screen
2. **Add assignment buttons** to your job cards
3. **Handle the assignment results** with BLoC listeners

The `getAnalysts()` method from `JobAllocationRepositoryImpl` will automatically fetch the latest list of available analysts from your API! 🚀
