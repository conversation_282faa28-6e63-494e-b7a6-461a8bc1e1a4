# LIMS Flutter App

A comprehensive Lab Management Information System (LIMS) built with Flutter following Clean Architecture principles.

## 🏗️ Architecture

This project implements Clean Architecture with the following layers:
- **Presentation Layer**: UI components with BLoC state management
- **Domain Layer**: Business logic and use cases
- **Data Layer**: Repository implementations and data sources

## 📱 Features

### User Roles
- **Front Desk**: View/Create tests, Download reports
- **Chief Chemist**: Job allocation, Approvals, Quality control (retest/blind/replicate)
- **Analyst**: Fill results, Reject jobs
- **Master**: System administration

### Core Functionality
- ✅ Authentication with role-based access
- ✅ Test request management
- ✅ Job allocation and tracking
- ✅ Quality control workflows
- ✅ Report generation
- ✅ Notifications system

## 🚀 State Management

The app uses **BLoC (Business Logic Component)** pattern for state management:
- Reactive programming with streams
- Separation of business logic from UI
- Testable and maintainable code structure

## 📚 Documentation

### Architecture & Implementation
- [Clean Architecture Overview](docs/README_CLEAN_ARCHITECTURE.md)
- [Use Cases Documentation](docs/USE_CASES_DOCUMENTATION.md)
- [Authentication BLoC Implementation](docs/AUTHENTICATION_BLOC_IMPLEMENTATION.md)

### API Documentation
- [Postman Collection](docs/LIMS.postman_collection.json)

## 🛠️ Getting Started

### Prerequisites
- Flutter SDK (>=3.8.1)
- Dart SDK
- Android Studio / VS Code
- Git

### Installation
1. Clone the repository
```bash
git clone <repository-url>
cd lims_app_flutter
```

2. Install dependencies
```bash
flutter pub get
```

3. Generate code (for JSON serialization)
```bash
flutter packages pub run build_runner build
```

4. Run the app
```bash
flutter run
```

## 🧪 Testing

Run tests with:
```bash
flutter test
```

## 📦 Dependencies

### Core Dependencies
- `flutter_bloc` - State management
- `dio` - HTTP client
- `dartz` - Functional programming
- `get_it` - Dependency injection
- `go_router` - Navigation
- `shared_preferences` - Local storage

### Development Dependencies
- `bloc_test` - BLoC testing utilities
- `mockito` - Mocking framework
- `build_runner` - Code generation

## 🏃‍♂️ Development Workflow

1. **Data Layer**: Implement models, data sources, and repositories
2. **Domain Layer**: Define entities, repositories interfaces, and use cases
3. **Presentation Layer**: Create UI screens and widgets
4. **State Management**: Implement BLoC for reactive state management
5. **Testing**: Write unit, widget, and integration tests

## 📋 Project Status

- ✅ Clean Architecture Setup
- ✅ Data Layer Implementation
- ✅ Domain Layer Implementation
- ✅ UI Layer Implementation
- ✅ Authentication BLoC Implementation
- 🔄 Additional Module BLoCs (In Progress)
- ⏳ API Integration Testing
- ⏳ End-to-End Testing

## 🤝 Contributing

1. Follow Clean Architecture principles
2. Write tests for new features
3. Use BLoC pattern for state management
4. Follow the established code style
5. Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
