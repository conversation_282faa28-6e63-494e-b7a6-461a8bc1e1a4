import 'package:flutter_test/flutter_test.dart';
import 'package:lims_app_flutter/core/error/failures.dart';

void main() {
  group('Failure Abstract Class', () {
    test('should access message field from abstract Failure class', () {
      // Arrange
      const testMessage = 'Test error message';
      
      // Act & Assert - Test different failure types
      const serverFailure = ServerFailure(message: testMessage);
      const networkFailure = NetworkFailure(message: testMessage);
      const cacheFailure = CacheFailure(message: testMessage);
      const unauthorizedFailure = UnauthorizedFailure(message: testMessage);
      const generalFailure = GeneralFailure(message: testMessage);
      const validationFailure = ValidationFailure(message: testMessage);

      // All should be accessible through the abstract Failure class
      final List<Failure> failures = [
        serverFailure,
        networkFailure,
        cacheFailure,
        unauthorizedFailure,
        generalFailure,
        validationFailure,
      ];

      // Test that message can be accessed polymorphically
      for (final failure in failures) {
        expect(failure.message, equals(testMessage));
      }
    });

    test('should maintain specific properties in concrete classes', () {
      // Arrange & Act
      const serverFailure = ServerFailure(
        message: 'Server error',
        statusCode: 500,
      );
      
      const validationFailure = ValidationFailure(
        message: 'Validation error',
        fieldErrors: {
          'username': ['Username is required'],
          'password': ['Password is too short'],
        },
      );
      
      const generalFailure = GeneralFailure(
        message: 'General error',
        details: 'Additional error details',
      );

      // Assert
      expect(serverFailure.message, equals('Server error'));
      expect(serverFailure.statusCode, equals(500));
      
      expect(validationFailure.message, equals('Validation error'));
      expect(validationFailure.fieldErrors, isNotNull);
      expect(validationFailure.getFirstFieldError('username'), equals('Username is required'));
      
      expect(generalFailure.message, equals('General error'));
      expect(generalFailure.details, equals('Additional error details'));
    });

    test('should work with polymorphic error handling', () {
      // Arrange
      final List<Failure> mixedFailures = [
        const ServerFailure(message: 'Server error', statusCode: 500),
        const NetworkFailure(message: 'Network error'),
        const ValidationFailure(
          message: 'Validation error',
          fieldErrors: {'email': ['Invalid email']},
        ),
      ];

      // Act & Assert
      for (final failure in mixedFailures) {
        // This should work for all failure types now
        expect(failure.message, isNotEmpty);
        expect(failure.message, isA<String>());
      }
    });

    test('should maintain equality based on message and other properties', () {
      // Arrange
      const failure1 = ServerFailure(message: 'Error', statusCode: 500);
      const failure2 = ServerFailure(message: 'Error', statusCode: 500);
      const failure3 = ServerFailure(message: 'Different Error', statusCode: 500);
      const failure4 = ServerFailure(message: 'Error', statusCode: 404);

      // Assert
      expect(failure1, equals(failure2)); // Same message and statusCode
      expect(failure1, isNot(equals(failure3))); // Different message
      expect(failure1, isNot(equals(failure4))); // Different statusCode
    });
  });

  group('ValidationFailure', () {
    test('should provide field-specific error methods', () {
      // Arrange
      const failure = ValidationFailure(
        message: 'Validation failed',
        fieldErrors: {
          'username': ['Username is required', 'Username must be unique'],
          'password': ['Password is too short'],
          'email': ['Invalid email format'],
        },
      );

      // Assert
      expect(failure.message, equals('Validation failed'));
      expect(failure.getFirstFieldError('username'), equals('Username is required'));
      expect(failure.getFieldErrors('username'), hasLength(2));
      expect(failure.hasFieldError('password'), isTrue);
      expect(failure.hasFieldError('nonexistent'), isFalse);
      expect(failure.errorFields, containsAll(['username', 'password', 'email']));
    });
  });
}
