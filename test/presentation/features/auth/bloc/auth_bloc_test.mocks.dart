// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in lims_app_flutter/test/presentation/features/auth/bloc/auth_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:dartz/dartz.dart' as _i3;
import 'package:lims_app_flutter/core/error/failures.dart' as _i6;
import 'package:lims_app_flutter/core/usecases/usecase.dart' as _i9;
import 'package:lims_app_flutter/domain/entities/auth/login_entity.dart' as _i7;
import 'package:lims_app_flutter/domain/entities/auth/user_entity.dart' as _i11;
import 'package:lims_app_flutter/domain/repositories/auth_repository.dart'
    as _i2;
import 'package:lims_app_flutter/domain/usecases/auth/get_profile_usecase.dart'
    as _i10;
import 'package:lims_app_flutter/domain/usecases/auth/login_and_get_profile_usecase.dart'
    as _i13;
import 'package:lims_app_flutter/domain/usecases/auth/login_usecase.dart'
    as _i4;
import 'package:lims_app_flutter/domain/usecases/auth/logout_usecase.dart'
    as _i8;
import 'package:lims_app_flutter/domain/usecases/auth/update_profile_usecase.dart'
    as _i12;
import 'package:mockito/mockito.dart' as _i1;
import 'package:shared_preferences/src/shared_preferences_legacy.dart' as _i14;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthRepository_0 extends _i1.SmartFake
    implements _i2.AuthRepository {
  _FakeAuthRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_1<L, R> extends _i1.SmartFake implements _i3.Either<L, R> {
  _FakeEither_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LoginUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginUseCase extends _i1.Mock implements _i4.LoginUseCase {
  MockLoginUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeAuthRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i7.LoginEntity>> call(
    _i4.LoginParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i5.Future<_i3.Either<_i6.Failure, _i7.LoginEntity>>.value(
                  _FakeEither_1<_i6.Failure, _i7.LoginEntity>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, _i7.LoginEntity>>);
}

/// A class which mocks [LogoutUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogoutUseCase extends _i1.Mock implements _i8.LogoutUseCase {
  MockLogoutUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeAuthRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, void>> call(_i9.NoParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, void>>.value(
              _FakeEither_1<_i6.Failure, void>(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, void>>);
}

/// A class which mocks [GetProfileUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetProfileUseCase extends _i1.Mock implements _i10.GetProfileUseCase {
  MockGetProfileUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeAuthRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i11.UserEntity>> call(
    _i9.NoParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i5.Future<_i3.Either<_i6.Failure, _i11.UserEntity>>.value(
                  _FakeEither_1<_i6.Failure, _i11.UserEntity>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, _i11.UserEntity>>);
}

/// A class which mocks [UpdateProfileUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockUpdateProfileUseCase extends _i1.Mock
    implements _i12.UpdateProfileUseCase {
  MockUpdateProfileUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeAuthRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i11.UserEntity>> call(
    _i12.UpdateProfileParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i5.Future<_i3.Either<_i6.Failure, _i11.UserEntity>>.value(
                  _FakeEither_1<_i6.Failure, _i11.UserEntity>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, _i11.UserEntity>>);
}

/// A class which mocks [LoginAndGetProfileUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginAndGetProfileUseCase extends _i1.Mock
    implements _i13.LoginAndGetProfileUseCase {
  MockLoginAndGetProfileUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeAuthRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i13.LoginAndGetProfileResult>> call(
    _i13.LoginAndGetProfileParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i5.Future<
                  _i3.Either<_i6.Failure, _i13.LoginAndGetProfileResult>
                >.value(
                  _FakeEither_1<_i6.Failure, _i13.LoginAndGetProfileResult>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i5.Future<
            _i3.Either<_i6.Failure, _i13.LoginAndGetProfileResult>
          >);
}

/// A class which mocks [SharedPreferences].
///
/// See the documentation for Mockito's code generation for more information.
class MockSharedPreferences extends _i1.Mock implements _i14.SharedPreferences {
  MockSharedPreferences() {
    _i1.throwOnMissingStub(this);
  }

  @override
  Set<String> getKeys() =>
      (super.noSuchMethod(
            Invocation.method(#getKeys, []),
            returnValue: <String>{},
          )
          as Set<String>);

  @override
  Object? get(String? key) =>
      (super.noSuchMethod(Invocation.method(#get, [key])) as Object?);

  @override
  bool? getBool(String? key) =>
      (super.noSuchMethod(Invocation.method(#getBool, [key])) as bool?);

  @override
  int? getInt(String? key) =>
      (super.noSuchMethod(Invocation.method(#getInt, [key])) as int?);

  @override
  double? getDouble(String? key) =>
      (super.noSuchMethod(Invocation.method(#getDouble, [key])) as double?);

  @override
  String? getString(String? key) =>
      (super.noSuchMethod(Invocation.method(#getString, [key])) as String?);

  @override
  bool containsKey(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#containsKey, [key]),
            returnValue: false,
          )
          as bool);

  @override
  List<String>? getStringList(String? key) =>
      (super.noSuchMethod(Invocation.method(#getStringList, [key]))
          as List<String>?);

  @override
  _i5.Future<bool> setBool(String? key, bool? value) =>
      (super.noSuchMethod(
            Invocation.method(#setBool, [key, value]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> setInt(String? key, int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setInt, [key, value]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> setDouble(String? key, double? value) =>
      (super.noSuchMethod(
            Invocation.method(#setDouble, [key, value]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> setString(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#setString, [key, value]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> setStringList(String? key, List<String>? value) =>
      (super.noSuchMethod(
            Invocation.method(#setStringList, [key, value]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> remove(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#remove, [key]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> commit() =>
      (super.noSuchMethod(
            Invocation.method(#commit, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<bool> clear() =>
      (super.noSuchMethod(
            Invocation.method(#clear, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> reload() =>
      (super.noSuchMethod(
            Invocation.method(#reload, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}
