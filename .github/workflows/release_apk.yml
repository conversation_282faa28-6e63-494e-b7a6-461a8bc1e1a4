name: Build Release APK

on:
  push:
    branches:
      - 'release/**'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.32.2'
          channel: 'stable'
          
      - name: Install dependencies
        run: flutter pub get

      - name: Get version from pubspec.yaml
        id: get_version
        run: |
          VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //' | sed 's/+.*//')
          echo "version=$VERSION" >> $GITHUB_OUTPUT
        
      - name: Build APK
        run: flutter build apk --release
        
      - name: Upload APK
        uses: actions/upload-artifact@v4
        with:
          name: "LIMS app V${{ steps.get_version.outputs.version }}.apk"
          path: build/app/outputs/flutter-apk/app-release.apk 
